var ye=Object.defineProperty;var Yt=c=>{throw TypeError(c)};var Se=(c,t,e)=>t in c?ye(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e;var r=(c,t,e)=>Se(c,typeof t!="symbol"?t+"":t,e),Dt=(c,t,e)=>t.has(c)||Yt("Cannot "+e);var i=(c,t,e)=>(Dt(c,t,"read from private field"),e?e.call(c):t.get(c)),p=(c,t,e)=>t.has(c)?Yt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(c):t.set(c,e),f=(c,t,e,s)=>(Dt(c,t,"write to private field"),s?s.call(c,e):t.set(c,e),e),u=(c,t,e)=>(Dt(c,t,"access private method"),e);var Ft=(c,t,e,s)=>({set _(o){f(c,t,o,e)},get _(){return i(c,t,s)}});import{h as ue,S as be,W as rt,e as we,i as Me}from"./IconButtonAugment-DVt24OaC.js";import{d as xe}from"./CardAugment-CB88N7dm.js";import{w as Q,l as fe,f as Mt,a as ge,t as Kt,b as dt,aA as Pt,A as Te,C as Rt,D as Fe,J as me,Q as Qt,W as Ae,_ as Ie,ab as Ee,I as ke}from"./SpinnerAugment-AffdR7--.js";import{j as St,S as It,c as ft,k as _e,l as Xt,C as Oe,R as Le,E as Ue,m as ot,n as De,o as Pe,p as Re,s as Gt,N as at,q as zt,r as Ge,t as ze,u as te,v as He,w as ee,x as se,y as Ne,z as ie,B as We,G as ne,H as I,I as Ve,J as oe,K as je,L as Ht}from"./index-CnLsnTY6.js";import{M as qe,C as Be,a as $e}from"./message-broker-Bv_1VsFe.js";import{i as Ze}from"./focusTrapStack-CB_5BS9R.js";class Ke{constructor(t=!0,e=setTimeout){r(this,"_notify",new Set);r(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});r(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});r(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});r(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,o){if(!t&&!o)return()=>{};const n={timeout:t,notify:e,once:s,date:o};return this._notify.add(n),this._schedule(n),()=>{this._clearTimeout(n),this._notify.delete(n)}}}class Je{constructor(t=0,e=0,s=new Ke,o=Q("busy"),n=Q(!1)){r(this,"unsubNotify");r(this,"unsubMessage");r(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});r(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=o,this.focusAfterIdle=n,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var Ye=Mt("<svg><!></svg>"),Qe=Mt("<svg><!></svg>");function Xe(c,t){const e=fe(t,["children","$$slots","$$events","$$legacy"]);var s=Qe();ge(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var o=Kt(s);ue(o,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',!0),dt(c,s)}var Et=(c=>(c.send="send",c.addTask="addTask",c))(Et||{});const ts={id:"send",label:"Send to Agent",icon:function(c,t){const e=fe(t,["children","$$slots","$$events","$$legacy"]);var s=Ye();ge(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var o=Kt(s);ue(o,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3L266 249.3c3.4.4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6c-9.4 1.2-17.6 6.9-22 15.3l-63 121.2c-17.4 33.5 17 70.2 51.6 55.1l435.2-190.9c25.5-11.2 25.5-47.4 0-58.6z"/>',!0),dt(c,s)},description:"Send message to agent"},bs=[ts,{id:"addTask",label:"Add Task",icon:Xe,description:"Add task with the message content"}];class es{constructor(){r(this,"_mode",Q(Et.send));r(this,"_currentMode",Et.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(Et).includes(t)&&this._mode.set(t)}}class ss{constructor(t){this.dependencies=t}_getChatMessagePayload(){const t=Pt(this.dependencies.chatModeType),e=Pt(this.dependencies.agentExecutionMode),s={chatMode:t,agentExecutionMode:t==="localAgent"?e:void 0};return t==="localAgent"&&(s.sendMode=Pt(this.dependencies.currentSendMode)),s}trackEvent(t,e){const s={...this._getChatMessagePayload(),...e};this.trackSimpleEvent(t,s)}trackSimpleEvent(t,e){this.dependencies.extensionClient.trackEventWithTypes(t,e)}}class is{constructor(t=3e5){r(this,"_cleanItems",new Set);r(this,"_lastProcessedTime",new Map);this.cooldownMs=t}markClean(t){this._cleanItems.add(t),this._lastProcessedTime.set(t,Date.now())}markDirty(t){this._cleanItems.delete(t),this._lastProcessedTime.delete(t)}isClean(t){return this._cleanItems.has(t)}isWithinCooldown(t){const e=this._lastProcessedTime.get(t);return!!e&&Date.now()-e<this.cooldownMs}getLastProcessedTime(t){return this._lastProcessedTime.get(t)||0}cleanup(t){const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this.markDirty(s)}clear(){this._cleanItems.clear(),this._lastProcessedTime.clear()}getStats(){return{cleanCount:this._cleanItems.size,trackedCount:this._lastProcessedTime.size}}}const O=[];for(let c=0;c<256;++c)O.push((c+256).toString(16).slice(1));let Nt;const ns=new Uint8Array(16),ae={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function os(c,t,e){var o;if(ae.randomUUID&&!c)return ae.randomUUID();const s=(c=c||{}).random??((o=c.rng)==null?void 0:o.call(c))??function(){if(!Nt){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Nt=crypto.getRandomValues.bind(crypto)}return Nt(ns)}();if(s.length<16)throw new Error("Random bytes length must be >= 16");return s[6]=15&s[6]|64,s[8]=63&s[8]|128,function(n,a=0){return(O[n[a+0]]+O[n[a+1]]+O[n[a+2]]+O[n[a+3]]+"-"+O[n[a+4]]+O[n[a+5]]+"-"+O[n[a+6]]+O[n[a+7]]+"-"+O[n[a+8]]+O[n[a+9]]+"-"+O[n[a+10]]+O[n[a+11]]+O[n[a+12]]+O[n[a+13]]+O[n[a+14]]+O[n[a+15]]).toLowerCase()}(s)}class as{constructor(t,e,s=new is(3e5)){r(this,"_maxItemsPerIteration",5);this._extensionClient=t,this._flags=e,this._cache=s}async hydrateConversation(t){let e=t;return e=await this._hydrateExchanges(e),e=await this._hydrateToolUseStates(e),this._cache.markDirty(t.id),e}async dehydrateConversation(t){let e=t;return this._flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this._flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async dehydrateConversationsIncremental(t,e){if(!this._flags.enableExchangeStorage&&!this._flags.enableToolUseStateStorage)return t;const s=Object.entries(t),o=this._selectConversationsForDehydration(s,e),n={};for(const[a,h]of s)if(o.includes(a))try{const l=await this.dehydrateConversation(h);this._cache.markClean(a),n[a]=a===e?h:l}catch(l){console.warn(`Failed to dehydrate conversation ${a}:`,l),n[a]=h}else n[a]=h;return n}async hydrateCurrentConversation(t,e){if(!e||!t[e])return t;try{const s=await this.hydrateConversation(t[e]);return{...t,[e]:s}}catch(s){return console.warn(`Failed to hydrate conversation ${e}:`,s),t}}markNeedsDehydration(t){this._cache.markDirty(t)}markDehydrated(t){this._cache.markClean(t)}cleanupDeletedConversations(t){this._cache.cleanup(t)}_selectConversationsForDehydration(t,e){var n;const s=[];if(e){const a=(n=t.find(([h])=>h===e))==null?void 0:n[1];a&&!this._cache.isClean(e)&&(a.chatHistory.some(St)?s.push(e):this._cache.markClean(e))}const o=t.filter(([a,h])=>a===e||this._cache.isClean(a)?!1:h.chatHistory.some(St)?!this._cache.isWithinCooldown(a):(this._cache.markClean(a),!1)).map(([a])=>a).sort((a,h)=>this._cache.getLastProcessedTime(a)-this._cache.getLastProcessedTime(h));return[...s,...o].slice(0,this._maxItemsPerIteration)}async _dehydrateExchanges(t){var o;const e=[],s=[];for(const n of t.chatHistory)if(St(n)){const a=n,h=a.request_id||((o=crypto==null?void 0:crypto.randomUUID)==null?void 0:o.call(crypto))||os(),l={request_message:a.request_message,response_text:a.response_text||"",request_id:h,request_nodes:a.structured_request_nodes,response_nodes:a.structured_output_nodes,uuid:h,conversationId:t.id,status:a.status===ft.success?"success":a.status===ft.failed?"failed":"sent",timestamp:a.timestamp||new Date().toISOString(),seen_state:a.seen_state===It.seen?"seen":"unseen"};e.push(l);const g={chatItemType:_e.exchangePointer,exchangeUuid:h,timestamp:a.timestamp,request_message:a.request_message,status:a.status,hasResponse:!!a.response_text,isStreaming:a.status===ft.sent,seen_state:a.seen_state};s.push(g)}else s.push(n);if(e.length>0)try{await this._extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this._flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this._extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateExchanges(t){const e=t.chatHistory.filter(Xt);if(e.length===0)return t;try{const s=e.map(h=>h.exchangeUuid),o=await this._extensionClient.loadExchanges(t.id,s),n=new Map(o.map(h=>[h.uuid,h])),a=t.chatHistory.map(h=>{if(Xt(h)){const l=n.get(h.exchangeUuid);if(l)return{request_message:l.request_message,response_text:l.response_text,request_id:l.request_id,structured_request_nodes:l.request_nodes,structured_output_nodes:l.response_nodes,timestamp:l.timestamp,status:l.status==="success"?ft.success:l.status==="failed"?ft.failed:ft.sent,seen_state:l.seen_state==="seen"?It.seen:It.unseen}}return h});return{...t,chatHistory:a}}catch(s){return console.warn(`Failed to restore exchanges for conversation ${t.id}:`,s),t}}async _hydrateToolUseStates(t){try{const e=await this._extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:{...t.toolUseStates,...e}}}catch(e){return console.warn(`Failed to restore tool use states for conversation ${t.id}:`,e),t}}}const At=Q("idle");var rs=(c=>(c.manual="manual",c.auto="auto",c))(rs||{});class hs{constructor(t,e,s,o={}){r(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});r(this,"extensionClient");r(this,"_chatFlagsModel");r(this,"_currConversationModel");r(this,"_chatModeModel");r(this,"_sendModeModel");r(this,"_flagsLoaded",Q(!1));r(this,"_eventTracker");r(this,"_rulesModel");r(this,"_persistenceController");r(this,"_messageBroker");r(this,"subscribers",new Set);r(this,"idleMessageModel",new Je);r(this,"isPanelCollapsed");r(this,"agentExecutionMode");r(this,"sortConversationsBy");r(this,"displayedAnnouncements");r(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Re,bigSyncThreshold:t.bigSyncThreshold??Pe,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??De,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??be.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{}}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});r(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));r(this,"initializeSync",t=>{if(this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Gt&&this.currentConversationId!==Gt||(delete this._state.conversations[Gt],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===at||ot.isValid(s))),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});r(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const o=s.chatHistory.some(n=>zt(n));t[e]={...s,isShareable:o}}this._state.conversations=t});r(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[o,n]of Object.entries(e))n.isPinned&&s.add(o);this.setState(this._state),this.notifySubscribers()});r(this,"saveImmediate",()=>{this.setState(this._state),this.setState.flush()});r(this,"setState");r(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});r(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));r(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:_e.educateFeatures,request_id:crypto.randomUUID(),seen_state:It.seen})});r(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});r(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let o;t===void 0&&(t=at);const n=this._state.conversations[t];o=n?await this._persistenceController.hydrateConversation(n):ot.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===at&&(o.id=at),s!=null&&s.newTaskUuid&&(o.rootTaskUuid=s.newTaskUuid);const a=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(o,!a,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});r(this,"saveConversation",async(t,e)=>{this._persistenceController.markNeedsDehydration(t.id),this.updateChatState({...this._state,currentConversationId:t.id,conversations:{...this._state.conversations,[t.id]:t}}),e&&delete this._state.conversations[at]});r(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});r(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});r(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;At.set("copying");const s=e==null?void 0:e.chatHistory,o=s.reduce((h,l)=>(zt(l)&&h.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),h),[]);if(o.length===0)throw new Error("No chat history to share");const n=ot.getDisplayName(e),a=await this.extensionClient.saveChat(t,o,n);if(a.data){let h=a.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:h}}}),h}throw new Error("Failed to create URL")});r(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void At.set("idle");navigator.clipboard.writeText(e),At.set("copied")}catch{At.set("failed")}});r(this,"deleteConversations",async(t,e=void 0,s=[],o)=>{const n=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${n>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const a=new Set(t);await this.deleteConversationIds(a)}if(s.length>0&&o)for(const a of s)try{await o.deleteAgent(a,!0)}catch(h){console.error(`Failed to delete remote agent ${a}:`,h)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});r(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});r(this,"deleteConversationIds",async t=>{var o,n,a;const e=[],s=[];for(const h of t){const l=((o=this._state.conversations[h])==null?void 0:o.requestIds)??[];e.push(...l);const g=((n=this._state.conversations[h])==null?void 0:n.toolUseStates)??{};for(const v of Object.keys(g)){const{toolUseId:E}=g[v];E&&s.push(E)}const C=this._state.conversations[h];if(C){for(const v of C.chatHistory)if(St(v)&&v.structured_output_nodes)for(const E of v.structured_output_nodes)E.type===Be.TOOL_USE&&((a=E.tool_use)!=null&&a.tool_use_id)&&s.push(E.tool_use.tool_use_id)}}for(const h of Object.values(this._state.conversations))if(t.has(h.id)){for(const g of h.chatHistory)St(g)&&this.deleteImagesInExchange(g);const l=h.draftExchange;l&&this.deleteImagesInExchange(l)}for(const h of t){try{await this.extensionClient.deleteConversationExchanges(h)}catch(l){console.error(`Failed to delete exchanges for conversation ${h}:`,l)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(h)}catch(l){console.error(`Failed to delete tool use states for conversation ${h}:`,l)}}this._persistenceController.cleanupDeletedConversations(t),this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([h])=>!t.has(h)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});r(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});r(this,"findImagesInJson",t=>{const e=[],s=o=>{var n,a;if(o.type==="file"&&((n=o.attrs)!=null&&n.src)){const h=(a=o.attrs)==null?void 0:a.src;Ze(h)&&e.push(o.attrs.src)}else if((o.type==="doc"||o.type==="paragraph")&&o.content)for(const h of o.content)s(h)};return s(t),e});r(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===$e.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));r(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});r(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});r(this,"smartPaste",(t,e,s,o)=>{const n=this._currConversationModel.historyTo(t,!0).filter(a=>zt(a)).map(a=>({request_message:a.request_message,response_text:a.response_text||"",request_id:a.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:n,targetFile:s??void 0,options:o})});r(this,"saveImage",async t=>await this.extensionClient.saveImage(t));r(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));r(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));r(this,"renderImage",async t=>await this.extensionClient.loadImage(t));var l,g;this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=o,this._chatFlagsModel=new Oe(o.initialFlags),this._messageBroker=new qe(this._host),this._rulesModel=new Le(this._messageBroker,!1),this.extensionClient=new Ue(this._host,this._asyncMsgSender,this._chatFlagsModel),this._messageBroker.registerConsumer(this._rulesModel),this._currConversationModel=new ot(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation,this._rulesModel,{forceAgentConversation:o.forceAgentConversation}),this._sendModeModel=new es,this._persistenceController=new as(this.extensionClient,this._chatFlagsModel);const n=((l=o.debounceConfig)==null?void 0:l.wait)??5e3,a=((g=o.debounceConfig)==null?void 0:g.maxWait)??3e4;this.setState=xe(C=>{this._setStateWithPersistence(C)},n,{maxWait:a}),this.initializeSync(o.initialConversation);const h=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=Q(h),this.agentExecutionMode=Q(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=Q(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=Q(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t;const e={extensionClient:this.extensionClient,chatModeType:t.chatModeType,currentSendMode:this._sendModeModel.mode,agentExecutionMode:this.agentExecutionMode};this._eventTracker=new ss(e),this._currConversationModel.setEventTracker(this._eventTracker)}get flagsLoaded(){return this._flagsLoaded}get eventTracker(){return this._eventTracker}get rulesModel(){return this._rulesModel}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;this._state.conversations=await this._persistenceController.hydrateCurrentConversation(this._state.conversations,e),t?await this.setCurrentConversation(t.id):await this.setCurrentConversation(this.currentConversationId)}async _setStateWithPersistence(t){const e=await this._persistenceController.dehydrateConversationsIncremental(t.conversations??this._state.conversations,this.currentConversationId);this._host.setState({...t,conversations:e})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const o=t||this._state.sortConversationsBy||"lastMessageTimestamp";let n=Object.values(this._state.conversations);return s&&(n=n.filter(s)),n.sort((a,h)=>{const l=ot.getTime(a,o).getTime(),g=ot.getTime(h,o).getTime();return e==="asc"?l-g:g-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===at)return!1;const o=!ot.isValid(this.conversations[s]),n=Ge(this.conversations[s]);return o&&(t==="agent"&&n||t==="chat"&&!n||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===rt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}r(hs,"NEW_AGENT_KEY",at);const gt=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,re=new Set,Wt=typeof process=="object"&&process?process:{},ve=(c,t,e,s)=>{typeof Wt.emitWarning=="function"?Wt.emitWarning(c,t,e,s):console.error(`[${e}] ${t}: ${c}`)};let Ut=globalThis.AbortController,he=globalThis.AbortSignal;var le;if(Ut===void 0){he=class{constructor(){r(this,"onabort");r(this,"_onabort",[]);r(this,"reason");r(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},Ut=class{constructor(){r(this,"signal",new he);t()}abort(e){var s,o;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const n of this.signal._onabort)n(e);(o=(s=this.signal).onabort)==null||o.call(s,e)}}};let c=((le=Wt.env)==null?void 0:le.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{c&&(c=!1,ve("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const tt=c=>c&&c===Math.floor(c)&&c>0&&isFinite(c),pe=c=>tt(c)?c<=Math.pow(2,8)?Uint8Array:c<=Math.pow(2,16)?Uint16Array:c<=Math.pow(2,32)?Uint32Array:c<=Number.MAX_SAFE_INTEGER?kt:null:null;class kt extends Array{constructor(t){super(t),this.fill(0)}}var mt;const ht=class ht{constructor(t,e){r(this,"heap");r(this,"length");if(!i(ht,mt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=pe(t);if(!e)return[];f(ht,mt,!0);const s=new ht(t,e);return f(ht,mt,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};mt=new WeakMap,p(ht,mt,!1);let Vt=ht;var ce,de,W,R,V,j,_t,vt,x,q,M,S,_,U,G,L,F,B,A,$,Z,z,K,nt,D,d,qt,lt,Y,bt,H,Ce,ct,pt,wt,et,st,Bt,Ot,Lt,y,$t,yt,it,Zt;const Jt=class Jt{constructor(t){p(this,d);p(this,W);p(this,R);p(this,V);p(this,j);p(this,_t);p(this,vt);r(this,"ttl");r(this,"ttlResolution");r(this,"ttlAutopurge");r(this,"updateAgeOnGet");r(this,"updateAgeOnHas");r(this,"allowStale");r(this,"noDisposeOnSet");r(this,"noUpdateTTL");r(this,"maxEntrySize");r(this,"sizeCalculation");r(this,"noDeleteOnFetchRejection");r(this,"noDeleteOnStaleGet");r(this,"allowStaleOnFetchAbort");r(this,"allowStaleOnFetchRejection");r(this,"ignoreFetchAbort");p(this,x);p(this,q);p(this,M);p(this,S);p(this,_);p(this,U);p(this,G);p(this,L);p(this,F);p(this,B);p(this,A);p(this,$);p(this,Z);p(this,z);p(this,K);p(this,nt);p(this,D);p(this,lt,()=>{});p(this,Y,()=>{});p(this,bt,()=>{});p(this,H,()=>!1);p(this,ct,t=>{});p(this,pt,(t,e,s)=>{});p(this,wt,(t,e,s,o)=>{if(s||o)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});r(this,ce,"LRUCache");const{max:e=0,ttl:s,ttlResolution:o=1,ttlAutopurge:n,updateAgeOnGet:a,updateAgeOnHas:h,allowStale:l,dispose:g,disposeAfter:C,noDisposeOnSet:v,noUpdateTTL:E,maxSize:b=0,maxEntrySize:P=0,sizeCalculation:k,fetchMethod:T,memoMethod:m,noDeleteOnFetchRejection:w,noDeleteOnStaleGet:xt,allowStaleOnFetchRejection:J,allowStaleOnFetchAbort:N,ignoreFetchAbort:Ct}=t;if(e!==0&&!tt(e))throw new TypeError("max option must be a nonnegative integer");const X=e?pe(e):Array;if(!X)throw new Error("invalid max value: "+e);if(f(this,W,e),f(this,R,b),this.maxEntrySize=P||i(this,R),this.sizeCalculation=k,this.sizeCalculation){if(!i(this,R)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(m!==void 0&&typeof m!="function")throw new TypeError("memoMethod must be a function if defined");if(f(this,vt,m),T!==void 0&&typeof T!="function")throw new TypeError("fetchMethod must be a function if specified");if(f(this,_t,T),f(this,nt,!!T),f(this,M,new Map),f(this,S,new Array(e).fill(void 0)),f(this,_,new Array(e).fill(void 0)),f(this,U,new X(e)),f(this,G,new X(e)),f(this,L,0),f(this,F,0),f(this,B,Vt.create(e)),f(this,x,0),f(this,q,0),typeof g=="function"&&f(this,V,g),typeof C=="function"?(f(this,j,C),f(this,A,[])):(f(this,j,void 0),f(this,A,void 0)),f(this,K,!!i(this,V)),f(this,D,!!i(this,j)),this.noDisposeOnSet=!!v,this.noUpdateTTL=!!E,this.noDeleteOnFetchRejection=!!w,this.allowStaleOnFetchRejection=!!J,this.allowStaleOnFetchAbort=!!N,this.ignoreFetchAbort=!!Ct,this.maxEntrySize!==0){if(i(this,R)!==0&&!tt(i(this,R)))throw new TypeError("maxSize must be a positive integer if specified");if(!tt(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");u(this,d,Ce).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!xt,this.updateAgeOnGet=!!a,this.updateAgeOnHas=!!h,this.ttlResolution=tt(o)||o===0?o:1,this.ttlAutopurge=!!n,this.ttl=s||0,this.ttl){if(!tt(this.ttl))throw new TypeError("ttl must be a positive integer if specified");u(this,d,qt).call(this)}if(i(this,W)===0&&this.ttl===0&&i(this,R)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!i(this,W)&&!i(this,R)){const ut="LRU_CACHE_UNBOUNDED";(Tt=>!re.has(Tt))(ut)&&(re.add(ut),ve("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",ut,Jt))}}static unsafeExposeInternals(t){return{starts:i(t,Z),ttls:i(t,z),sizes:i(t,$),keyMap:i(t,M),keyList:i(t,S),valList:i(t,_),next:i(t,U),prev:i(t,G),get head(){return i(t,L)},get tail(){return i(t,F)},free:i(t,B),isBackgroundFetch:e=>{var s;return u(s=t,d,y).call(s,e)},backgroundFetch:(e,s,o,n)=>{var a;return u(a=t,d,Lt).call(a,e,s,o,n)},moveToTail:e=>{var s;return u(s=t,d,yt).call(s,e)},indexes:e=>{var s;return u(s=t,d,et).call(s,e)},rindexes:e=>{var s;return u(s=t,d,st).call(s,e)},isStale:e=>{var s;return i(s=t,H).call(s,e)}}}get max(){return i(this,W)}get maxSize(){return i(this,R)}get calculatedSize(){return i(this,q)}get size(){return i(this,x)}get fetchMethod(){return i(this,_t)}get memoMethod(){return i(this,vt)}get dispose(){return i(this,V)}get disposeAfter(){return i(this,j)}getRemainingTTL(t){return i(this,M).has(t)?1/0:0}*entries(){for(const t of u(this,d,et).call(this))i(this,_)[t]===void 0||i(this,S)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield[i(this,S)[t],i(this,_)[t]])}*rentries(){for(const t of u(this,d,st).call(this))i(this,_)[t]===void 0||i(this,S)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield[i(this,S)[t],i(this,_)[t]])}*keys(){for(const t of u(this,d,et).call(this)){const e=i(this,S)[t];e===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield e)}}*rkeys(){for(const t of u(this,d,st).call(this)){const e=i(this,S)[t];e===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield e)}}*values(){for(const t of u(this,d,et).call(this))i(this,_)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield i(this,_)[t])}*rvalues(){for(const t of u(this,d,st).call(this))i(this,_)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield i(this,_)[t])}[(de=Symbol.iterator,ce=Symbol.toStringTag,de)](){return this.entries()}find(t,e={}){for(const s of u(this,d,et).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;if(n!==void 0&&t(n,i(this,S)[s],this))return this.get(i(this,S)[s],e)}}forEach(t,e=this){for(const s of u(this,d,et).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;n!==void 0&&t.call(e,n,i(this,S)[s],this)}}rforEach(t,e=this){for(const s of u(this,d,st).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;n!==void 0&&t.call(e,n,i(this,S)[s],this)}}purgeStale(){let t=!1;for(const e of u(this,d,st).call(this,{allowStale:!0}))i(this,H).call(this,e)&&(u(this,d,it).call(this,i(this,S)[e],"expire"),t=!0);return t}info(t){const e=i(this,M).get(t);if(e===void 0)return;const s=i(this,_)[e],o=u(this,d,y).call(this,s)?s.__staleWhileFetching:s;if(o===void 0)return;const n={value:o};if(i(this,z)&&i(this,Z)){const a=i(this,z)[e],h=i(this,Z)[e];if(a&&h){const l=a-(gt.now()-h);n.ttl=l,n.start=Date.now()}}return i(this,$)&&(n.size=i(this,$)[e]),n}dump(){const t=[];for(const e of u(this,d,et).call(this,{allowStale:!0})){const s=i(this,S)[e],o=i(this,_)[e],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;if(n===void 0||s===void 0)continue;const a={value:n};if(i(this,z)&&i(this,Z)){a.ttl=i(this,z)[e];const h=gt.now()-i(this,Z)[e];a.start=Math.floor(Date.now()-h)}i(this,$)&&(a.size=i(this,$)[e]),t.unshift([s,a])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const o=Date.now()-s.start;s.start=gt.now()-o}this.set(e,s.value,s)}}set(t,e,s={}){var E,b,P,k,T;if(e===void 0)return this.delete(t),this;const{ttl:o=this.ttl,start:n,noDisposeOnSet:a=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:l}=s;let{noUpdateTTL:g=this.noUpdateTTL}=s;const C=i(this,wt).call(this,t,e,s.size||0,h);if(this.maxEntrySize&&C>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),u(this,d,it).call(this,t,"set"),this;let v=i(this,x)===0?void 0:i(this,M).get(t);if(v===void 0)v=i(this,x)===0?i(this,F):i(this,B).length!==0?i(this,B).pop():i(this,x)===i(this,W)?u(this,d,Ot).call(this,!1):i(this,x),i(this,S)[v]=t,i(this,_)[v]=e,i(this,M).set(t,v),i(this,U)[i(this,F)]=v,i(this,G)[v]=i(this,F),f(this,F,v),Ft(this,x)._++,i(this,pt).call(this,v,C,l),l&&(l.set="add"),g=!1;else{u(this,d,yt).call(this,v);const m=i(this,_)[v];if(e!==m){if(i(this,nt)&&u(this,d,y).call(this,m)){m.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:w}=m;w===void 0||a||(i(this,K)&&((E=i(this,V))==null||E.call(this,w,t,"set")),i(this,D)&&((b=i(this,A))==null||b.push([w,t,"set"])))}else a||(i(this,K)&&((P=i(this,V))==null||P.call(this,m,t,"set")),i(this,D)&&((k=i(this,A))==null||k.push([m,t,"set"])));if(i(this,ct).call(this,v),i(this,pt).call(this,v,C,l),i(this,_)[v]=e,l){l.set="replace";const w=m&&u(this,d,y).call(this,m)?m.__staleWhileFetching:m;w!==void 0&&(l.oldValue=w)}}else l&&(l.set="update")}if(o===0||i(this,z)||u(this,d,qt).call(this),i(this,z)&&(g||i(this,bt).call(this,v,o,n),l&&i(this,Y).call(this,l,v)),!a&&i(this,D)&&i(this,A)){const m=i(this,A);let w;for(;w=m==null?void 0:m.shift();)(T=i(this,j))==null||T.call(this,...w)}return this}pop(){var t;try{for(;i(this,x);){const e=i(this,_)[i(this,L)];if(u(this,d,Ot).call(this,!0),u(this,d,y).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(i(this,D)&&i(this,A)){const e=i(this,A);let s;for(;s=e==null?void 0:e.shift();)(t=i(this,j))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:o}=e,n=i(this,M).get(t);if(n!==void 0){const a=i(this,_)[n];if(u(this,d,y).call(this,a)&&a.__staleWhileFetching===void 0)return!1;if(!i(this,H).call(this,n))return s&&i(this,lt).call(this,n),o&&(o.has="hit",i(this,Y).call(this,o,n)),!0;o&&(o.has="stale",i(this,Y).call(this,o,n))}else o&&(o.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,o=i(this,M).get(t);if(o===void 0||!s&&i(this,H).call(this,o))return;const n=i(this,_)[o];return u(this,d,y).call(this,n)?n.__staleWhileFetching:n}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:o=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:a=this.ttl,noDisposeOnSet:h=this.noDisposeOnSet,size:l=0,sizeCalculation:g=this.sizeCalculation,noUpdateTTL:C=this.noUpdateTTL,noDeleteOnFetchRejection:v=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:E=this.allowStaleOnFetchRejection,ignoreFetchAbort:b=this.ignoreFetchAbort,allowStaleOnFetchAbort:P=this.allowStaleOnFetchAbort,context:k,forceRefresh:T=!1,status:m,signal:w}=e;if(!i(this,nt))return m&&(m.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:o,noDeleteOnStaleGet:n,status:m});const xt={allowStale:s,updateAgeOnGet:o,noDeleteOnStaleGet:n,ttl:a,noDisposeOnSet:h,size:l,sizeCalculation:g,noUpdateTTL:C,noDeleteOnFetchRejection:v,allowStaleOnFetchRejection:E,allowStaleOnFetchAbort:P,ignoreFetchAbort:b,status:m,signal:w};let J=i(this,M).get(t);if(J===void 0){m&&(m.fetch="miss");const N=u(this,d,Lt).call(this,t,J,xt,k);return N.__returned=N}{const N=i(this,_)[J];if(u(this,d,y).call(this,N)){const Tt=s&&N.__staleWhileFetching!==void 0;return m&&(m.fetch="inflight",Tt&&(m.returnedStale=!0)),Tt?N.__staleWhileFetching:N.__returned=N}const Ct=i(this,H).call(this,J);if(!T&&!Ct)return m&&(m.fetch="hit"),u(this,d,yt).call(this,J),o&&i(this,lt).call(this,J),m&&i(this,Y).call(this,m,J),N;const X=u(this,d,Lt).call(this,t,J,xt,k),ut=X.__staleWhileFetching!==void 0&&s;return m&&(m.fetch=Ct?"stale":"refresh",ut&&Ct&&(m.returnedStale=!0)),ut?X.__staleWhileFetching:X.__returned=X}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=i(this,vt);if(!s)throw new Error("no memoMethod provided to constructor");const{context:o,forceRefresh:n,...a}=e,h=this.get(t,a);if(!n&&h!==void 0)return h;const l=s(t,h,{options:a,context:o});return this.set(t,l,a),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:o=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:a}=e,h=i(this,M).get(t);if(h!==void 0){const l=i(this,_)[h],g=u(this,d,y).call(this,l);return a&&i(this,Y).call(this,a,h),i(this,H).call(this,h)?(a&&(a.get="stale"),g?(a&&s&&l.__staleWhileFetching!==void 0&&(a.returnedStale=!0),s?l.__staleWhileFetching:void 0):(n||u(this,d,it).call(this,t,"expire"),a&&s&&(a.returnedStale=!0),s?l:void 0)):(a&&(a.get="hit"),g?l.__staleWhileFetching:(u(this,d,yt).call(this,h),o&&i(this,lt).call(this,h),l))}a&&(a.get="miss")}delete(t){return u(this,d,it).call(this,t,"delete")}clear(){return u(this,d,Zt).call(this,"delete")}};W=new WeakMap,R=new WeakMap,V=new WeakMap,j=new WeakMap,_t=new WeakMap,vt=new WeakMap,x=new WeakMap,q=new WeakMap,M=new WeakMap,S=new WeakMap,_=new WeakMap,U=new WeakMap,G=new WeakMap,L=new WeakMap,F=new WeakMap,B=new WeakMap,A=new WeakMap,$=new WeakMap,Z=new WeakMap,z=new WeakMap,K=new WeakMap,nt=new WeakMap,D=new WeakMap,d=new WeakSet,qt=function(){const t=new kt(i(this,W)),e=new kt(i(this,W));f(this,z,t),f(this,Z,e),f(this,bt,(n,a,h=gt.now())=>{if(e[n]=a!==0?h:0,t[n]=a,a!==0&&this.ttlAutopurge){const l=setTimeout(()=>{i(this,H).call(this,n)&&u(this,d,it).call(this,i(this,S)[n],"expire")},a+1);l.unref&&l.unref()}}),f(this,lt,n=>{e[n]=t[n]!==0?gt.now():0}),f(this,Y,(n,a)=>{if(t[a]){const h=t[a],l=e[a];if(!h||!l)return;n.ttl=h,n.start=l,n.now=s||o();const g=n.now-l;n.remainingTTL=h-g}});let s=0;const o=()=>{const n=gt.now();if(this.ttlResolution>0){s=n;const a=setTimeout(()=>s=0,this.ttlResolution);a.unref&&a.unref()}return n};this.getRemainingTTL=n=>{const a=i(this,M).get(n);if(a===void 0)return 0;const h=t[a],l=e[a];return!h||!l?1/0:h-((s||o())-l)},f(this,H,n=>{const a=e[n],h=t[n];return!!h&&!!a&&(s||o())-a>h})},lt=new WeakMap,Y=new WeakMap,bt=new WeakMap,H=new WeakMap,Ce=function(){const t=new kt(i(this,W));f(this,q,0),f(this,$,t),f(this,ct,e=>{f(this,q,i(this,q)-t[e]),t[e]=0}),f(this,wt,(e,s,o,n)=>{if(u(this,d,y).call(this,s))return 0;if(!tt(o)){if(!n)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(o=n(s,e),!tt(o))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return o}),f(this,pt,(e,s,o)=>{if(t[e]=s,i(this,R)){const n=i(this,R)-t[e];for(;i(this,q)>n;)u(this,d,Ot).call(this,!0)}f(this,q,i(this,q)+t[e]),o&&(o.entrySize=s,o.totalCalculatedSize=i(this,q))})},ct=new WeakMap,pt=new WeakMap,wt=new WeakMap,et=function*({allowStale:t=this.allowStale}={}){if(i(this,x))for(let e=i(this,F);u(this,d,Bt).call(this,e)&&(!t&&i(this,H).call(this,e)||(yield e),e!==i(this,L));)e=i(this,G)[e]},st=function*({allowStale:t=this.allowStale}={}){if(i(this,x))for(let e=i(this,L);u(this,d,Bt).call(this,e)&&(!t&&i(this,H).call(this,e)||(yield e),e!==i(this,F));)e=i(this,U)[e]},Bt=function(t){return t!==void 0&&i(this,M).get(i(this,S)[t])===t},Ot=function(t){var n,a;const e=i(this,L),s=i(this,S)[e],o=i(this,_)[e];return i(this,nt)&&u(this,d,y).call(this,o)?o.__abortController.abort(new Error("evicted")):(i(this,K)||i(this,D))&&(i(this,K)&&((n=i(this,V))==null||n.call(this,o,s,"evict")),i(this,D)&&((a=i(this,A))==null||a.push([o,s,"evict"]))),i(this,ct).call(this,e),t&&(i(this,S)[e]=void 0,i(this,_)[e]=void 0,i(this,B).push(e)),i(this,x)===1?(f(this,L,f(this,F,0)),i(this,B).length=0):f(this,L,i(this,U)[e]),i(this,M).delete(s),Ft(this,x)._--,e},Lt=function(t,e,s,o){const n=e===void 0?void 0:i(this,_)[e];if(u(this,d,y).call(this,n))return n;const a=new Ut,{signal:h}=s;h==null||h.addEventListener("abort",()=>a.abort(h.reason),{signal:a.signal});const l={signal:a.signal,options:s,context:o},g=(b,P=!1)=>{const{aborted:k}=a.signal,T=s.ignoreFetchAbort&&b!==void 0;if(s.status&&(k&&!P?(s.status.fetchAborted=!0,s.status.fetchError=a.signal.reason,T&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),k&&!T&&!P)return C(a.signal.reason);const m=v;return i(this,_)[e]===v&&(b===void 0?m.__staleWhileFetching?i(this,_)[e]=m.__staleWhileFetching:u(this,d,it).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,b,l.options))),b},C=b=>{const{aborted:P}=a.signal,k=P&&s.allowStaleOnFetchAbort,T=k||s.allowStaleOnFetchRejection,m=T||s.noDeleteOnFetchRejection,w=v;if(i(this,_)[e]===v&&(!m||w.__staleWhileFetching===void 0?u(this,d,it).call(this,t,"fetch"):k||(i(this,_)[e]=w.__staleWhileFetching)),T)return s.status&&w.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),w.__staleWhileFetching;if(w.__returned===w)throw b};s.status&&(s.status.fetchDispatched=!0);const v=new Promise((b,P)=>{var T;const k=(T=i(this,_t))==null?void 0:T.call(this,t,n,l);k&&k instanceof Promise&&k.then(m=>b(m===void 0?void 0:m),P),a.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(b(void 0),s.allowStaleOnFetchAbort&&(b=m=>g(m,!0)))})}).then(g,b=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=b),C(b))),E=Object.assign(v,{__abortController:a,__staleWhileFetching:n,__returned:void 0});return e===void 0?(this.set(t,E,{...l.options,status:void 0}),e=i(this,M).get(t)):i(this,_)[e]=E,E},y=function(t){if(!i(this,nt))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof Ut},$t=function(t,e){i(this,G)[e]=t,i(this,U)[t]=e},yt=function(t){t!==i(this,F)&&(t===i(this,L)?f(this,L,i(this,U)[t]):u(this,d,$t).call(this,i(this,G)[t],i(this,U)[t]),u(this,d,$t).call(this,i(this,F),t),f(this,F,t))},it=function(t,e){var o,n,a,h;let s=!1;if(i(this,x)!==0){const l=i(this,M).get(t);if(l!==void 0)if(s=!0,i(this,x)===1)u(this,d,Zt).call(this,e);else{i(this,ct).call(this,l);const g=i(this,_)[l];if(u(this,d,y).call(this,g)?g.__abortController.abort(new Error("deleted")):(i(this,K)||i(this,D))&&(i(this,K)&&((o=i(this,V))==null||o.call(this,g,t,e)),i(this,D)&&((n=i(this,A))==null||n.push([g,t,e]))),i(this,M).delete(t),i(this,S)[l]=void 0,i(this,_)[l]=void 0,l===i(this,F))f(this,F,i(this,G)[l]);else if(l===i(this,L))f(this,L,i(this,U)[l]);else{const C=i(this,G)[l];i(this,U)[C]=i(this,U)[l];const v=i(this,U)[l];i(this,G)[v]=i(this,G)[l]}Ft(this,x)._--,i(this,B).push(l)}}if(i(this,D)&&((a=i(this,A))!=null&&a.length)){const l=i(this,A);let g;for(;g=l==null?void 0:l.shift();)(h=i(this,j))==null||h.call(this,...g)}return s},Zt=function(t){var e,s,o;for(const n of u(this,d,st).call(this,{allowStale:!0})){const a=i(this,_)[n];if(u(this,d,y).call(this,a))a.__abortController.abort(new Error("deleted"));else{const h=i(this,S)[n];i(this,K)&&((e=i(this,V))==null||e.call(this,a,h,t)),i(this,D)&&((s=i(this,A))==null||s.push([a,h,t]))}}if(i(this,M).clear(),i(this,_).fill(void 0),i(this,S).fill(void 0),i(this,z)&&i(this,Z)&&(i(this,z).fill(0),i(this,Z).fill(0)),i(this,$)&&i(this,$).fill(0),f(this,L,0),f(this,F,0),i(this,B).length=0,f(this,q,0),f(this,x,0),i(this,D)&&i(this,A)){const n=i(this,A);let a;for(;a=n==null?void 0:n.shift();)(o=i(this,j))==null||o.call(this,...a)}};let jt=Jt;class ws{constructor(){r(this,"_syncStatus",{status:ze.done,foldersProgress:[]});r(this,"_syncEnabledState",te.initializing);r(this,"_workspaceGuidelines",[]);r(this,"_openUserGuidelinesInput",!1);r(this,"_userGuidelines");r(this,"_contextStore",new ls);r(this,"_prevOpenFiles",[]);r(this,"_disableContext",!1);r(this,"_enableAgentMemories",!1);r(this,"subscribers",new Set);r(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));r(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case rt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case rt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case rt.fileRangesSelected:this.updateSelections(e.data);break;case rt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case rt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case rt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});r(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:I.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});r(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});r(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});r(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});r(this,"addFile",t=>{this.addFiles([t])});r(this,"addFiles",t=>{this.updateFiles(t,[])});r(this,"removeFile",t=>{this.removeFiles([t])});r(this,"removeFiles",t=>{this.updateFiles([],t)});r(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});r(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});r(this,"updateFiles",(t,e)=>{const s=a=>({file:a,...Ht(a)}),o=t.map(s),n=e.map(s);this._contextStore.update(o,n,a=>a.id),this.notifySubscribers()});r(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});r(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});r(this,"setCurrentlyOpenFiles",t=>{const e=t.map(o=>({recentFile:o,...Ht(o)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,o=>o.id),s.forEach(o=>{const n=this._contextStore.peekKey(o.id);n!=null&&n.recentFile&&(n.file=n.recentFile,delete n.recentFile)}),e.forEach(o=>{const n=this._contextStore.peekKey(o.id);n!=null&&n.file&&(n.recentFile=n.file,delete n.file)}),this.notifySubscribers()});r(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});r(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,o=t.overLimit||((e==null?void 0:e.overLimit)??!1),n={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:I.active,referenceCount:1,showWarning:o,rulesAndGuidelinesState:e};this._contextStore.update([n],s,a=>{var h;return a.id+String((h=a.userGuidelines)==null?void 0:h.overLimit)}),this.notifySubscribers()});r(this,"onGuidelinesStateUpdate",t=>{var o;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const n=e||{overLimit:!1,contents:"",lengthLimit:((o=t.rulesAndGuidelines)==null?void 0:o.lengthLimit)??2e3};this.updateUserGuidelines(n,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(n=>n.sourceFolder))});r(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(o=>o.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));r(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});r(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});r(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});r(this,"updateSelections",t=>{const e=this._contextStore.values.filter(se),s=t.map(o=>({selection:o,...Ht(o)}));this._contextStore.update([],e,o=>o.id),this._contextStore.update(s,[],o=>o.id),this.notifySubscribers()});r(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});r(this,"markInactive",t=>{this.markItemsInactive([t])});r(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,I.inactive)}),this.notifySubscribers()});r(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});r(this,"markActive",t=>{this.markItemsActive([t])});r(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,I.active)}),this.notifySubscribers()});r(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});r(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});r(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});r(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>He(t)&&!ee(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(ee)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(se)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Ne)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(ie)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(We)}get userGuidelines(){return this._contextStore.values.filter(ne)}get workspaceGuidelines(){return this._workspaceGuidelines}get agentMemories(){return[{...Ve,status:this._enableAgentMemories?I.active:I.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>oe(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===I.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===I.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===I.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===I.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===I.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===I.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===te.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(g=>g.progress!==void 0);if(t.length===0)return;const e=t.reduce((g,C)=>{var v;return g+(((v=C==null?void 0:C.progress)==null?void 0:v.trackedFiles)??0)},0),s=t.reduce((g,C)=>{var v;return g+(((v=C==null?void 0:C.progress)==null?void 0:v.backlogSize)??0)},0),o=Math.max(e,0),n=Math.min(Math.max(s,0),o),a=o-n,h=[];for(const g of t)(l=g==null?void 0:g.progress)!=null&&l.newlyTracked&&h.push(g.folderRoot);return{status:this._syncStatus.status,totalFiles:o,syncedCount:a,backlogSize:n,newlyTrackedFolders:h}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(ie(t)||ne(t)||je(t)||oe(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===I.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===I.inactive)}get isContextDisabled(){return this._disableContext}}class ls{constructor(){r(this,"_cache",new jt({max:1e3}));r(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));r(this,"clear",()=>{this._cache.clear()});r(this,"update",(t,e,s)=>{t.forEach(o=>this.addInPlace(o,s)),e.forEach(o=>this.removeInPlace(o,s))});r(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});r(this,"addInPlace",(t,e)=>{const s=e(t),o=t.referenceCount??1,n=this._cache.get(s),a=t.status??(n==null?void 0:n.status)??I.active;n?(n.referenceCount+=o,n.status=a,n.pinned=t.pinned??n.pinned,n.showWarning=t.showWarning??n.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in n&&(n.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in n&&(n.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:o,status:a})});r(this,"removeInPlace",(t,e)=>{const s=e(t),o=this._cache.get(s);o&&(o.referenceCount-=1,o.referenceCount===0&&this._cache.delete(s))});r(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});r(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});r(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});r(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});r(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===I.active?I.inactive:I.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}var cs=me('<span class="c-keyboard-shortcut-hint__icon svelte-1txw16l"> </span>'),ds=me("<span></span>");function Ms(c,t){Te(t,!1);let e=Rt(t,"class",8,""),s=Rt(t,"keybinding",24,()=>{}),o=Rt(t,"icons",24,()=>{var a;return((a=s())==null?void 0:a.split("-"))??[]});Fe();var n=ds();we(n,5,o,Me,(a,h)=>{var l=cs(),g=Kt(l);Qt(()=>Ae(g,Ie(h))),dt(a,l)}),Qt(()=>Ee(n,1,`c-keyboard-shortcut-hint ${e()}`,"svelte-1txw16l")),dt(c,n),ke()}var us=Mt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z" fill="currentColor"></path></svg>');function xs(c){var t=us();dt(c,t)}var fs=Mt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z" fill="currentColor"></path></svg>');function Ts(c){var t=fs();dt(c,t)}var gs=Mt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z" fill="currentColor"></path></svg>');function Fs(c){var t=gs();dt(c,t)}export{rs as A,hs as C,bs as D,Ts as F,Ms as K,ws as S,xs as a,Fs as b,Et as c,ts as d,Xe as e};
