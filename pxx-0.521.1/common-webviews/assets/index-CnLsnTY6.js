var pl=Object.defineProperty;var ml=(e,t,n)=>t in e?pl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var g=(e,t,n)=>ml(e,typeof t!="symbol"?t+"":t,n);import{a as gl}from"./async-messaging-DXXiLgz5.js";import{W as E,S as fl,a as Os,b as De,B as yl,h as _l}from"./IconButtonAugment-DVt24OaC.js";import{R as ne,P as z,C as R,b as Xn,I as Qt,a as K,E as bl}from"./message-broker-Bv_1VsFe.js";import{C as vl}from"./types-CGlLNakm.js";import{n as Sl,f as El,i as Tl,F as Kr}from"./focusTrapStack-CB_5BS9R.js";import{w as Bt,y as vr,aA as wl,f as Uo,b as O,aH as Ps,A as _e,C as x,v as $o,u as oe,X as xe,$ as ft,D as be,P as we,al as Il,J as re,N as se,t as ce,Q as yt,ab as _t,a4 as ke,I as ve,a2 as He,_ as F,m as Ae,K as Ye,a5 as Nl,Y as Re,z as qo,l as W,a7 as et,G as Ce,H as Ot,L as nt,M as Pt,T as Ho,F as Be,aF as Wr,a3 as In,ba as zr,Z as Bo,a as Go,b0 as Xr,am as Vo,aD as kl,B as Cl,R as xl}from"./SpinnerAugment-AffdR7--.js";import{a as Al,c as Rl,_ as Ml,i as Sr}from"./isObjectLike-D6uePTe3.js";import{c as Ol,e as pn,f as Pl,C as Dl,a as mn,R as Ll,b as it,g as Jr}from"./CardAugment-CB88N7dm.js";import{B as Fl,b as Ul}from"./BaseTextInput-IcL3sG2L.js";function Er(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var _;function $l(){let e=0,t=0;for(let s=0;s<28;s+=7){let a=this.buf[this.pos++];if(e|=(127&a)<<s,!(128&a))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let s=3;s<=31;s+=7){let a=this.buf[this.pos++];if(t|=(127&a)<<s,!(128&a))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function Jn(e,t,n){for(let r=0;r<28;r+=7){const o=e>>>r,i=!(!(o>>>7)&&t==0),u=255&(i?128|o:o);if(n.push(u),!i)return}const s=e>>>28&15|(7&t)<<4,a=!!(t>>3);if(n.push(255&(a?128|s:s)),a){for(let r=3;r<31;r+=7){const o=t>>>r,i=!!(o>>>7),u=255&(i?128|o:o);if(n.push(u),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(_||(_={}));const gn=4294967296;function Zr(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let s=0,a=0;function r(o,i){const u=Number(e.slice(o,i));a*=n,s=s*n+u,s>=gn&&(a+=s/gn|0,s%=gn)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),t?jo(s,a):Tr(s,a)}function Qr(e,t){if({lo:e,hi:t}=function(u,l){return{lo:u>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(gn*t+e);const n=16777215&(e>>>24|t<<8),s=t>>16&65535;let a=(16777215&e)+6777216*n+6710656*s,r=n+8147497*s,o=2*s;const i=1e7;return a>=i&&(r+=Math.floor(a/i),a%=i),r>=i&&(o+=Math.floor(r/i),r%=i),o.toString()+ea(r)+ea(a)}function Tr(e,t){return{lo:0|e,hi:0|t}}function jo(e,t){return t=~t,e?e=1+~e:t+=1,Tr(e,t)}const ea=e=>{const t=String(e);return"0000000".slice(t.length)+t};function ta(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function ql(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var na={};const L=Hl();function Hl(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof na!="object"||na.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(r){const o=typeof r=="bigint"?r:BigInt(r);if(o>n||o<t)throw new Error(`invalid int64: ${r}`);return o},uParse(r){const o=typeof r=="bigint"?r:BigInt(r);if(o>a||o<s)throw new Error(`invalid uint64: ${r}`);return o},enc(r){return e.setBigInt64(0,this.parse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(r){return e.setBigInt64(0,this.uParse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(r,o)=>(e.setInt32(0,r,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(r,o)=>(e.setInt32(0,r,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),sa(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),ra(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),sa(t),Zr(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),ra(t),Zr(t)),dec:(t,n)=>function(s,a){let r=Tr(s,a);const o=2147483648&r.hi;o&&(r=jo(r.lo,r.hi));const i=Qr(r.lo,r.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>Qr(t,n)}}function sa(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function ra(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function st(e,t){switch(e){case _.STRING:return"";case _.BOOL:return!1;case _.DOUBLE:case _.FLOAT:return 0;case _.INT64:case _.UINT64:case _.SFIXED64:case _.FIXED64:case _.SINT64:return t?"0":L.zero;case _.BYTES:return new Uint8Array(0);default:return 0}}const Ue=Symbol.for("reflect unsafe local");function Yo(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(s=>s.localName===n)}function Bl(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(s,a){switch(s){case _.BOOL:return a===!1;case _.STRING:return a==="";case _.BYTES:return a instanceof Uint8Array&&!a.byteLength;default:return a==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function Dt(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Ko(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function Wo(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function ze(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function wr(e,t){var n,s,a,r;if(ze(e)&&Ue in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function Ir(e,t){var n,s,a,r;if(ze(e)&&Ue in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function Nr(e,t){return ze(e)&&Ue in e&&"desc"in e&&ze(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function zt(e){const t=e.fields[0];return zo(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function zo(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const Gl=999,Vl=998,en=2;function Ee(e,t){if(Er(t,e))return t;const n=function(s){let a;if(function(r){switch(r.file.edition){case Gl:return!1;case Vl:return!0;default:return r.fields.some(o=>o.presence!=en&&o.fieldKind!="message"&&!o.oneof)}}(s)){const r=oa.get(s);let o,i;if(r)({prototype:o,members:i}=r);else{o={},i=new Set;for(const u of s.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=en&&(i.add(u),o[u.localName]=Zn(u)));oa.set(s,{prototype:o,members:i})}a=Object.create(o),a.$typeName=s.typeName;for(const u of s.members)if(!i.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=en))continue;a[u.localName]=Zn(u)}}else{a={$typeName:s.typeName};for(const r of s.members)r.kind!="oneof"&&r.presence!=en||(a[r.localName]=Zn(r))}return a}(e);return t!==void 0&&function(s,a,r){for(const o of s.members){let i,u=r[o.localName];if(u!=null){if(o.kind=="oneof"){const l=Yo(r,o);if(!l)continue;i=l,u=Ko(r,l)}else i=o;switch(i.fieldKind){case"message":u=kr(i,u);break;case"scalar":u=Xo(i,u);break;case"list":u=Yl(i,u);break;case"map":u=jl(i,u)}Wo(a,i,u)}}}(e,n,t),n}function Xo(e,t){return e.scalar==_.BYTES?Cr(t):t}function jl(e,t){if(ze(t)){if(e.scalar==_.BYTES)return aa(t,Cr);if(e.mapKind=="message")return aa(t,n=>kr(e,n))}return t}function Yl(e,t){if(Array.isArray(t)){if(e.scalar==_.BYTES)return t.map(Cr);if(e.listKind=="message")return t.map(n=>kr(e,n))}return t}function kr(e,t){if(e.fieldKind=="message"&&!e.oneof&&zt(e.message))return Xo(e.message.fields[0],t);if(ze(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Er(t,e.message))return Ee(e.message,t)}return t}function Cr(e){return Array.isArray(e)?new Uint8Array(e):e}function aa(e,t){const n={};for(const s of Object.entries(e))n[s[0]]=t(s[1]);return n}const Kl=Symbol(),oa=new WeakMap;function Zn(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Kl;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?st(e.scalar,e.longAsString):e.enum.values[0].number}const Wl=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class ie extends Error{constructor(t,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>t}}const Qn=Symbol.for("@bufbuild/protobuf/text-encoding");function xr(){if(globalThis[Qn]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[Qn]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[Qn]}var $;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})($||($={}));const Jo=34028234663852886e22,Zo=-34028234663852886e22,Qo=4294967295,ei=2147483647,ti=-2147483648;class ni{constructor(t=xr().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let a=0;a<this.chunks.length;a++)t+=this.chunks[a].length;let n=new Uint8Array(t),s=0;for(let a=0;a<this.chunks.length;a++)n.set(this.chunks[a],s),s+=this.chunks[a].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(ia(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return es(t),ta(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(s){if(typeof s=="string"){const a=s;if(s=Number(s),Number.isNaN(s)&&a!=="NaN")throw new Error("invalid float32: "+a)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>Jo||s<Zo))throw new Error("invalid float32: "+s)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){ia(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){es(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return es(t),ta(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),a=L.enc(t);return s.setInt32(0,a.lo,!0),s.setInt32(4,a.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),a=L.uEnc(t);return s.setInt32(0,a.lo,!0),s.setInt32(4,a.hi,!0),this.raw(n)}int64(t){let n=L.enc(t);return Jn(n.lo,n.hi,this.buf),this}sint64(t){const n=L.enc(t),s=n.hi>>31;return Jn(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(t){const n=L.uEnc(t);return Jn(n.lo,n.hi,this.buf),this}}class Ar{constructor(t,n=xr().decodeUtf8){this.decodeUtf8=n,this.varint64=$l,this.uint32=ql,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,s=7&t;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(t,n){let s=this.pos;switch(t){case $.Varint:for(;128&this.buf[this.pos++];);break;case $.Bit64:this.pos+=4;case $.Bit32:this.pos+=4;break;case $.LengthDelimited:let a=this.uint32();this.pos+=a;break;case $.StartGroup:for(;;){const[r,o]=this.tag();if(o===$.EndGroup){if(n!==void 0&&r!==n)throw new Error("invalid end group tag");break}this.skip(o,r)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return L.dec(...this.varint64())}uint64(){return L.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),s=-(1&t);return t=(t>>>1|(1&n)<<31)^s,n=n>>>1^s,L.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return L.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return L.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function es(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>ei||e<ti)throw new Error("invalid int32: "+e)}function ia(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>Qo||e<0)throw new Error("invalid uint32: "+e)}function Xe(e,t){const n=e.fieldKind=="list"?wr(t,e):e.fieldKind=="map"?Ir(t,e):Rr(e,t);if(n===!0)return;let s;switch(e.fieldKind){case"list":s=`expected ${ai(e)}, got ${G(t)}`;break;case"map":s=`expected ${oi(e)}, got ${G(t)}`;break;default:s=Nn(e,t,n)}return new ie(e,s)}function la(e,t,n){const s=Rr(e,n);if(s!==!0)return new ie(e,`list item #${t+1}: ${Nn(e,n,s)}`)}function Rr(e,t){return e.scalar!==void 0?si(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):Nr(t,e.message)}function si(e,t){switch(t){case _.DOUBLE:return typeof e=="number";case _.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>Jo||e<Zo)||`${e.toFixed()} out of range`);case _.INT32:case _.SFIXED32:case _.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>ei||e<ti)||`${e.toFixed()} out of range`);case _.FIXED32:case _.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Qo||e<0)||`${e.toFixed()} out of range`);case _.BOOL:return typeof e=="boolean";case _.STRING:return typeof e=="string"&&(xr().checkUtf8(e)||"invalid UTF8");case _.BYTES:return e instanceof Uint8Array;case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return L.parse(e),!0}catch{return`${e} out of range`}return!1;case _.FIXED64:case _.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return L.uParse(e),!0}catch{return`${e} out of range`}return!1}}function Nn(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${G(t)}`,e.scalar!==void 0?`expected ${function(s){switch(s){case _.STRING:return"string";case _.BOOL:return"boolean";case _.INT64:case _.SINT64:case _.SFIXED64:return"bigint (int64)";case _.UINT64:case _.FIXED64:return"bigint (uint64)";case _.BYTES:return"Uint8Array";case _.DOUBLE:return"number (float64)";case _.FLOAT:return"number (float32)";case _.FIXED32:case _.UINT32:return"number (uint32)";case _.INT32:case _.SFIXED32:case _.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${ri(e.message)}`+n}function G(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:wr(e)?ai(e.field()):Ir(e)?oi(e.field()):Nr(e)?ri(e.desc):Er(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function ri(e){return`ReflectMessage (${e.typeName})`}function ai(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${_[e.scalar]})`}}function oi(e){switch(e.mapKind){case"message":return`ReflectMap (${_[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${_[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${_[e.mapKey]}, ${_[e.scalar]})`}}function me(e,t,n=!0){return new ii(e,t,n)}class ii{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(t,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=t,this.message=this[Ue]=n??Ee(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return Tt(this.message,t),Yo(this.message,t)}isSet(t){return Tt(this.message,t),Bl(this.message,t)}clear(t){Tt(this.message,t),function(n,s){const a=s.localName;if(s.oneof){const r=s.oneof.localName;n[r].case===a&&(n[r]={case:void 0})}else if(s.presence!=2)delete n[a];else switch(s.fieldKind){case"map":n[a]={};break;case"list":n[a]=[];break;case"enum":n[a]=s.enum.values[0].number;break;case"scalar":n[a]=st(s.scalar,s.longAsString)}}(this.message,t)}get(t){Tt(this.message,t);const n=Ko(this.message,t);switch(t.fieldKind){case"list":let s=this.lists.get(t);return s&&s[Ue]===n||this.lists.set(t,s=new zl(t,n,this.check)),s;case"map":let a=this.maps.get(t);return a&&a[Ue]===n||this.maps.set(t,a=new Xl(t,n,this.check)),a;case"message":return Or(t,n,this.check);case"scalar":return n===void 0?st(t.scalar,!1):Pr(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(Tt(this.message,t),this.check){const a=Xe(t,n);if(a)throw a}let s;s=t.fieldKind=="message"?Mr(t,n):Ir(n)||wr(n)?n[Ue]:Dr(t,n),Wo(this.message,t,s)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function Tt(e,t){if(t.parent.typeName!==e.$typeName)throw new ie(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class zl{field(){return this._field}get size(){return this._arr.length}constructor(t,n,s){this._field=t,this._arr=this[Ue]=n,this.check=s}get(t){const n=this._arr[t];return n===void 0?void 0:ts(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new ie(this._field,`list item #${t+1}: out of range`);if(this.check){const s=la(this._field,t,n);if(s)throw s}this._arr[t]=ua(this._field,n)}add(t){if(this.check){const n=la(this._field,this._arr.length,t);if(n)throw n}this._arr.push(ua(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield ts(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,ts(this._field,this._arr[t],this.check)]}}class Xl{constructor(t,n,s=!0){this.obj=this[Ue]=n??{},this.check=s,this._field=t}field(){return this._field}set(t,n){if(this.check){const s=function(a,r,o){const i=si(r,a.mapKey);if(i!==!0)return new ie(a,`invalid map key: ${Nn({scalar:a.mapKey},r,i)}`);const u=Rr(a,o);return u!==!0?new ie(a,`map entry ${G(r)}: ${Nn(a,o,u)}`):void 0}(this._field,t,n);if(s)throw s}return this.obj[tn(t)]=function(s,a){return s.mapKind=="message"?Mr(s,a):Dr(s,a)}(this._field,n),this}delete(t){const n=tn(t),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[tn(t)];return n!==void 0&&(n=ns(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,tn(t))}*keys(){for(const t of Object.keys(this.obj))yield ca(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[ca(t[0],this._field.mapKey),ns(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield ns(this._field,t,this.check)}forEach(t,n){for(const s of this.entries())t.call(n,s[1],s[0],this)}}function Mr(e,t){return Nr(t)?zo(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?ui(t.message):t.message:t}function Or(e,t,n){return t!==void 0&&(zt(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:Pr(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&ze(t)&&(t=li(t))),new ii(e.message,t,n)}function ua(e,t){return e.listKind=="message"?Mr(e,t):Dr(e,t)}function ts(e,t,n){return e.listKind=="message"?Or(e,t,n):Pr(e,t)}function ns(e,t,n){return e.mapKind=="message"?Or(e,t,n):t}function tn(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function ca(e,t){switch(t){case _.STRING:return e;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case _.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case _.UINT64:case _.FIXED64:try{return L.uParse(e)}catch{}break;default:try{return L.parse(e)}catch{}}return e}function Pr(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=L.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=L.uParse(t))}return t}function Dr(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=L.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=L.uParse(t))}return t}function li(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(ze(e))for(const[n,s]of Object.entries(e))t.fields[n]=di(s);return t}function ui(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=ci(s);return t}function ci(e){switch(e.kind.case){case"structValue":return ui(e.kind.value);case"listValue":return e.kind.value.values.map(ci);case"nullValue":case void 0:return null;default:return e.kind.value}}function di(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const s of e)n.values.push(di(s));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:li(e)}}return t}function hi(e){const t=function(){if(!lt){lt=[];const u=pi("std");for(let l=0;l<u.length;l++)lt[u[l].charCodeAt(0)]=l;lt[45]=u.indexOf("+"),lt[95]=u.indexOf("/")}return lt}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let s,a=new Uint8Array(n),r=0,o=0,i=0;for(let u=0;u<e.length;u++){if(s=t[e.charCodeAt(u)],s===void 0)switch(e[u]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=s,o=1;break;case 1:a[r++]=i<<2|(48&s)>>4,i=s,o=2;break;case 2:a[r++]=(15&i)<<4|(60&s)>>2,i=s,o=3;break;case 3:a[r++]=(3&i)<<6|s,o=0}}if(o==1)throw Error("invalid base64 string");return a.subarray(0,r)}let nn,da,lt;function pi(e){return nn||(nn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),da=nn.slice(0,-2).concat("-","_")),e=="url"?da:nn}function Gt(e){let t=!1;const n=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);switch(a){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(a),t=!1;break;default:t&&(t=!1,a=a.toUpperCase()),n.push(a)}}return n.join("")}const Jl=new Set(["constructor","toString","toJSON","valueOf"]);function Vt(e){return Jl.has(e)?e+"$":e}function Lr(e){for(const t of e.field)Dt(t,"jsonName")||(t.jsonName=Gt(t.name));e.nestedType.forEach(Lr)}function Zl(e,t){switch(e){case _.STRING:return t;case _.BYTES:{const n=function(s){const a=[],r={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;r.next();)if(r.c==="\\"){if(r.next())switch(r.c){case"\\":a.push(r.c.charCodeAt(0));break;case"b":a.push(8);break;case"f":a.push(12);break;case"n":a.push(10);break;case"r":a.push(13);break;case"t":a.push(9);break;case"v":a.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=r.c,i=r.take(2);if(i===!1)return!1;const u=parseInt(o+i,8);if(Number.isNaN(u))return!1;a.push(u);break}case"x":{const o=r.c,i=r.take(2);if(i===!1)return!1;const u=parseInt(o+i,16);if(Number.isNaN(u))return!1;a.push(u);break}case"u":{const o=r.c,i=r.take(4);if(i===!1)return!1;const u=parseInt(o+i,16);if(Number.isNaN(u))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,u,!0),a.push(l[0],l[1],l[2],l[3]);break}case"U":{const o=r.c,i=r.take(8);if(i===!1)return!1;const u=L.uEnc(o+i),l=new Uint8Array(8),d=new DataView(l.buffer);d.setInt32(0,u.lo,!0),d.setInt32(4,u.hi,!0),a.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else a.push(r.c.charCodeAt(0));return new Uint8Array(a)}(t);if(n===!1)throw new Error(`cannot parse ${_[e]} default value: ${t}`);return n}case _.INT64:case _.SFIXED64:case _.SINT64:return L.parse(t);case _.UINT64:case _.FIXED64:return L.uParse(t);case _.DOUBLE:case _.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case _.BOOL:return t==="true";case _.INT32:case _.UINT32:case _.SINT32:case _.FIXED32:case _.SFIXED32:return parseInt(t,10)}}function*Ds(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*Ds(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*Ds(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function mi(...e){const t=function(){const n=new Map,s=new Map,a=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return a.values()},addFile(r,o,i){if(a.set(r.proto.name,r),!o)for(const u of Ds(r))this.add(u);if(i)for(const u of r.dependencies)this.addFile(u,o,i)},add(r){if(r.kind=="extension"){let o=s.get(r.extendee.typeName);o||s.set(r.extendee.typeName,o=new Map),o.set(r.number,r)}n.set(r.typeName,r)},get:r=>n.get(r),getFile:r=>a.get(r),getMessage(r){const o=n.get(r);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(r){const o=n.get(r);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(r){const o=n.get(r);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(r,o){var i;return(i=s.get(r.typeName))===null||i===void 0?void 0:i.get(o)},getService(r){const o=n.get(r);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)ma(n,t);return t}if("$typeName"in e[0]){let r=function(o){const i=[];for(const u of o.dependency){if(t.getFile(u)!=null||a.has(u))continue;const l=s(u);if(!l)throw new Error(`Unable to resolve ${u}, imported by ${o.name}`);"kind"in l?t.addFile(l,!1,!0):(a.add(l.name),i.push(l))}return i.concat(...i.map(r))};const n=e[0],s=e[1],a=new Set;for(const o of[n,...r(n)].reverse())ma(o,t)}else for(const n of e)for(const s of n.files)t.addFile(s);return t}const Ql=998,eu=999,tu=9,fn=10,xt=11,nu=12,ha=14,Ls=3,su=2,pa=1,ru=0,au=1,ou=2,iu=3,lu=1,uu=2,cu=1,gi={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function ma(e,t){var n,s;const a={kind:"file",proto:e,deprecated:(s=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:pu(e),name:e.name.replace(/\.proto$/,""),dependencies:mu(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},r=new Map,o={get:i=>r.get(i),add(i){var u;Se(((u=i.proto.options)===null||u===void 0?void 0:u.mapEntry)===!0),r.set(i.typeName,i)}};for(const i of e.enumType)fi(i,a,void 0,t);for(const i of e.messageType)yi(i,a,void 0,t,o);for(const i of e.service)du(i,a,t);Fs(a,t);for(const i of r.values())Us(i,t,o);for(const i of a.messages)Us(i,t,o),Fs(i,t);t.addFile(a,!0)}function Fs(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const s=$s(n,e,t);e.extensions.push(s),t.add(s)}break;case"message":for(const n of e.proto.extension){const s=$s(n,e,t);e.nestedExtensions.push(s),t.add(s)}for(const n of e.nestedMessages)Fs(n,t)}}function Us(e,t,n){const s=e.proto.oneofDecl.map(r=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:Vt(Gt(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(r,e)),a=new Set;for(const r of e.proto.field){const o=gu(r,s),i=$s(r,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),a.has(o)||(a.add(o),e.members.push(o)))}for(const r of s.filter(o=>a.has(o)))e.oneofs.push(r);for(const r of e.nestedMessages)Us(r,t,n)}function fi(e,t,n,s){var a,r,o,i,u;const l=function(c,h){const p=(m=c,(m.substring(0,1)+m.substring(1).replace(/[A-Z]/g,y=>"_"+y)).toLowerCase()+"_");var m;for(const y of h){if(!y.name.toLowerCase().startsWith(p))return;const f=y.name.substring(p.length);if(f.length==0||/^\d/.test(f))return}return p}(e.name,e.value),d={kind:"enum",proto:e,deprecated:(r=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&r!==void 0&&r,file:t,parent:n,open:!0,name:e.name,typeName:jn(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};d.open=function(c){var h;return cu==vt("enumType",{proto:c.proto,parent:(h=c.parent)!==null&&h!==void 0?h:c.file})}(d),s.add(d);for(const c of e.value){const h=c.name;d.values.push(d.value[c.number]={kind:"enum_value",proto:c,deprecated:(i=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:d,name:h,localName:Vt(l==null?h:h.substring(l.length)),number:c.number,toString:()=>`enum value ${d.typeName}.${h}`})}((u=n==null?void 0:n.nestedEnums)!==null&&u!==void 0?u:t.enums).push(d)}function yi(e,t,n,s,a){var r,o,i,u;const l={kind:"message",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:jn(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?a.add(l):(((u=n==null?void 0:n.nestedMessages)!==null&&u!==void 0?u:t.messages).push(l),s.add(l));for(const d of e.enumType)fi(d,t,l,s);for(const d of e.nestedType)yi(d,t,l,s,a)}function du(e,t,n){var s,a;const r={kind:"service",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,file:t,name:e.name,typeName:jn(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(r),n.add(r);for(const o of e.method){const i=hu(o,r,n);r.methods.push(i),r.method[i.localName]=i}}function hu(e,t,n){var s,a,r,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const u=n.getMessage(Fe(e.inputType)),l=n.getMessage(Fe(e.outputType));Se(u,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),Se(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const d=e.name;return{kind:"rpc",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,parent:t,name:d,localName:Vt(d.length?Vt(d[0].toLowerCase()+d.substring(1)):d),methodKind:i,input:u,output:l,idempotency:(o=(r=e.options)===null||r===void 0?void 0:r.idempotencyLevel)!==null&&o!==void 0?o:ru,toString:()=>`rpc ${t.typeName}.${d}`}}function $s(e,t,n,s,a){var r,o,i;const u=a===void 0,l={kind:"field",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:fu(e,s,u,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(u){const p=t.kind=="file"?t:t.file,m=t.kind=="file"?void 0:t,y=jn(e,m,p);l.kind="extension",l.file=p,l.parent=m,l.oneof=void 0,l.typeName=y,l.jsonName=`[${y}]`,l.toString=()=>`extension ${y}`;const f=n.getMessage(Fe(e.extendee));Se(f,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=f}else{const p=t;Se(p.kind=="message"),l.parent=p,l.oneof=s,l.localName=s?Gt(e.name):Vt(Gt(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${p.typeName}.${e.name}`}const d=e.label,c=e.type,h=(i=e.options)===null||i===void 0?void 0:i.jstype;if(d===Ls){const p=c==xt?a==null?void 0:a.get(Fe(e.typeName)):void 0;if(p){l.fieldKind="map";const{key:m,value:y}=function(f){const b=f.fields.find(v=>v.number===1),S=f.fields.find(v=>v.number===2);return Se(b&&b.fieldKind=="scalar"&&b.scalar!=_.BYTES&&b.scalar!=_.FLOAT&&b.scalar!=_.DOUBLE&&S&&S.fieldKind!="list"&&S.fieldKind!="map"),{key:b,value:S}}(p);return l.mapKey=m.scalar,l.mapKind=y.fieldKind,l.message=y.message,l.delimitedEncoding=!1,l.enum=y.enum,l.scalar=y.scalar,l}switch(l.fieldKind="list",c){case xt:case fn:l.listKind="message",l.message=n.getMessage(Fe(e.typeName)),Se(l.message),l.delimitedEncoding=ga(e,t);break;case ha:l.listKind="enum",l.enum=n.getEnum(Fe(e.typeName)),Se(l.enum);break;default:l.listKind="scalar",l.scalar=c,l.longAsString=h==pa}return l.packed=function(m,y){if(m.label!=Ls)return!1;switch(m.type){case tu:case nu:case fn:case xt:return!1}const f=m.options;return f&&Dt(f,"packed")?f.packed:lu==vt("repeatedFieldEncoding",{proto:m,parent:y})}(e,t),l}switch(c){case xt:case fn:l.fieldKind="message",l.message=n.getMessage(Fe(e.typeName)),Se(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=ga(e,t),l.getDefaultValue=()=>{};break;case ha:{const p=n.getEnum(Fe(e.typeName));Se(p!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(Fe(e.typeName)),l.getDefaultValue=()=>Dt(e,"defaultValue")?function(m,y){const f=m.values.find(b=>b.name===y);if(!f)throw new Error(`cannot parse ${m} default value: ${y}`);return f.number}(p,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=c,l.longAsString=h==pa,l.getDefaultValue=()=>Dt(e,"defaultValue")?Zl(c,e.defaultValue):void 0}return l}function pu(e){switch(e.syntax){case"":case"proto2":return Ql;case"proto3":return eu;case"editions":if(e.edition in gi)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function mu(e,t){return e.dependency.map(n=>{const s=t.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return s})}function jn(e,t,n){let s;return s=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,s}function Fe(e){return e.startsWith(".")?e.substring(1):e}function gu(e,t){if(!Dt(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return Se(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function fu(e,t,n,s){return e.label==su?iu:e.label==Ls?ou:t||e.proto3Optional||e.type==xt||n?au:vt("fieldPresence",{proto:e,parent:s})}function ga(e,t){return e.type==fn||uu==vt("messageEncoding",{proto:e,parent:t})}function vt(e,t){var n,s;const a=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(a){const r=a[e];if(r!=0)return r}if("kind"in t){if(t.kind=="message")return vt(e,(s=t.parent)!==null&&s!==void 0?s:t.file);const r=gi[t.edition];if(!r)throw new Error(`feature default for edition ${t.edition} not found`);return r[e]}return vt(e,t.parent)}function Se(e,t){if(!e)throw new Error(t)}function yu(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(_i),enumType:n.enumType.map(bi)}))}(e);return t.messageType.forEach(Lr),mi(t,()=>{}).getFile(t.name)}function _i(e){var t,n,s,a,r,o,i,u;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(_u))!==null&&n!==void 0?n:[],extension:[],nestedType:(a=(s=e.nestedType)===null||s===void 0?void 0:s.map(_i))!==null&&a!==void 0?a:[],enumType:(o=(r=e.enumType)===null||r===void 0?void 0:r.map(bi))!==null&&o!==void 0?o:[],extensionRange:(u=(i=e.extensionRange)===null||i===void 0?void 0:i.map(l=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},l)))!==null&&u!==void 0?u:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function _u(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?bu(e.options):void 0}))}function bu(e){var t,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(s=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(r=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},r)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function bi(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function Xt(e,t,...n){return n.reduce((s,a)=>s.nestedMessages[a],e.messages[t])}const vu=Xt(yu({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var fa,ya,_a,ba,va,Sa,Ea,Ta,wa,Ia,Na,ka,Ca,xa,Aa,Ra,Ma,Oa;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(fa||(fa={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(ya||(ya={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(_a||(_a={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(ba||(ba={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(va||(va={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(Sa||(Sa={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(Ea||(Ea={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Ta||(Ta={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(wa||(wa={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(Ia||(Ia={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(Na||(Na={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(ka||(ka={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(Ca||(Ca={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(xa||(xa={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(Aa||(Aa={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(Ra||(Ra={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(Ma||(Ma={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(Oa||(Oa={}));const Su={readUnknownFields:!0};function Fr(e,t,n){const s=me(e,void 0,!1);return vi(s,new Ar(t),Su,!1,t.byteLength),s.message}function vi(e,t,n,s,a){var r;const o=s?t.len:t.pos+a;let i,u;const l=(r=e.getUnknown())!==null&&r!==void 0?r:[];for(;t.pos<o&&([i,u]=t.tag(),!s||u!=$.EndGroup);){const d=e.findNumber(i);if(d)Si(e,t,d,u,n);else{const c=t.skip(u,i);n.readUnknownFields&&l.push({no:i,wireType:u,data:c})}}if(s&&(u!=$.EndGroup||i!==a))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function Si(e,t,n,s,a){switch(n.fieldKind){case"scalar":e.set(n,ut(t,n.scalar));break;case"enum":e.set(n,ut(t,_.INT32));break;case"message":e.set(n,ss(t,a,n,e.get(n)));break;case"list":(function(r,o,i,u){var l;const d=i.field();if(d.listKind==="message")return void i.add(ss(r,u,d));const c=(l=d.scalar)!==null&&l!==void 0?l:_.INT32;if(!(o==$.LengthDelimited&&c!=_.STRING&&c!=_.BYTES))return void i.add(ut(r,c));const p=r.uint32()+r.pos;for(;r.pos<p;)i.add(ut(r,c))})(t,s,e.get(n),a);break;case"map":(function(r,o,i){const u=o.field();let l,d;const c=r.pos+r.uint32();for(;r.pos<c;){const[h]=r.tag();switch(h){case 1:l=ut(r,u.mapKey);break;case 2:switch(u.mapKind){case"scalar":d=ut(r,u.scalar);break;case"enum":d=r.int32();break;case"message":d=ss(r,i,u)}}}if(l===void 0&&(l=st(u.mapKey,!1)),d===void 0)switch(u.mapKind){case"scalar":d=st(u.scalar,!1);break;case"enum":d=u.enum.values[0].number;break;case"message":d=me(u.message,void 0,!1)}o.set(l,d)})(t,e.get(n),a)}}function ss(e,t,n,s){const a=n.delimitedEncoding,r=s??me(n.message,void 0,!1);return vi(r,e,t,a,a?n.number:e.uint32()),r}function ut(e,t){switch(t){case _.STRING:return e.string();case _.BOOL:return e.bool();case _.DOUBLE:return e.double();case _.FLOAT:return e.float();case _.INT32:return e.int32();case _.INT64:return e.int64();case _.UINT64:return e.uint64();case _.FIXED64:return e.fixed64();case _.BYTES:return e.bytes();case _.FIXED32:return e.fixed32();case _.SFIXED32:return e.sfixed32();case _.SFIXED64:return e.sfixed64();case _.SINT64:return e.sint64();case _.UINT32:return e.uint32();case _.SINT32:return e.sint32()}}function Ur(e,t){const n=Fr(vu,hi(e));return n.messageType.forEach(Lr),n.dependency=[],mi(n,s=>{}).getFile(n.name)}const Eu=Xt(Ur("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),Tu=3,Pa={writeUnknownFields:!0};function wu(e,t,n){return kn(new ni,function(s){return s?Object.assign(Object.assign({},Pa),s):Pa}(n),me(e,t)).finish()}function kn(e,t,n){var s;for(const a of n.sortedFields)if(n.isSet(a))Ei(e,t,n,a);else if(a.presence==Tu)throw new Error(`cannot encode ${a} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:a,wireType:r,data:o}of(s=n.getUnknown())!==null&&s!==void 0?s:[])e.tag(a,r).raw(o);return e}function Ei(e,t,n,s){var a;switch(s.fieldKind){case"scalar":case"enum":Cn(e,n.desc.typeName,s.name,(a=s.scalar)!==null&&a!==void 0?a:_.INT32,s.number,n.get(s));break;case"list":(function(r,o,i,u){var l;if(i.listKind=="message"){for(const c of u)Da(r,o,i,c);return}const d=(l=i.scalar)!==null&&l!==void 0?l:_.INT32;if(i.packed){if(!u.size)return;r.tag(i.number,$.LengthDelimited).fork();for(const c of u)Ti(r,i.parent.typeName,i.name,d,c);return void r.join()}for(const c of u)Cn(r,i.parent.typeName,i.name,d,i.number,c)})(e,t,s,n.get(s));break;case"message":Da(e,t,s,n.get(s));break;case"map":for(const[r,o]of n.get(s))Iu(e,t,s,r,o)}}function Cn(e,t,n,s,a,r){Ti(e.tag(a,function(o){switch(o){case _.BYTES:case _.STRING:return $.LengthDelimited;case _.DOUBLE:case _.FIXED64:case _.SFIXED64:return $.Bit64;case _.FIXED32:case _.SFIXED32:case _.FLOAT:return $.Bit32;default:return $.Varint}}(s)),t,n,s,r)}function Da(e,t,n,s){n.delimitedEncoding?kn(e.tag(n.number,$.StartGroup),t,s).tag(n.number,$.EndGroup):kn(e.tag(n.number,$.LengthDelimited).fork(),t,s).join()}function Iu(e,t,n,s,a){var r;switch(e.tag(n.number,$.LengthDelimited).fork(),Cn(e,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":Cn(e,n.parent.typeName,n.name,(r=n.scalar)!==null&&r!==void 0?r:_.INT32,2,a);break;case"message":kn(e.tag(2,$.LengthDelimited).fork(),t,a).join()}e.join()}function Ti(e,t,n,s,a){try{switch(s){case _.STRING:e.string(a);break;case _.BOOL:e.bool(a);break;case _.DOUBLE:e.double(a);break;case _.FLOAT:e.float(a);break;case _.INT32:e.int32(a);break;case _.INT64:e.int64(a);break;case _.UINT64:e.uint64(a);break;case _.FIXED64:e.fixed64(a);break;case _.BYTES:e.bytes(a);break;case _.FIXED32:e.fixed32(a);break;case _.SFIXED32:e.sfixed32(a);break;case _.SFIXED64:e.sfixed64(a);break;case _.SINT64:e.sint64(a);break;case _.UINT32:e.uint32(a);break;case _.SINT32:e.sint32(a)}}catch(r){throw r instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${r.message}`):r}}function Nu(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(La(e.typeUrl));return n&&function(s,a){return s.typeUrl!==""&&(typeof a=="string"?a:a.typeName)===La(s.typeUrl)}(e,n)?Fr(n,e.value):void 0}function La(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const $r=Ur("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),ku=Xt($r,0),wi=Xt($r,1),Cu=Xt($r,2);var qs;function xu(e,t){Ii(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let u=o.length-1;u>=0;--u)if(o[u].no==i.number)return[o[u]];return[]}return o.filter(u=>u.no===i.number)}(e.$unknown,t),[s,a,r]=Yn(t);for(const o of n)Si(s,new Ar(o.data),a,o.wireType,{readUnknownFields:!0});return r()}function Au(e,t,n){var s;Ii(t,e);const a=((s=e.$unknown)!==null&&s!==void 0?s:[]).filter(l=>l.no!==t.number),[r,o]=Yn(t,n),i=new ni;Ei(i,{writeUnknownFields:!0},r,o);const u=new Ar(i.finish());for(;u.pos<u.len;){const[l,d]=u.tag(),c=u.skip(d,l);a.push({no:l,wireType:d,data:c})}e.$unknown=a}function Yn(e,t){const n=e.typeName,s=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),a=Object.assign(Object.assign({},e.extendee),{fields:[s],members:[s],oneofs:[]}),r=Ee(a,t!==void 0?{[n]:t}:void 0);return[me(a,r),s,()=>{const o=r[n];if(o===void 0){const i=e.message;return zt(i)?st(i.fields[0].scalar,i.fields[0].longAsString):Ee(i)}return o}]}function Ii(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(qs||(qs={}));const Ru=3,Mu=2,Fa={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function Ou(e,t,n){return Lt(me(e,t),function(s){return s?Object.assign(Object.assign({},Fa),s):Fa}(n))}function Lt(e,t){var n;const s=function(r,o){if(r.desc.typeName.startsWith("google.protobuf.")){switch(r.desc.typeName){case"google.protobuf.Any":return function(u,l){if(u.typeUrl==="")return{};const{registry:d}=l;let c,h;if(d&&(c=Nu(u,d),c&&(h=d.getMessage(c.$typeName))),!h||!c)throw new Error(`cannot encode message ${u.$typeName} to JSON: "${u.typeUrl}" is not in the type registry`);let p=Lt(me(h,c),l);return(h.typeName.startsWith("google.protobuf.")||p===null||Array.isArray(p)||typeof p!="object")&&(p={value:p}),p["@type"]=u.typeUrl,p}(r.message,o);case"google.protobuf.Timestamp":return function(u){const l=1e3*Number(u.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${u.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(u.nanos<0)throw new Error(`cannot encode message ${u.$typeName} to JSON: nanos must not be negative`);let d="Z";if(u.nanos>0){const c=(u.nanos+1e9).toString().substring(1);d=c.substring(3)==="000000"?"."+c.substring(0,3)+"Z":c.substring(6)==="000"?"."+c.substring(0,6)+"Z":"."+c+"Z"}return new Date(l).toISOString().replace(".000Z",d)}(r.message);case"google.protobuf.Duration":return function(u){if(Number(u.seconds)>315576e6||Number(u.seconds)<-315576e6)throw new Error(`cannot encode message ${u.$typeName} to JSON: value out of range`);let l=u.seconds.toString();if(u.nanos!==0){let d=Math.abs(u.nanos).toString();d="0".repeat(9-d.length)+d,d.substring(3)==="000000"?d=d.substring(0,3):d.substring(6)==="000"&&(d=d.substring(0,6)),l+="."+d,u.nanos<0&&Number(u.seconds)==0&&(l="-"+l)}return l+"s"}(r.message);case"google.protobuf.FieldMask":return(i=r.message).paths.map(u=>{if(u.match(/_[0-9]?_/g)||u.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+u+'" is irreversible');return Gt(u)}).join(",");case"google.protobuf.Struct":return Ni(r.message);case"google.protobuf.Value":return qr(r.message);case"google.protobuf.ListValue":return ki(r.message);default:if(zt(r.desc)){const u=r.desc.fields[0];return yn(u,r.get(u))}return}var i}}(e,t);if(s!==void 0)return s;const a={};for(const r of e.sortedFields){if(!e.isSet(r)){if(r.presence==Ru)throw new Error(`cannot encode ${r} to JSON: required field not set`);if(!t.alwaysEmitImplicit||r.presence!==Mu)continue}const o=Ua(r,e.get(r),t);o!==void 0&&(a[Pu(r,t)]=o)}if(t.registry){const r=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!r.has(o)){r.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const u=xu(e.message,i),[l,d]=Yn(i,u),c=Ua(d,l.get(d),t);c!==void 0&&(a[i.jsonName]=c)}}return a}function Ua(e,t,n){switch(e.fieldKind){case"scalar":return yn(e,t);case"message":return Lt(t,n);case"enum":return rs(e.enum,t,n.enumAsInteger);case"list":return function(s,a){const r=s.field(),o=[];switch(r.listKind){case"scalar":for(const i of s)o.push(yn(r,i));break;case"enum":for(const i of s)o.push(rs(r.enum,i,a.enumAsInteger));break;case"message":for(const i of s)o.push(Lt(i,a))}return a.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(s,a){const r=s.field(),o={};switch(r.mapKind){case"scalar":for(const[i,u]of s)o[i]=yn(r,u);break;case"message":for(const[i,u]of s)o[i]=Lt(u,a);break;case"enum":for(const[i,u]of s)o[i]=rs(r.enum,u,a.enumAsInteger)}return a.alwaysEmitImplicit||s.size>0?o:void 0}(t,n)}}function rs(e,t,n){var s;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${G(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const a=e.value[t];return(s=a==null?void 0:a.name)!==null&&s!==void 0?s:t}function yn(e,t){var n,s,a,r,o,i;switch(e.scalar){case _.INT32:case _.SFIXED32:case _.SINT32:case _.FIXED32:case _.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=Xe(e,t))===null||n===void 0?void 0:n.message}`);return t;case _.FLOAT:case _.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(s=Xe(e,t))===null||s===void 0?void 0:s.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case _.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(a=Xe(e,t))===null||a===void 0?void 0:a.message}`);return t;case _.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(r=Xe(e,t))===null||r===void 0?void 0:r.message}`);return t;case _.UINT64:case _.FIXED64:case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=Xe(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case _.BYTES:if(t instanceof Uint8Array)return function(u,l="std"){const d=pi(l),c=l=="std";let h,p="",m=0,y=0;for(let f=0;f<u.length;f++)switch(h=u[f],m){case 0:p+=d[h>>2],y=(3&h)<<4,m=1;break;case 1:p+=d[y|h>>4],y=(15&h)<<2,m=2;break;case 2:p+=d[y|h>>6],p+=d[63&h],m=0}return m&&(p+=d[y],c&&(p+="=",m==1&&(p+="="))),p}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=Xe(e,t))===null||i===void 0?void 0:i.message}`)}}function Pu(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Ni(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=qr(s);return t}function qr(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Ni(e.kind.value);case"listValue":return ki(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function ki(e){return e.values.map(qr)}const $a={ignoreUnknownFields:!1};function Du(e,t,n){const s=me(e);try{bt(s,t,function(r){return r?Object.assign(Object.assign({},$a),r):$a}(n))}catch(r){throw(a=r)instanceof Error&&Wl.includes(a.name)&&"field"in a&&typeof a.field=="function"?new Error(`cannot decode ${r.field()} from JSON: ${r.message}`,{cause:r}):r}var a;return s.message}function bt(e,t,n){var s;if(function(o,i,u){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(l,d,c){var h;if(d===null||Array.isArray(d)||typeof d!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${G(d)}`);if(Object.keys(d).length==0)return;const p=d["@type"];if(typeof p!="string"||p=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const m=p.includes("/")?p.substring(p.lastIndexOf("/")+1):p;if(!m.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const y=(h=c.registry)===null||h===void 0?void 0:h.getMessage(m);if(!y)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${p} is not in the type registry`);const f=me(y);if(m.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(d,"value"))bt(f,d.value,c);else{const b=Object.assign({},d);delete b["@type"],bt(f,b,c)}(function(b,S,v){let T=!1;v||(v=Ee(Eu),T=!0),v.value=wu(b,S),v.typeUrl=`type.googleapis.com/${S.$typeName}`})(f.desc,f.message,l)}(o.message,i,u),!0;case"google.protobuf.Timestamp":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const c=d.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!c)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const h=Date.parse(c[1]+"-"+c[2]+"-"+c[3]+"T"+c[4]+":"+c[5]+":"+c[6]+(c[8]?c[8]:"Z"));if(Number.isNaN(h))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(h<Date.parse("0001-01-01T00:00:00Z")||h>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=L.parse(h/1e3),l.nanos=0,c[7]&&(l.nanos=parseInt("1"+c[7]+"0".repeat(9-c[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const c=d.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(c===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const h=Number(c[1]);if(h>315576e6||h<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);if(l.seconds=L.parse(h),typeof c[2]!="string")return;const p=c[2]+"0".repeat(9-c[2].length);l.nanos=parseInt(p),(h<0||Object.is(h,-0))&&(l.nanos=-l.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);if(d==="")return;function c(h){if(h.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const p=h.replace(/[A-Z]/g,m=>"_"+m.toLowerCase());return p[0]==="_"?p.substring(1):p}l.paths=d.split(",").map(c)}(o.message,i),!0;case"google.protobuf.Struct":return xi(o.message,i),!0;case"google.protobuf.Value":return Hr(o.message,i),!0;case"google.protobuf.ListValue":return Ai(o.message,i),!0;default:if(zt(o.desc)){const l=o.desc.fields[0];return i===null?o.clear(l):o.set(l,bn(l,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${G(t)}`);const a=new Map,r=new Map;for(const o of e.desc.fields)r.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const u=r.get(o);if(u){if(u.oneof){if(i===null&&u.fieldKind=="scalar")continue;const l=a.get(u.oneof);if(l!==void 0)throw new ie(u.oneof,`oneof set multiple times by ${l.name} and ${u.name}`);a.set(u.oneof,u)}qa(e,u,i,n)}else{let l;if(o.startsWith("[")&&o.endsWith("]")&&(l=(s=n.registry)===null||s===void 0?void 0:s.getExtension(o.substring(1,o.length-1)))&&l.extendee.typeName===e.desc.typeName){const[d,c,h]=Yn(l);qa(d,c,i,n),Au(e.message,l,h())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function qa(e,t,n,s){switch(t.fieldKind){case"scalar":(function(a,r,o){const i=bn(r,o,!1);i===xn?a.clear(r):a.set(r,i)})(e,t,n);break;case"enum":(function(a,r,o,i){const u=as(r.enum,o,i.ignoreUnknownFields,!1);u===xn?a.clear(r):u!==_n&&a.set(r,u)})(e,t,n,s);break;case"message":(function(a,r,o,i){if(o===null&&r.message.typeName!="google.protobuf.Value")return void a.clear(r);const u=a.isSet(r)?a.get(r):me(r.message);bt(u,o,i),a.set(r,u)})(e,t,n,s);break;case"list":(function(a,r,o){if(r===null)return;const i=a.field();if(!Array.isArray(r))throw new ie(i,"expected Array, got "+G(r));for(const u of r){if(u===null)throw new ie(i,"list item must not be null");switch(i.listKind){case"message":const l=me(i.message);bt(l,u,o),a.add(l);break;case"enum":const d=as(i.enum,u,o.ignoreUnknownFields,!0);d!==_n&&a.add(d);break;case"scalar":a.add(bn(i,u,!0))}}})(e.get(t),n,s);break;case"map":(function(a,r,o){if(r===null)return;const i=a.field();if(typeof r!="object"||Array.isArray(r))throw new ie(i,"expected object, got "+G(r));for(const[u,l]of Object.entries(r)){if(l===null)throw new ie(i,"map value must not be null");let d;switch(i.mapKind){case"message":const h=me(i.message);bt(h,l,o),d=h;break;case"enum":if(d=as(i.enum,l,o.ignoreUnknownFields,!0),d===_n)return;break;case"scalar":d=bn(i,l,!0)}const c=Lu(i.mapKey,u);a.set(c,d)}})(e.get(t),n,s)}}const _n=Symbol();function as(e,t,n,s){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:s?e.values[0].number:xn;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const a=e.values.find(r=>r.name===t);if(a!==void 0)return a.number;if(n)return _n}throw new Error(`cannot decode ${e} from JSON: ${G(t)}`)}const xn=Symbol();function bn(e,t,n){if(t===null)return n?st(e.scalar,!1):xn;switch(e.scalar){case _.DOUBLE:case _.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new ie(e,"unexpected NaN number");if(!Number.isFinite(t))throw new ie(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const s=Number(t);if(!Number.isFinite(s))break;return s}break;case _.INT32:case _.FIXED32:case _.SFIXED32:case _.SINT32:case _.UINT32:return Ci(t);case _.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return hi(t)}catch(s){const a=s instanceof Error?s.message:String(s);throw new ie(e,a)}}}return t}function Lu(e,t){switch(e){case _.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:return Ci(t);default:return t}}function Ci(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function xi(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`);for(const[n,s]of Object.entries(t)){const a=Ee(wi);Hr(a,s),e.fields[n]=a}}function Hr(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:qs.NULL_VALUE};else if(Array.isArray(t)){const n=Ee(Cu);Ai(n,t),e.kind={case:"listValue",value:n}}else{const n=Ee(ku);xi(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`)}return e}function Ai(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`);for(const n of t){const s=Ee(wi);Hr(s,n),e.values.push(s)}}class Ri{constructor(t){g(this,"target");g(this,"pendingRequests",new Map);g(this,"cleanup");g(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(s){const a=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${a}`))}}sendRequest(t,n){return new Promise((s,a)=>{let r;n&&(r=setTimeout(()=>{this.pendingRequests.delete(t.id),a(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:s,reject:a,timeout:r}),this.target.sendMessage(t)})}async unary(t,n,s,a,r,o){const i=crypto.randomUUID(),u=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const d=r?Ou(t.input,Ee(t.input,r)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${u} (ID: ${i})`);let c;n&&(c=()=>{const p=this.pendingRequests.get(i);p&&(this.pendingRequests.delete(i),clearTimeout(p.timeout),p.reject(new Error(`gRPC request aborted during execution: ${l}.${u} (ID: ${i})`)))},n.addEventListener("abort",c));const h=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:u,serviceTypeName:l,data:d,timeout:s},s);return n&&c&&n.removeEventListener("abort",c),{stream:!1,method:t,service:t.parent,header:new Headers(a),message:Du(t.output,h.data),trailer:new Headers}}stream(t,n,s,a,r,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}g(Ri,"PROTOCOL_NAME","com.augmentcode.client.rpc");var Ke;function Ha(e){const t=Ke[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(Ke||(Ke={}));class Ge extends Error{constructor(t,n=Ke.Unknown,s,a,r){super(function(o,i){return o.length?`[${Ha(i)}] ${o}`:`[${Ha(i)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(s??{}),this.details=a??[],this.cause=r}static from(t,n=Ke.Unknown){return t instanceof Ge?t:t instanceof Error?t.name=="AbortError"?new Ge(t.message,Ke.Canceled):new Ge(t.message,n,void 0,void 0,t):new Ge(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===Ge.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:a=>a===t.typeName?t:void 0}:t,s=[];for(const a of this.details){if("desc"in a){n.getMessage(a.desc.typeName)&&s.push(Ee(a.desc,a.value));continue}const r=n.getMessage(a.type);if(r)try{s.push(Fr(r,a.value))}catch{}}return s}}var Fu=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(a){t[a]=e[a]&&function(r){return new Promise(function(o,i){(function(u,l,d,c){Promise.resolve(c).then(function(h){u({value:h,done:d})},l)})(o,i,(r=e[a](r)).done,r.value)})}}},jt=function(e){return this instanceof jt?(this.v=e,this):new jt(e)},Uu=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=n.apply(e,t||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(c){return function(h){return Promise.resolve(h).then(c,l)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(c,h){a[c]&&(s[c]=function(p){return new Promise(function(m,y){r.push([c,p,m,y])>1||i(c,p)})},h&&(s[c]=h(s[c])))}function i(c,h){try{(p=a[c](h)).value instanceof jt?Promise.resolve(p.value.v).then(u,l):d(r[0][2],p)}catch(m){d(r[0][3],m)}var p}function u(c){i("next",c)}function l(c){i("throw",c)}function d(c,h){c(h),r.shift(),r.length&&i(r[0][0],r[0][1])}},$u=function(e){var t,n;return t={},s("next"),s("throw",function(a){throw a}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(a,r){t[a]=e[a]?function(o){return(n=!n)?{value:jt(e[a](o)),done:!1}:r?r(o):o}:r}},Mi=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(a){t[a]=e[a]&&function(r){return new Promise(function(o,i){(function(u,l,d,c){Promise.resolve(c).then(function(h){u({value:h,done:d})},l)})(o,i,(r=e[a](r)).done,r.value)})}}},St=function(e){return this instanceof St?(this.v=e,this):new St(e)},qu=function(e){var t,n;return t={},s("next"),s("throw",function(a){throw a}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(a,r){t[a]=e[a]?function(o){return(n=!n)?{value:St(e[a](o)),done:!1}:r?r(o):o}:r}},Hu=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=n.apply(e,t||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(c){return function(h){return Promise.resolve(h).then(c,l)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(c,h){a[c]&&(s[c]=function(p){return new Promise(function(m,y){r.push([c,p,m,y])>1||i(c,p)})},h&&(s[c]=h(s[c])))}function i(c,h){try{(p=a[c](h)).value instanceof St?Promise.resolve(p.value.v).then(u,l):d(r[0][2],p)}catch(m){d(r[0][3],m)}var p}function u(c){i("next",c)}function l(c){i("throw",c)}function d(c,h){c(h),r.shift(),r.length&&i(r[0][0],r[0][1])}};function Bu(e,t){return function(n,s){const a={};for(const r of n.methods){const o=s(r);o!=null&&(a[r.localName]=o)}return a}(e,n=>{switch(n.methodKind){case"unary":return function(s,a){return async function(r,o){var i,u;const l=await s.unary(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,l.header),(u=o==null?void 0:o.onTrailer)===null||u===void 0||u.call(o,l.trailer),l.message}}(t,n);case"server_streaming":return function(s,a){return function(r,o){return Ba(s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return Uu(this,arguments,function*(){yield jt(yield*$u(Fu(i)))})}([r]),o==null?void 0:o.contextValues),o)}}(t,n);case"client_streaming":return function(s,a){return async function(r,o){var i,u,l,d,c,h;const p=await s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues);let m;(c=o==null?void 0:o.onHeader)===null||c===void 0||c.call(o,p.header);let y=0;try{for(var f,b=!0,S=Mi(p.message);!(i=(f=await S.next()).done);b=!0)d=f.value,b=!1,m=d,y++}catch(v){u={error:v}}finally{try{b||i||!(l=S.return)||await l.call(S)}finally{if(u)throw u.error}}if(!m)throw new Ge("protocol error: missing response message",Ke.Unimplemented);if(y>1)throw new Ge("protocol error: received extra messages for client streaming method",Ke.Unimplemented);return(h=o==null?void 0:o.onTrailer)===null||h===void 0||h.call(o,p.trailer),m}}(t,n);case"bidi_streaming":return function(s,a){return function(r,o){return Ba(s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues),o)}}(t,n);default:return null}})}function Ba(e,t){const n=function(){return Hu(this,arguments,function*(){var s,a;const r=yield St(e);(s=t==null?void 0:t.onHeader)===null||s===void 0||s.call(t,r.header),yield St(yield*qu(Mi(r.message))),(a=t==null?void 0:t.onTrailer)===null||a===void 0||a.call(t,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}function Gu(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}async function Ga(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var Oi=(e=>(e.chat="chat",e))(Oi||{}),Pi=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(Pi||{});function Va(e){return e.replace(/^data:.*?;base64,/,"")}async function os(e){return new Promise((t,n)=>{const s=new FileReader;s.onload=a=>{var r;return t((r=a.target)==null?void 0:r.result)},s.onerror=n,s.readAsDataURL(e)})}async function is(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(a){a.data.error?n(new Error(a.data.error)):t(a.data),s.terminate()},s.onerror=function(a){n(a.error),s.terminate()},s.postMessage(e)})}const Vu=Gu(Ur("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var j=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(j||{}),vn=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(vn||{}),Sn=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(Sn||{});class ju{constructor(t=[]){g(this,"_items",[]);g(this,"_focusedItemIdx");g(this,"_subscribers",new Set);g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});g(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const n=t?this._items.indexOf(t):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});g(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-n)%this._items.length,this.notifySubscribers()});g(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));g(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});g(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});g(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});g(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});g(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Yu=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Yu||{}),Ku=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Ku||{});function Wu(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Di{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Wu(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var zu=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(zu||{}),Xu=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Xu||{});class Li extends Di{constructor(){super()}static create(){return new Li}}var Ju=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalTimedOutWaitingForStartupCommand="vs-code-terminal-timed-out-waiting-for-startup-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e.modelSelectionChange="model-selection-change",e))(Ju||{}),An=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(An||{}),Zu=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Zu||{});class Fi extends Di{constructor(){super()}static create(){return new Fi}}var Qu=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(Qu||{}),ec=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(ec||{}),tc=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(tc||{}),nc=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(nc||{}),sc=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(sc||{}),rc=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(rc||{}),ac=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(ac||{}),oc=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(oc||{}),ic=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(ic||{}),lc=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(lc||{}),uc=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(uc||{}),cc=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(cc||{});function dc(e,t,n=1e3){let s=null,a=0;const r=Bt(t),o=()=>{const i=(()=>{const u=Date.now();if(s!==null&&u-a<n)return s;const l=e();return s=l,a=u,l})();r.set(i)};return{subscribe:r.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Ui=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(Ui||{}),hc=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(hc||{}),pc=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(pc||{});const jr=class jr{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,s=!0){const a=this.extractFrontmatter(t);if(a){const r=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=a.match(r);if(o&&o[1])return o[1].toLowerCase()==="true"}return s}static parseString(t,n,s=""){const a=this.extractFrontmatter(t);if(a){const r=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=a.match(r);if(o&&o[1])return o[1].trim()}return s}static updateFrontmatter(t,n,s){const a=t.match(this.frontmatterRegex),r=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(a){const o=a[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const u=o.replace(i,`$1${r}`);return t.replace(this.frontmatterRegex,`---
${u}---
`)}{const u=`${o.endsWith(`
`)?o:o+`
`}${n}: ${r}
`;return t.replace(this.frontmatterRegex,`---
${u}---
`)}}return`---
${n}: ${r}
---

${t}`}static createFrontmatter(t,n){let s=t;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[a,r]of Object.entries(n))s=this.updateFrontmatter(s,a,r);return s}};jr.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let ue=jr;const Qe=class Qe{static parseRuleFile(t,n){const s=ue.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),a=ue.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:a,description:s||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=ue.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=ue.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return ue.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return ue.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return ue.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return ue.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return ue.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return ne.ALWAYS_ATTACHED;case"manual":return ne.MANUAL;case"agent_requested":return ne.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case ne.ALWAYS_ATTACHED:return"always_apply";case ne.MANUAL:return"manual";case ne.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return ue.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const s=this.mapRuleTypeToString(n);return ue.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(t),a=this.getDescriptionFrontmatterKey(t);return s?ne.ALWAYS_ATTACHED:a&&a.trim()!==""?ne.AGENT_REQUESTED:ne.MANUAL}};Qe.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",Qe.DESCRIPTION_FRONTMATTER_KEY="description",Qe.TYPE_FRONTMATTER_KEY="type",Qe.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],Qe.DEFAULT_RULE_TYPE=ne.MANUAL;let tt=Qe;const ct=".augment",At="rules",ls=".augment-guidelines";function ae(e,t){return t in e&&e[t]!==void 0}function mc(e){return ae(e,"file")}function gc(e){return ae(e,"recentFile")}function fc(e){return ae(e,"folder")}function yc(e){return ae(e,"sourceFolder")}function Cp(e){return ae(e,"sourceFolderGroup")}function xp(e){return ae(e,"selection")}function _c(e){return ae(e,"externalSource")}function Ap(e){return ae(e,"allDefaultContext")}function Rp(e){return ae(e,"clearContext")}function Mp(e){return ae(e,"userGuidelines")}function Op(e){return ae(e,"agentMemories")}function $i(e){return ae(e,"personality")}function bc(e){return ae(e,"rule")}function vc(e){return ae(e,"task")}const Pp={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Dp={clearContext:!0,label:"Clear Context",id:"clearContext"},Lp={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Fp={agentMemories:{},label:"Agent Memories",id:"agentMemories"},ja=[{personality:{type:z.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:z.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:z.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:z.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Up(e){return ae(e,"group")}function $p(e){const t=new Map;return e.forEach(n=>{mc(n)?t.set("file",[...t.get("file")??[],n]):gc(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):fc(n)?t.set("folder",[...t.get("folder")??[],n]):_c(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):yc(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):$i(n)?t.set("personality",[...t.get("personality")??[],n]):bc(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function Sc(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const s={label:Sl(e.pathName).split("/").filter(a=>a.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const a=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;s.label+=a,s.name+=a,s.id+=a}else if(e.range){const a=`:L${e.range.start}-${e.range.stop}`;s.label+=a,s.name+=a,s.id+=a}return s}function Ec(e){const t=e.path.split("/"),n=t[t.length-1],s=n.endsWith(".md")?n.slice(0,-3):n,a=`${ct}/${At}/${e.path}`;return{label:s,name:a,id:a}}var M=(e=>(e[e.unknown=0]="unknown",e[e.new=1]="new",e[e.checkingSafety=2]="checkingSafety",e[e.runnable=3]="runnable",e[e.running=4]="running",e[e.completed=5]="completed",e[e.error=6]="error",e[e.cancelling=7]="cancelling",e[e.cancelled=8]="cancelled",e))(M||{});function us(e){return`${e.requestId};${e.toolUseId}`}function Ya(e){const[t,n]=e.split(";");return{requestId:t,toolUseId:n}}var Tc=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(Tc||{}),wc=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(wc||{}),Rn=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Rn||{}),Ic=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(Ic||{}),Nc=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(Nc||{});function Ka(e,t){return function(n,s){if(n.length<=s||n.length===0)return{truncatedText:n};const a=n.split(`
`),r="... additional lines truncated ..."+(a[0].endsWith("\r")?"\r":"");let o,i="";if(a.length<2||a[0].length+a[a.length-1].length+r.length>s){const u=Math.floor(s/2);i=[n.slice(0,u),"<...>",n.slice(-u)].join(""),o=[1,1,a.length,a.length]}else{const u=[],l=[];let d=r.length+1;for(let c=0;c<Math.floor(a.length/2);c++){const h=a[c],p=a[a.length-1-c],m=h.length+p.length+2;if(d+m>s)break;d+=m,u.push(h),l.push(p)}o=[1,u.length,a.length-l.length+1,a.length],u.push(r),u.push(...l.reverse()),i=u.join(`
`)}return{truncatedText:i,shownRangeWhenTruncated:o}}(e,t).truncatedText}function kc(e){var n;if(!e)return Qt.IMAGE_FORMAT_UNSPECIFIED;switch((n=e.split("/")[1])==null?void 0:n.toLowerCase()){case"jpeg":case"jpg":return Qt.JPEG;case"png":return Qt.PNG;default:return Qt.IMAGE_FORMAT_UNSPECIFIED}}function Cc(e,t,n){var a,r;if(e.phase!==M.cancelled&&e.phase!==M.completed&&e.phase!==M.error)return;let s;return(a=e.result)!=null&&a.contentNodes?(s=function(o,i){return o.map(u=>u.type===Rn.ContentText?{type:Xn.CONTENT_TEXT,text_content:u.text_content}:u.type===Rn.ContentImage&&u.image_content&&i?{type:Xn.CONTENT_IMAGE,image_content:{image_data:u.image_content.image_data,format:kc(u.image_content.media_type)}}:{type:Xn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(e.result.contentNodes,n),{content:"",is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t,content_nodes:s}):((r=e.result)==null?void 0:r.text)!==void 0?{content:e.result.text,is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t}:void 0}function xc(e=[]){let t;for(const n of e){if(n.type===R.TOOL_USE)return n;n.type===R.TOOL_USE_START&&(t=n)}return t}function Ac(e,t,n,s){if(!e||!t)return[];let a=!1;return t.filter(r=>{var i;const o=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):n.getToolUseState(r.requestId??e,(i=r.tool_use)==null?void 0:i.tool_use_id);return a===!1&&o.phase!==M.new&&o.phase!==M.unknown&&o.phase!==M.checkingSafety&&r.tool_use!==void 0||(o.phase===M.runnable&&(a=!0),!1)})}function qp(e,t){if(e.contentNodes&&e.contentNodes.length>0){const n=e.contentNodes.map(s=>{if(s.type===Rn.ContentText){let a="";return s.text_content&&(a=Ka(s.text_content,t/e.contentNodes.length)),{...s,text_content:a}}return s});return{...e,contentNodes:n}}return{...e,text:Ka(e.text,t)}}const Rc="__NEW_AGENT__",Hp=e=>e.chatItemType===void 0,Bp=(e,t)=>{var r;const n=e.chatHistory.at(-1);if(!n||!H(n))return Ze.notRunning;if(!(n.status===C.success||n.status===C.failed||n.status===C.cancelled))return Ze.running;const s=((r=n.structured_output_nodes)==null?void 0:r.filter(o=>o.type===R.TOOL_USE&&!!o.tool_use))??[];let a;if(a=t.enableParallelTools?Ac(n.request_id,s,e).at(-1):s.at(-1),!a||!a.tool_use)return Ze.notRunning;switch(e.getToolUseState(n.request_id,a.tool_use.tool_use_id).phase){case M.runnable:return Ze.awaitingUserAction;case M.cancelled:return Ze.notRunning;default:return Ze.running}},Hs=e=>H(e)&&!!e.request_message,Mc=e=>e.chatHistory.findLast(t=>Hs(t)),Gp=(e,t)=>{const n=Mc(e);return n!=null&&n.request_id?e.historyFrom(n.request_id,!0).filter(s=>H(s)&&(!t||t(s))):[]},Vp=e=>{var s;const t=e.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!H(t))return!1;const n=((s=t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===R.TOOL_USE))??[];for(const a of n)if(a.tool_use&&e.getToolUseState(t.request_id,a.tool_use.tool_use_id).phase===M.runnable)return e.updateToolUseState({requestId:t.request_id,toolUseId:a.tool_use.tool_use_id,phase:M.cancelled}),!0;return!1};function Oc(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case z.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case z.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case z.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case z.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Pc[t]}const Pc={[z.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[z.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[z.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[z.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var de=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(de||{}),Br=(e=>(e.USER="USER",e.AGENT="AGENT",e))(Br||{}),qi={},Mn={},On={};let sn;Object.defineProperty(On,"__esModule",{value:!0}),On.default=function(){if(!sn&&(sn=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!sn))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return sn(Dc)};const Dc=new Uint8Array(16);var We={},rt={},Pn={};Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.default=void 0;Pn.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(rt,"__esModule",{value:!0}),rt.default=void 0;var rn,Lc=(rn=Pn)&&rn.__esModule?rn:{default:rn},Fc=function(e){return typeof e=="string"&&Lc.default.test(e)};rt.default=Fc,Object.defineProperty(We,"__esModule",{value:!0}),We.default=void 0,We.unsafeStringify=Hi;var Uc=function(e){return e&&e.__esModule?e:{default:e}}(rt);const Y=[];for(let e=0;e<256;++e)Y.push((e+256).toString(16).slice(1));function Hi(e,t=0){return Y[e[t+0]]+Y[e[t+1]]+Y[e[t+2]]+Y[e[t+3]]+"-"+Y[e[t+4]]+Y[e[t+5]]+"-"+Y[e[t+6]]+Y[e[t+7]]+"-"+Y[e[t+8]]+Y[e[t+9]]+"-"+Y[e[t+10]]+Y[e[t+11]]+Y[e[t+12]]+Y[e[t+13]]+Y[e[t+14]]+Y[e[t+15]]}var $c=function(e,t=0){const n=Hi(e,t);if(!(0,Uc.default)(n))throw TypeError("Stringified UUID is invalid");return n};We.default=$c,Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=void 0;var qc=function(e){return e&&e.__esModule?e:{default:e}}(On),Hc=We;let Wa,cs,ds=0,hs=0;var Bc=function(e,t,n){let s=t&&n||0;const a=t||new Array(16);let r=(e=e||{}).node||Wa,o=e.clockseq!==void 0?e.clockseq:cs;if(r==null||o==null){const h=e.random||(e.rng||qc.default)();r==null&&(r=Wa=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),o==null&&(o=cs=16383&(h[6]<<8|h[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),u=e.nsecs!==void 0?e.nsecs:hs+1;const l=i-ds+(u-hs)/1e4;if(l<0&&e.clockseq===void 0&&(o=o+1&16383),(l<0||i>ds)&&e.nsecs===void 0&&(u=0),u>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");ds=i,hs=u,cs=o,i+=122192928e5;const d=(1e4*(268435455&i)+u)%4294967296;a[s++]=d>>>24&255,a[s++]=d>>>16&255,a[s++]=d>>>8&255,a[s++]=255&d;const c=i/4294967296*1e4&268435455;a[s++]=c>>>8&255,a[s++]=255&c,a[s++]=c>>>24&15|16,a[s++]=c>>>16&255,a[s++]=o>>>8|128,a[s++]=255&o;for(let h=0;h<6;++h)a[s+h]=r[h];return t||(0,Hc.unsafeStringify)(a)};Mn.default=Bc;var Dn={},Ve={},Yt={};Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.default=void 0;var Gc=function(e){return e&&e.__esModule?e:{default:e}}(rt),Vc=function(e){if(!(0,Gc.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};Yt.default=Vc,Object.defineProperty(Ve,"__esModule",{value:!0}),Ve.URL=Ve.DNS=void 0,Ve.default=function(e,t,n){function s(a,r,o,i){var u;if(typeof a=="string"&&(a=function(d){d=unescape(encodeURIComponent(d));const c=[];for(let h=0;h<d.length;++h)c.push(d.charCodeAt(h));return c}(a)),typeof r=="string"&&(r=(0,Yc.default)(r)),((u=r)===null||u===void 0?void 0:u.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+a.length);if(l.set(r),l.set(a,r.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,o){i=i||0;for(let d=0;d<16;++d)o[i+d]=l[d];return o}return(0,jc.unsafeStringify)(l)}try{s.name=e}catch{}return s.DNS=Bi,s.URL=Gi,s};var jc=We,Yc=function(e){return e&&e.__esModule?e:{default:e}}(Yt);const Bi="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Ve.DNS=Bi;const Gi="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Ve.URL=Gi;var Ln={};function za(e){return 14+(e+64>>>9<<4)+1}function je(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Kn(e,t,n,s,a,r){return je((o=je(je(t,e),je(s,r)))<<(i=a)|o>>>32-i,n);var o,i}function J(e,t,n,s,a,r,o){return Kn(t&n|~t&s,e,t,a,r,o)}function Z(e,t,n,s,a,r,o){return Kn(t&s|n&~s,e,t,a,r,o)}function Q(e,t,n,s,a,r,o){return Kn(t^n^s,e,t,a,r,o)}function ee(e,t,n,s,a,r,o){return Kn(n^(t|~s),e,t,a,r,o)}Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Kc=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],s=32*t.length,a="0123456789abcdef";for(let r=0;r<s;r+=8){const o=t[r>>5]>>>r%32&255,i=parseInt(a.charAt(o>>>4&15)+a.charAt(15&o),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[za(n)-1]=n;let s=1732584193,a=-271733879,r=-1732584194,o=271733878;for(let i=0;i<t.length;i+=16){const u=s,l=a,d=r,c=o;s=J(s,a,r,o,t[i],7,-680876936),o=J(o,s,a,r,t[i+1],12,-389564586),r=J(r,o,s,a,t[i+2],17,606105819),a=J(a,r,o,s,t[i+3],22,-1044525330),s=J(s,a,r,o,t[i+4],7,-176418897),o=J(o,s,a,r,t[i+5],12,1200080426),r=J(r,o,s,a,t[i+6],17,-1473231341),a=J(a,r,o,s,t[i+7],22,-45705983),s=J(s,a,r,o,t[i+8],7,1770035416),o=J(o,s,a,r,t[i+9],12,-1958414417),r=J(r,o,s,a,t[i+10],17,-42063),a=J(a,r,o,s,t[i+11],22,-1990404162),s=J(s,a,r,o,t[i+12],7,1804603682),o=J(o,s,a,r,t[i+13],12,-40341101),r=J(r,o,s,a,t[i+14],17,-1502002290),a=J(a,r,o,s,t[i+15],22,1236535329),s=Z(s,a,r,o,t[i+1],5,-165796510),o=Z(o,s,a,r,t[i+6],9,-1069501632),r=Z(r,o,s,a,t[i+11],14,643717713),a=Z(a,r,o,s,t[i],20,-373897302),s=Z(s,a,r,o,t[i+5],5,-701558691),o=Z(o,s,a,r,t[i+10],9,38016083),r=Z(r,o,s,a,t[i+15],14,-660478335),a=Z(a,r,o,s,t[i+4],20,-405537848),s=Z(s,a,r,o,t[i+9],5,568446438),o=Z(o,s,a,r,t[i+14],9,-1019803690),r=Z(r,o,s,a,t[i+3],14,-187363961),a=Z(a,r,o,s,t[i+8],20,1163531501),s=Z(s,a,r,o,t[i+13],5,-1444681467),o=Z(o,s,a,r,t[i+2],9,-51403784),r=Z(r,o,s,a,t[i+7],14,1735328473),a=Z(a,r,o,s,t[i+12],20,-1926607734),s=Q(s,a,r,o,t[i+5],4,-378558),o=Q(o,s,a,r,t[i+8],11,-2022574463),r=Q(r,o,s,a,t[i+11],16,1839030562),a=Q(a,r,o,s,t[i+14],23,-35309556),s=Q(s,a,r,o,t[i+1],4,-1530992060),o=Q(o,s,a,r,t[i+4],11,1272893353),r=Q(r,o,s,a,t[i+7],16,-155497632),a=Q(a,r,o,s,t[i+10],23,-1094730640),s=Q(s,a,r,o,t[i+13],4,681279174),o=Q(o,s,a,r,t[i],11,-358537222),r=Q(r,o,s,a,t[i+3],16,-722521979),a=Q(a,r,o,s,t[i+6],23,76029189),s=Q(s,a,r,o,t[i+9],4,-640364487),o=Q(o,s,a,r,t[i+12],11,-421815835),r=Q(r,o,s,a,t[i+15],16,530742520),a=Q(a,r,o,s,t[i+2],23,-995338651),s=ee(s,a,r,o,t[i],6,-198630844),o=ee(o,s,a,r,t[i+7],10,1126891415),r=ee(r,o,s,a,t[i+14],15,-1416354905),a=ee(a,r,o,s,t[i+5],21,-57434055),s=ee(s,a,r,o,t[i+12],6,1700485571),o=ee(o,s,a,r,t[i+3],10,-1894986606),r=ee(r,o,s,a,t[i+10],15,-1051523),a=ee(a,r,o,s,t[i+1],21,-2054922799),s=ee(s,a,r,o,t[i+8],6,1873313359),o=ee(o,s,a,r,t[i+15],10,-30611744),r=ee(r,o,s,a,t[i+6],15,-1560198380),a=ee(a,r,o,s,t[i+13],21,1309151649),s=ee(s,a,r,o,t[i+4],6,-145523070),o=ee(o,s,a,r,t[i+11],10,-1120210379),r=ee(r,o,s,a,t[i+2],15,718787259),a=ee(a,r,o,s,t[i+9],21,-343485551),s=je(s,u),a=je(a,l),r=je(r,d),o=je(o,c)}return[s,a,r,o]}(function(t){if(t.length===0)return[];const n=8*t.length,s=new Uint32Array(za(n));for(let a=0;a<n;a+=8)s[a>>5]|=(255&t[a/8])<<a%32;return s}(e),8*e.length))};Ln.default=Kc,Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.default=void 0;var Wc=Vi(Ve),zc=Vi(Ln);function Vi(e){return e&&e.__esModule?e:{default:e}}var Xc=(0,Wc.default)("v3",48,zc.default);Dn.default=Xc;var Fn={},Un={};Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var Jc={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};Un.default=Jc,Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var Xa=ji(Un),Zc=ji(On),Qc=We;function ji(e){return e&&e.__esModule?e:{default:e}}var ed=function(e,t,n){if(Xa.default.randomUUID&&!t&&!e)return Xa.default.randomUUID();const s=(e=e||{}).random||(e.rng||Zc.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let a=0;a<16;++a)t[n+a]=s[a];return t}return(0,Qc.unsafeStringify)(s)};Fn.default=ed;var $n={},qn={};function td(e,t,n,s){switch(e){case 0:return t&n^~t&s;case 1:case 3:return t^n^s;case 2:return t&n^t&s^n&s}}function ps(e,t){return e<<t|e>>>32-t}Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var nd=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const o=unescape(encodeURIComponent(e));e=[];for(let i=0;i<o.length;++i)e.push(o.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const s=e.length/4+2,a=Math.ceil(s/16),r=new Array(a);for(let o=0;o<a;++o){const i=new Uint32Array(16);for(let u=0;u<16;++u)i[u]=e[64*o+4*u]<<24|e[64*o+4*u+1]<<16|e[64*o+4*u+2]<<8|e[64*o+4*u+3];r[o]=i}r[a-1][14]=8*(e.length-1)/Math.pow(2,32),r[a-1][14]=Math.floor(r[a-1][14]),r[a-1][15]=8*(e.length-1)&4294967295;for(let o=0;o<a;++o){const i=new Uint32Array(80);for(let p=0;p<16;++p)i[p]=r[o][p];for(let p=16;p<80;++p)i[p]=ps(i[p-3]^i[p-8]^i[p-14]^i[p-16],1);let u=n[0],l=n[1],d=n[2],c=n[3],h=n[4];for(let p=0;p<80;++p){const m=Math.floor(p/20),y=ps(u,5)+td(m,l,d,c)+h+t[m]+i[p]>>>0;h=c,c=d,d=ps(l,30)>>>0,l=u,u=y}n[0]=n[0]+u>>>0,n[1]=n[1]+l>>>0,n[2]=n[2]+d>>>0,n[3]=n[3]+c>>>0,n[4]=n[4]+h>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};qn.default=nd,Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var sd=Yi(Ve),rd=Yi(qn);function Yi(e){return e&&e.__esModule?e:{default:e}}var ad=(0,sd.default)("v5",80,rd.default);$n.default=ad;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.default=void 0;Hn.default="00000000-0000-0000-0000-000000000000";var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.default=void 0;var od=function(e){return e&&e.__esModule?e:{default:e}}(rt),id=function(e){if(!(0,od.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function Bs(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}Bn.default=id,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return o.default}});var t=d(Mn),n=d(Dn),s=d(Fn),a=d($n),r=d(Hn),o=d(Bn),i=d(rt),u=d(We),l=d(Yt);function d(c){return c&&c.__esModule?c:{default:c}}}(qi),Bs.prototype.convert=function(e){var t,n,s,a={},r=this.srcAlphabet.length,o=this.dstAlphabet.length,i=e.length,u=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)a[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,s=0,t=0;t<i;t++)(n=n*r+a[t])>=o?(a[s++]=parseInt(n/o,10),n%=o):s>0&&(a[s++]=0);i=s,u=this.dstAlphabet.slice(n,n+1).concat(u)}while(s!==0);return u},Bs.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var ld=Bs;function Rt(e,t){var n=new ld(e,t);return function(s){return n.convert(s)}}Rt.BIN="01",Rt.OCT="01234567",Rt.DEC="0123456789",Rt.HEX="0123456789abcdef";var ud=Rt;const{v4:ms,validate:cd}=qi,an=ud,gs={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},dd={consistentLength:!0};let fs;const Ja=(e,t,n)=>{const s=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},Za=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var hd=(()=>{const e=(t,n)=>{const s=t||gs.flickrBase58,a={...dd,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const r=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:r,consistentLength:a.consistentLength,paddingChar:s[0]},u=an(an.HEX,s),l=an(s,an.HEX),d=()=>Ja(ms(),u,i),c={alphabet:s,fromUUID:h=>Ja(h,u,i),maxLength:r,generate:d,new:d,toUUID:h=>Za(h,l),uuid:ms,validate:(h,p=!1)=>{if(!h||typeof h!="string")return!1;const m=a.consistentLength?h.length===r:h.length<=r,y=h.split("").every(f=>s.includes(f));return p===!1?m&&y:m&&y&&cd(Za(h,l))}};return Object.freeze(c),c};return e.constants=gs,e.uuid=ms,e.generate=()=>(fs||(fs=e(gs.flickrBase58).generate),fs()),e})();const pd=vr(hd),Ki={[de.NOT_STARTED]:"[ ]",[de.IN_PROGRESS]:"[/]",[de.COMPLETE]:"[x]",[de.CANCELLED]:"[-]"},Wi=pd(void 0,{consistentLength:!0});function md(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const s=md(n,t);if(s)return s}}function zi(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:a=!0}=t;return Xi(e,{shallow:n,excludeUuid:s,shortUuid:a}).join(`
`)}function Xi(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:a=!0}=t;let r="";s||(r=`UUID:${a?function(i){try{return Wi.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const o=`${Ki[e.state]} ${r}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[o]:[o,...(e.subTasksData||[]).map(i=>Xi(i,t).map(u=>`-${u}`)).flat()]}function gd(e,t){var s;const n=(s=e.subTasksData)==null?void 0:s.map(a=>gd(a,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(a=>a.uuid))||[],subTasksData:n}}function jp(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let s=0;for(const l of n)if(l.trim()&&Qa(l)===0)try{Gs(l,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const a=e.split(`
`);function r(){for(;a.length>0;){const l=a.shift(),d=Qa(l);try{return{task:Gs(l,t),level:d}}catch{}}}const o=r();if(!o)throw new Error("No root task found");const i=[o.task];let u;for(;u=r();){const l=i[u.level-1];if(!l)throw new Error(`Invalid markdown: level ${u.level+1} has no parent
Line: ${u.task.name} is missing a parent
Current tasks: 
${zi(o.task)}`);l.subTasksData&&l.subTasks||(l.subTasks=[],l.subTasksData=[]),l.subTasksData.push(u.task),l.subTasks.push(u.task.uuid),i[u.level]=u.task,i.splice(u.level+1)}return o.task}function Qa(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Gs(e,t={}){const{excludeUuid:n=!1,shortUuid:s=!0}=t;let a=0;for(;a<e.length&&(e[a]===" "||e[a]==="	"||e[a]==="-");)a++;const r=e.substring(a),o=r.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${e} (missing state)`);const i=o[1],u=Object.entries(Ki).reduce((p,[m,y])=>(p[y.substring(1,2)]=m,p),{})[i]||de.NOT_STARTED,l=r.substring(o.index+o[0].length).trim();let d,c,h;if(n){const p=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(p);if(!m){const y=/\b(?:name|NAME):/i.test(l),f=/\b(?:description|DESCRIPTION):/i.test(l);throw!y||!f?new Error(`Invalid task line: ${e} (missing required fields)`):l.toLowerCase().indexOf("name:")<l.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(c=m[1].trim(),h=m[2].trim(),!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);d=crypto.randomUUID()}else{const p=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(p);if(!m){const y=/\b(?:uuid|UUID):/i.test(l),f=/\b(?:name|NAME):/i.test(l),b=/\b(?:description|DESCRIPTION):/i.test(l);if(!y||!f||!b)throw new Error(`Invalid task line: ${e} (missing required fields)`);const S=l.toLowerCase().indexOf("uuid:"),v=l.toLowerCase().indexOf("name:"),T=l.toLowerCase().indexOf("description:");throw S<v&&v<T?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(d=m[1].trim(),c=m[2].trim(),h=m[3].trim(),!d||!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(y){try{return Wi.toUUID(y)}catch{return y}}(d)}catch{}}return{uuid:d,name:c,description:h,state:u,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Br.USER}}const Et=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:de.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Br.USER,...e}),eo=Et({name:"Task 1.1",description:"This is the first sub task",state:de.IN_PROGRESS}),to=Et({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:de.NOT_STARTED}),no=Et({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:de.IN_PROGRESS}),so=Et({name:"Task 1.2",description:"This is the second sub task",state:de.COMPLETE,subTasks:[to.uuid,no.uuid],subTasksData:[to,no]}),ro=Et({name:"Task 1.3",description:"This is the third sub task",state:de.CANCELLED}),Yp=zi(Et({name:"Task 1",description:"This is the first task",state:de.NOT_STARTED,subTasks:[eo.uuid,so.uuid,ro.uuid],subTasksData:[eo,so,ro]}));function Ji(e){const t=e.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const a of t){const r=a.trim();if(r!=="## Created Tasks")if(r!=="## Updated Tasks")if(r!=="## Deleted Tasks"){if(n&&(r.startsWith("[ ]")||r.startsWith("[/]")||r.startsWith("[x]")||r.startsWith("[-]")))try{const o=Gs(r,{excludeUuid:!1,shortUuid:!0});o&&s[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function Kp(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Ji(Zi(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Zi(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let a=n.length;for(const i of s){const u=n.indexOf(i);u!==-1&&u<a&&(a=u)}const r=n.substring(0,a),o=r.indexOf(`
`);return o===-1?"":r.substring(o+1).trim()}function Wp(e){return Ji(Zi(e))}class fd{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:s}=t,a=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${a}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:s}=t;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:s,targetTaskPath:a}=n;let r=`This task is part of a larger project: "${s.name}"`;return s.description&&(r+=`

**Project Description:** ${s.description}`),a.length>1&&(r+=`

**Task Path:** ${a.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(r+=`

**Subtasks:**`,t.subTasksData.forEach((o,i)=>{r+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(r+=` - ${o.description}`)})),r}}function wt(e){var t;return((t=e.extraData)==null?void 0:t.isAgentConversation)===!0}var yd=(e=>(e[e.active=0]="active",e[e.inactive=1]="inactive",e))(yd||{});const q={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point.",abridgedHistoryParams:{totalCharsLimit:1e4,userMessageCharsLimit:1e3,agentResponseCharsLimit:2e3,actionCharsLimit:200,numFilesModifiedLimit:10,numFilesCreatedLimit:10,numFilesDeletedLimit:10,numFilesViewedLimit:10,numTerminalCommandsLimit:10}};function Vs(e,t,n=.5,s=.5){if(e.length<=t||t<=0)return e;if(n+s>1)throw new Error("startRatio + endRatio cannot exceed 1.0");const a="...",r=t-3;if(r<=0)return a.substring(0,t);const o=Math.floor(r*n),i=Math.floor(r*s);return e.substring(0,o)+a+e.substring(e.length-i)}const V=(e,t)=>e!==void 0?e:t;var js={exports:{}},Ys={exports:{}},ge={},P={__esModule:!0};P.extend=ao,P.indexOf=function(e,t){for(var n=0,s=e.length;n<s;n++)if(e[n]===t)return n;return-1},P.escapeExpression=function(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return vd.test(e)?e.replace(bd,Sd):e},P.isEmpty=function(e){return!e&&e!==0||!(!Qi(e)||e.length!==0)},P.createFrame=function(e){var t=ao({},e);return t._parent=e,t},P.blockParams=function(e,t){return e.path=t,e},P.appendContextPath=function(e,t){return(e?e+".":"")+t};var _d={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},bd=/[&<>"'`=]/g,vd=/[&<>"'`=]/;function Sd(e){return _d[e]}function ao(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}var Gr=Object.prototype.toString;P.toString=Gr;var ys=function(e){return typeof e=="function"};ys(/x/)&&(P.isFunction=ys=function(e){return typeof e=="function"&&Gr.call(e)==="[object Function]"}),P.isFunction=ys;var Qi=Array.isArray||function(e){return!(!e||typeof e!="object")&&Gr.call(e)==="[object Array]"};P.isArray=Qi;var Ks={exports:{}};(function(e,t){t.__esModule=!0;var n=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function s(a,r){var o=r&&r.loc,i=void 0,u=void 0,l=void 0,d=void 0;o&&(i=o.start.line,u=o.end.line,l=o.start.column,d=o.end.column,a+=" - "+i+":"+l);for(var c=Error.prototype.constructor.call(this,a),h=0;h<n.length;h++)this[n[h]]=c[n[h]];Error.captureStackTrace&&Error.captureStackTrace(this,s);try{o&&(this.lineNumber=i,this.endLineNumber=u,Object.defineProperty?(Object.defineProperty(this,"column",{value:l,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:d,enumerable:!0})):(this.column=l,this.endColumn=d))}catch{}}s.prototype=new Error,t.default=s,e.exports=t.default})(Ks,Ks.exports);var Te=Ks.exports,Ft={},Ws={exports:{}};(function(e,t){t.__esModule=!0;var n=P;t.default=function(s){s.registerHelper("blockHelperMissing",function(a,r){var o=r.inverse,i=r.fn;if(a===!0)return i(this);if(a===!1||a==null)return o(this);if(n.isArray(a))return a.length>0?(r.ids&&(r.ids=[r.name]),s.helpers.each(a,r)):o(this);if(r.data&&r.ids){var u=n.createFrame(r.data);u.contextPath=n.appendContextPath(r.data.contextPath,r.name),r={data:u}}return i(a,r)})},e.exports=t.default})(Ws,Ws.exports);var Ed=Ws.exports,zs={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(a){return a&&a.__esModule?a:{default:a}}(Te);t.default=function(a){a.registerHelper("each",function(r,o){if(!o)throw new s.default("Must pass iterator to #each");var i,u=o.fn,l=o.inverse,d=0,c="",h=void 0,p=void 0;function m(v,T,w){h&&(h.key=v,h.index=T,h.first=T===0,h.last=!!w,p&&(h.contextPath=p+v)),c+=u(r[v],{data:h,blockParams:n.blockParams([r[v],v],[p+v,null])})}if(o.data&&o.ids&&(p=n.appendContextPath(o.data.contextPath,o.ids[0])+"."),n.isFunction(r)&&(r=r.call(this)),o.data&&(h=n.createFrame(o.data)),r&&typeof r=="object")if(n.isArray(r))for(var y=r.length;d<y;d++)d in r&&m(d,d,d===r.length-1);else if(typeof Symbol=="function"&&r[Symbol.iterator]){for(var f=[],b=r[Symbol.iterator](),S=b.next();!S.done;S=b.next())f.push(S.value);for(y=(r=f).length;d<y;d++)m(d,d,d===r.length-1)}else i=void 0,Object.keys(r).forEach(function(v){i!==void 0&&m(i,d-1),i=v,d++}),i!==void 0&&m(i,d-1,!0);return d===0&&(c=l(this)),c})},e.exports=t.default})(zs,zs.exports);var Td=zs.exports,Xs={exports:{}};(function(e,t){t.__esModule=!0;var n=function(s){return s&&s.__esModule?s:{default:s}}(Te);t.default=function(s){s.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new n.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t.default})(Xs,Xs.exports);var wd=Xs.exports,Js={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(a){return a&&a.__esModule?a:{default:a}}(Te);t.default=function(a){a.registerHelper("if",function(r,o){if(arguments.length!=2)throw new s.default("#if requires exactly one argument");return n.isFunction(r)&&(r=r.call(this)),!o.hash.includeZero&&!r||n.isEmpty(r)?o.inverse(this):o.fn(this)}),a.registerHelper("unless",function(r,o){if(arguments.length!=2)throw new s.default("#unless requires exactly one argument");return a.helpers.if.call(this,r,{fn:o.inverse,inverse:o.fn,hash:o.hash})})},e.exports=t.default})(Js,Js.exports);var oo,_s,Id=Js.exports,Zs={exports:{}};oo=Zs,(_s=Zs.exports).__esModule=!0,_s.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],n=arguments[arguments.length-1],s=0;s<arguments.length-1;s++)t.push(arguments[s]);var a=1;n.hash.level!=null?a=n.hash.level:n.data&&n.data.level!=null&&(a=n.data.level),t[0]=a,e.log.apply(e,t)})},oo.exports=_s.default;var Nd=Zs.exports,Qs={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){n.registerHelper("lookup",function(s,a,r){return s&&r.lookupProperty(s,a)})},e.exports=t.default})(Qs,Qs.exports);var kd=Qs.exports,er={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(a){return a&&a.__esModule?a:{default:a}}(Te);t.default=function(a){a.registerHelper("with",function(r,o){if(arguments.length!=2)throw new s.default("#with requires exactly one argument");n.isFunction(r)&&(r=r.call(this));var i=o.fn;if(n.isEmpty(r))return o.inverse(this);var u=o.data;return o.data&&o.ids&&((u=n.createFrame(o.data)).contextPath=n.appendContextPath(o.data.contextPath,o.ids[0])),i(r,{data:u,blockParams:n.blockParams([r],[u&&u.contextPath])})})},e.exports=t.default})(er,er.exports);var Cd=er.exports;function at(e){return e&&e.__esModule?e:{default:e}}Ft.__esModule=!0,Ft.registerDefaultHelpers=function(e){xd.default(e),Ad.default(e),Rd.default(e),Md.default(e),Od.default(e),Pd.default(e),Dd.default(e)},Ft.moveHelperToHooks=function(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])};var xd=at(Ed),Ad=at(Td),Rd=at(wd),Md=at(Id),Od=at(Nd),Pd=at(kd),Dd=at(Cd),tr={},nr={exports:{}};(function(e,t){t.__esModule=!0;var n=P;t.default=function(s){s.registerDecorator("inline",function(a,r,o,i){var u=a;return r.partials||(r.partials={},u=function(l,d){var c=o.partials;o.partials=n.extend({},c,r.partials);var h=a(l,d);return o.partials=c,h}),r.partials[i.args[0]]=i.fn,u})},e.exports=t.default})(nr,nr.exports);var Ld=nr.exports;tr.__esModule=!0,tr.registerDefaultDecorators=function(e){Fd.default(e)};var Fd=function(e){return e&&e.__esModule?e:{default:e}}(Ld),sr={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(a){if(typeof a=="string"){var r=n.indexOf(s.methodMap,a.toLowerCase());a=r>=0?r:parseInt(a,10)}return a},log:function(a){if(a=s.lookupLevel(a),typeof console<"u"&&s.lookupLevel(s.level)<=a){var r=s.methodMap[a];console[r]||(r="log");for(var o=arguments.length,i=Array(o>1?o-1:0),u=1;u<o;u++)i[u-1]=arguments[u];console[r].apply(console,i)}}};t.default=s,e.exports=t.default})(sr,sr.exports);var el=sr.exports,ht={},Ud={__esModule:!0,createNewLookupObject:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return $d.extend.apply(void 0,[Object.create(null)].concat(t))}},$d=P;ht.__esModule=!0,ht.createProtoAccessControl=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=Object.create(null);return n.__proto__=!1,{properties:{whitelist:io.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:io.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},ht.resultIsAllowed=function(e,t,n){return Hd(typeof e=="function"?t.methods:t.properties,n)},ht.resetLoggedProperties=function(){Object.keys(Gn).forEach(function(e){delete Gn[e]})};var io=Ud,qd=function(e){return e&&e.__esModule?e:{default:e}}(el),Gn=Object.create(null);function Hd(e,t){return e.whitelist[t]!==void 0?e.whitelist[t]===!0:e.defaultValue!==void 0?e.defaultValue:(function(n){Gn[n]!==!0&&(Gn[n]=!0,qd.default.log("error",'Handlebars: Access has been denied to resolve the property "'+n+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}(t),!1)}function tl(e){return e&&e.__esModule?e:{default:e}}ge.__esModule=!0,ge.HandlebarsEnvironment=rr;var Je=P,bs=tl(Te),Bd=Ft,Gd=tr,Vn=tl(el),Vd=ht;ge.VERSION="4.7.8";ge.COMPILER_REVISION=8;ge.LAST_COMPATIBLE_COMPILER_REVISION=7;ge.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var vs="[object Object]";function rr(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},Bd.registerDefaultHelpers(this),Gd.registerDefaultDecorators(this)}rr.prototype={constructor:rr,logger:Vn.default,log:Vn.default.log,registerHelper:function(e,t){if(Je.toString.call(e)===vs){if(t)throw new bs.default("Arg not supported with multiple helpers");Je.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(Je.toString.call(e)===vs)Je.extend(this.partials,e);else{if(t===void 0)throw new bs.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(Je.toString.call(e)===vs){if(t)throw new bs.default("Arg not supported with multiple decorators");Je.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){Vd.resetLoggedProperties()}};var jd=Vn.default.log;ge.log=jd,ge.createFrame=Je.createFrame,ge.logger=Vn.default;var ar={exports:{}};(function(e,t){function n(s){this.string=s}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t.default=n,e.exports=t.default})(ar,ar.exports);var Yd=ar.exports,qe={},or={};or.__esModule=!0,or.wrapHelper=function(e,t){return typeof e!="function"?e:function(){return arguments[arguments.length-1]=t(arguments[arguments.length-1]),e.apply(this,arguments)}},qe.__esModule=!0,qe.checkRevision=function(e){var t=e&&e[0]||1,n=Pe.COMPILER_REVISION;if(!(t>=Pe.LAST_COMPATIBLE_COMPILER_REVISION&&t<=Pe.COMPILER_REVISION)){if(t<Pe.LAST_COMPATIBLE_COMPILER_REVISION){var s=Pe.REVISION_CHANGES[n],a=Pe.REVISION_CHANGES[t];throw new Oe.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+s+") or downgrade your runtime to an older version ("+a+").")}throw new Oe.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},qe.template=function(e,t){if(!t)throw new Oe.default("No environment passed to template");if(!e||!e.main)throw new Oe.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var n=e.compiler&&e.compiler[0]===7,s={strict:function(r,o,i){if(!r||!(o in r))throw new Oe.default('"'+o+'" not defined in '+r,{loc:i});return s.lookupProperty(r,o)},lookupProperty:function(r,o){var i=r[o];return i==null||Object.prototype.hasOwnProperty.call(r,o)||uo.resultIsAllowed(i,s.protoAccessControl,o)?i:void 0},lookup:function(r,o){for(var i=r.length,u=0;u<i;u++)if((r[u]&&s.lookupProperty(r[u],o))!=null)return r[u][o]},lambda:function(r,o){return typeof r=="function"?r.call(o):r},escapeExpression:Le.escapeExpression,invokePartial:function(r,o,i){i.hash&&(o=Le.extend({},o,i.hash),i.ids&&(i.ids[0]=!0)),r=t.VM.resolvePartial.call(this,r,o,i);var u=Le.extend({},i,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),l=t.VM.invokePartial.call(this,r,o,u);if(l==null&&t.compile&&(i.partials[i.name]=t.compile(r,e.compilerOptions,t),l=i.partials[i.name](o,u)),l!=null){if(i.indent){for(var d=l.split(`
`),c=0,h=d.length;c<h&&(d[c]||c+1!==h);c++)d[c]=i.indent+d[c];l=d.join(`
`)}return l}throw new Oe.default("The partial "+i.name+" could not be compiled when running in runtime-only mode")},fn:function(r){var o=e[r];return o.decorator=e[r+"_d"],o},programs:[],program:function(r,o,i,u,l){var d=this.programs[r],c=this.fn(r);return o||l||u||i?d=on(this,r,c,o,i,u,l):d||(d=this.programs[r]=on(this,r,c)),d},data:function(r,o){for(;r&&o--;)r=r._parent;return r},mergeIfNeeded:function(r,o){var i=r||o;return r&&o&&r!==o&&(i=Le.extend({},o,r)),i},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function a(r){var o=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],i=o.data;a._setup(o),!o.partial&&e.useData&&(i=function(c,h){return h&&"root"in h||((h=h?Pe.createFrame(h):{}).root=c),h}(r,i));var u=void 0,l=e.useBlockParams?[]:void 0;function d(c){return""+e.main(s,c,s.helpers,s.partials,i,l,u)}return e.useDepths&&(u=o.depths?r!=o.depths[0]?[r].concat(o.depths):o.depths:[r]),(d=nl(e.main,d,s,o.depths||[],i,l))(r,o)}return a.isTop=!0,a._setup=function(r){if(r.partial)s.protoAccessControl=r.protoAccessControl,s.helpers=r.helpers,s.partials=r.partials,s.decorators=r.decorators,s.hooks=r.hooks;else{var o=Le.extend({},t.helpers,r.helpers);(function(u,l){Object.keys(u).forEach(function(d){var c=u[d];u[d]=function(h,p){var m=p.lookupProperty;return Kd.wrapHelper(h,function(y){return Le.extend({lookupProperty:m},y)})}(c,l)})})(o,s),s.helpers=o,e.usePartial&&(s.partials=s.mergeIfNeeded(r.partials,t.partials)),(e.usePartial||e.useDecorators)&&(s.decorators=Le.extend({},t.decorators,r.decorators)),s.hooks={},s.protoAccessControl=uo.createProtoAccessControl(r);var i=r.allowCallsToHelperMissing||n;lo.moveHelperToHooks(s,"helperMissing",i),lo.moveHelperToHooks(s,"blockHelperMissing",i)}},a._child=function(r,o,i,u){if(e.useBlockParams&&!i)throw new Oe.default("must pass block params");if(e.useDepths&&!u)throw new Oe.default("must pass parent depths");return on(s,r,e[r],o,0,i,u)},a},qe.wrapProgram=on,qe.resolvePartial=function(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e=n.name==="@partial-block"?n.data["partial-block"]:n.partials[n.name],e},qe.invokePartial=function(e,t,n){var s=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var a=void 0;if(n.fn&&n.fn!==co&&function(){n.data=Pe.createFrame(n.data);var r=n.fn;a=n.data["partial-block"]=function(o){var i=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return i.data=Pe.createFrame(i.data),i.data["partial-block"]=s,r(o,i)},r.partials&&(n.partials=Le.extend({},n.partials,r.partials))}(),e===void 0&&a&&(e=a),e===void 0)throw new Oe.default("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)},qe.noop=co;var Le=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(P),Oe=function(e){return e&&e.__esModule?e:{default:e}}(Te),Pe=ge,lo=Ft,Kd=or,uo=ht;function on(e,t,n,s,a,r,o){function i(u){var l=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],d=o;return!o||u==o[0]||u===e.nullContext&&o[0]===null||(d=[u].concat(o)),n(e,u,e.helpers,e.partials,l.data||s,r&&[l.blockParams].concat(r),d)}return(i=nl(n,i,e,o,s,r)).program=t,i.depth=o?o.length:0,i.blockParams=a||0,i}function co(){return""}function nl(e,t,n,s,a,r){if(e.decorator){var o={};t=e.decorator(t,o,n,s&&s[0],a,r,s),Le.extend(t,o)}return t}var ir={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var s=globalThis.Handlebars;n.noConflict=function(){return globalThis.Handlebars===n&&(globalThis.Handlebars=s),n}},e.exports=t.default})(ir,ir.exports);var sl=ir.exports;(function(e,t){function n(h){return h&&h.__esModule?h:{default:h}}function s(h){if(h&&h.__esModule)return h;var p={};if(h!=null)for(var m in h)Object.prototype.hasOwnProperty.call(h,m)&&(p[m]=h[m]);return p.default=h,p}t.__esModule=!0;var a=s(ge),r=n(Yd),o=n(Te),i=s(P),u=s(qe),l=n(sl);function d(){var h=new a.HandlebarsEnvironment;return i.extend(h,a),h.SafeString=r.default,h.Exception=o.default,h.Utils=i,h.escapeExpression=i.escapeExpression,h.VM=u,h.template=function(p){return u.template(p,h)},h}var c=d();c.create=d,l.default(c),c.default=c,t.default=c,e.exports=t.default})(Ys,Ys.exports);var Wd=Ys.exports,lr={exports:{}};(function(e,t){t.__esModule=!0;var n={helpers:{helperExpression:function(s){return s.type==="SubExpression"||(s.type==="MustacheStatement"||s.type==="BlockStatement")&&!!(s.params&&s.params.length||s.hash)},scopedId:function(s){return/^\.|this\b/.test(s.original)},simpleId:function(s){return s.parts.length===1&&!n.helpers.scopedId(s)&&!s.depth}}};t.default=n,e.exports=t.default})(lr,lr.exports);var rl=lr.exports,Ut={},ur={exports:{}};(function(e,t){t.__esModule=!0;var n=function(){var s={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(o,i,u,l,d,c,h){var p=c.length-1;switch(d){case 1:return c[p-1];case 2:this.$=l.prepareProgram(c[p]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=c[p];break;case 9:this.$={type:"CommentStatement",value:l.stripComment(c[p]),strip:l.stripFlags(c[p],c[p]),loc:l.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:c[p],value:c[p],loc:l.locInfo(this._$)};break;case 11:this.$=l.prepareRawBlock(c[p-2],c[p-1],c[p],this._$);break;case 12:this.$={path:c[p-3],params:c[p-2],hash:c[p-1]};break;case 13:this.$=l.prepareBlock(c[p-3],c[p-2],c[p-1],c[p],!1,this._$);break;case 14:this.$=l.prepareBlock(c[p-3],c[p-2],c[p-1],c[p],!0,this._$);break;case 15:this.$={open:c[p-5],path:c[p-4],params:c[p-3],hash:c[p-2],blockParams:c[p-1],strip:l.stripFlags(c[p-5],c[p])};break;case 16:case 17:this.$={path:c[p-4],params:c[p-3],hash:c[p-2],blockParams:c[p-1],strip:l.stripFlags(c[p-5],c[p])};break;case 18:this.$={strip:l.stripFlags(c[p-1],c[p-1]),program:c[p]};break;case 19:var m=l.prepareBlock(c[p-2],c[p-1],c[p],c[p],!1,this._$),y=l.prepareProgram([m],c[p-1].loc);y.chained=!0,this.$={strip:c[p-2].strip,program:y,chain:!0};break;case 21:this.$={path:c[p-1],strip:l.stripFlags(c[p-2],c[p])};break;case 22:case 23:this.$=l.prepareMustache(c[p-3],c[p-2],c[p-1],c[p-4],l.stripFlags(c[p-4],c[p]),this._$);break;case 24:this.$={type:"PartialStatement",name:c[p-3],params:c[p-2],hash:c[p-1],indent:"",strip:l.stripFlags(c[p-4],c[p]),loc:l.locInfo(this._$)};break;case 25:this.$=l.preparePartialBlock(c[p-2],c[p-1],c[p],this._$);break;case 26:this.$={path:c[p-3],params:c[p-2],hash:c[p-1],strip:l.stripFlags(c[p-4],c[p])};break;case 29:this.$={type:"SubExpression",path:c[p-3],params:c[p-2],hash:c[p-1],loc:l.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:c[p],loc:l.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:l.id(c[p-2]),value:c[p],loc:l.locInfo(this._$)};break;case 32:this.$=l.id(c[p-1]);break;case 35:this.$={type:"StringLiteral",value:c[p],original:c[p],loc:l.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(c[p]),original:Number(c[p]),loc:l.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:c[p]==="true",original:c[p]==="true",loc:l.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:l.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:l.locInfo(this._$)};break;case 42:this.$=l.preparePath(!0,c[p],this._$);break;case 43:this.$=l.preparePath(!1,c[p],this._$);break;case 44:c[p-2].push({part:l.id(c[p]),original:c[p],separator:c[p-1]}),this.$=c[p-2];break;case 45:this.$=[{part:l.id(c[p]),original:c[p]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:c[p-1].push(c[p]);break;case 98:case 100:this.$=[c[p]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(o,i){throw new Error(o)},parse:function(o){var i=this,u=[0],l=[null],d=[],c=this.table,h="",p=0,m=0;this.lexer.setInput(o),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var y=this.lexer.yylloc;d.push(y);var f=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);for(var b,S,v,T,w,I,k,U,D,N={};;){if(S=u[u.length-1],this.defaultActions[S]?v=this.defaultActions[S]:(b==null&&(D=void 0,typeof(D=i.lexer.lex()||1)!="number"&&(D=i.symbols_[D]||D),b=D),v=c[S]&&c[S][b]),v===void 0||!v.length||!v[0]){var X="";for(w in U=[],c[S])this.terminals_[w]&&w>2&&U.push("'"+this.terminals_[w]+"'");X=this.lexer.showPosition?"Parse error on line "+(p+1)+`:
`+this.lexer.showPosition()+`
Expecting `+U.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(p+1)+": Unexpected "+(b==1?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(X,{text:this.lexer.match,token:this.terminals_[b]||b,line:this.lexer.yylineno,loc:y,expected:U})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+b);switch(v[0]){case 1:u.push(b),l.push(this.lexer.yytext),d.push(this.lexer.yylloc),u.push(v[1]),b=null,m=this.lexer.yyleng,h=this.lexer.yytext,p=this.lexer.yylineno,y=this.lexer.yylloc;break;case 2:if(I=this.productions_[v[1]][1],N.$=l[l.length-I],N._$={first_line:d[d.length-(I||1)].first_line,last_line:d[d.length-1].last_line,first_column:d[d.length-(I||1)].first_column,last_column:d[d.length-1].last_column},f&&(N._$.range=[d[d.length-(I||1)].range[0],d[d.length-1].range[1]]),(T=this.performAction.call(N,h,m,p,this.yy,v[1],l,d))!==void 0)return T;I&&(u=u.slice(0,-1*I*2),l=l.slice(0,-1*I),d=d.slice(0,-1*I)),u.push(this.productions_[v[1]][0]),l.push(N.$),d.push(N._$),k=c[u[u.length-2]][u[u.length-1]],u.push(k);break;case 3:return!0}}return!0}},a=function(){var o={EOF:1,parseError:function(i,u){if(!this.yy.parser)throw new Error(i);this.yy.parser.parseError(i,u)},setInput:function(i){return this._input=i,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var i=this._input[0];return this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i,i.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},unput:function(i){var u=i.length,l=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-u-1),this.offset-=u;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var c=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===d.length?this.yylloc.first_column:0)+d[d.length-l.length].length-l[0].length:this.yylloc.first_column-u},this.options.ranges&&(this.yylloc.range=[c[0],c[0]+this.yyleng-u]),this},more:function(){return this._more=!0,this},less:function(i){this.unput(this.match.slice(i))},pastInput:function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var i=this.pastInput(),u=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+u+"^"},next:function(){if(this.done)return this.EOF;var i,u,l,d,c;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),p=0;p<h.length&&(!(l=this._input.match(this.rules[h[p]]))||u&&!(l[0].length>u[0].length)||(u=l,d=p,this.options.flex));p++);return u?((c=u[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],i=this.performAction.call(this,this.yy,this,h[d],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),i||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var i=this.next();return i!==void 0?i:this.lex()},begin:function(i){this.conditionStack.push(i)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(i){this.begin(i)},options:{},performAction:function(i,u,l,d){function c(h,p){return u.yytext=u.yytext.substring(h,u.yyleng-p+h)}switch(l){case 0:if(u.yytext.slice(-2)==="\\\\"?(c(0,1),this.begin("mu")):u.yytext.slice(-1)==="\\"?(c(0,1),this.begin("emu")):this.begin("mu"),u.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(c(5,9),"END_RAW_BLOCK");case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(u.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return u.yytext=c(1,2).replace(/\\"/g,'"'),80;case 32:return u.yytext=c(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return u.yytext=u.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};return o}();function r(){this.yy={}}return s.lexer=a,r.prototype=s,s.Parser=r,new r}();t.default=n,e.exports=t.default})(ur,ur.exports);var zd=ur.exports,cr={exports:{}},dr={exports:{}};(function(e,t){t.__esModule=!0;var n=function(i){return i&&i.__esModule?i:{default:i}}(Te);function s(){this.parents=[]}function a(i){this.acceptRequired(i,"path"),this.acceptArray(i.params),this.acceptKey(i,"hash")}function r(i){a.call(this,i),this.acceptKey(i,"program"),this.acceptKey(i,"inverse")}function o(i){this.acceptRequired(i,"name"),this.acceptArray(i.params),this.acceptKey(i,"hash")}s.prototype={constructor:s,mutating:!1,acceptKey:function(i,u){var l=this.accept(i[u]);if(this.mutating){if(l&&!s.prototype[l.type])throw new n.default('Unexpected node type "'+l.type+'" found when accepting '+u+" on "+i.type);i[u]=l}},acceptRequired:function(i,u){if(this.acceptKey(i,u),!i[u])throw new n.default(i.type+" requires "+u)},acceptArray:function(i){for(var u=0,l=i.length;u<l;u++)this.acceptKey(i,u),i[u]||(i.splice(u,1),u--,l--)},accept:function(i){if(i){if(!this[i.type])throw new n.default("Unknown type: "+i.type,i);this.current&&this.parents.unshift(this.current),this.current=i;var u=this[i.type](i);return this.current=this.parents.shift(),!this.mutating||u?u:u!==!1?i:void 0}},Program:function(i){this.acceptArray(i.body)},MustacheStatement:a,Decorator:a,BlockStatement:r,DecoratorBlock:r,PartialStatement:o,PartialBlockStatement:function(i){o.call(this,i),this.acceptKey(i,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:a,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(i){this.acceptArray(i.pairs)},HashPair:function(i){this.acceptRequired(i,"value")}},t.default=s,e.exports=t.default})(dr,dr.exports);var al=dr.exports;(function(e,t){t.__esModule=!0;var n=function(u){return u&&u.__esModule?u:{default:u}}(al);function s(){var u=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=u}function a(u,l,d){l===void 0&&(l=u.length);var c=u[l-1],h=u[l-2];return c?c.type==="ContentStatement"?(h||!d?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(c.original):void 0:d}function r(u,l,d){l===void 0&&(l=-1);var c=u[l+1],h=u[l+2];return c?c.type==="ContentStatement"?(h||!d?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(c.original):void 0:d}function o(u,l,d){var c=u[l==null?0:l+1];if(c&&c.type==="ContentStatement"&&(d||!c.rightStripped)){var h=c.value;c.value=c.value.replace(d?/^\s+/:/^[ \t]*\r?\n?/,""),c.rightStripped=c.value!==h}}function i(u,l,d){var c=u[l==null?u.length-1:l-1];if(c&&c.type==="ContentStatement"&&(d||!c.leftStripped)){var h=c.value;return c.value=c.value.replace(d?/\s+$/:/[ \t]+$/,""),c.leftStripped=c.value!==h,c.leftStripped}}s.prototype=new n.default,s.prototype.Program=function(u){var l=!this.options.ignoreStandalone,d=!this.isRootSeen;this.isRootSeen=!0;for(var c=u.body,h=0,p=c.length;h<p;h++){var m=c[h],y=this.accept(m);if(y){var f=a(c,h,d),b=r(c,h,d),S=y.openStandalone&&f,v=y.closeStandalone&&b,T=y.inlineStandalone&&f&&b;y.close&&o(c,h,!0),y.open&&i(c,h,!0),l&&T&&(o(c,h),i(c,h)&&m.type==="PartialStatement"&&(m.indent=/([ \t]+$)/.exec(c[h-1].original)[1])),l&&S&&(o((m.program||m.inverse).body),i(c,h)),l&&v&&(o(c,h),i((m.inverse||m.program).body))}}return u},s.prototype.BlockStatement=s.prototype.DecoratorBlock=s.prototype.PartialBlockStatement=function(u){this.accept(u.program),this.accept(u.inverse);var l=u.program||u.inverse,d=u.program&&u.inverse,c=d,h=d;if(d&&d.chained)for(c=d.body[0].program;h.chained;)h=h.body[h.body.length-1].program;var p={open:u.openStrip.open,close:u.closeStrip.close,openStandalone:r(l.body),closeStandalone:a((c||l).body)};if(u.openStrip.close&&o(l.body,null,!0),d){var m=u.inverseStrip;m.open&&i(l.body,null,!0),m.close&&o(c.body,null,!0),u.closeStrip.open&&i(h.body,null,!0),!this.options.ignoreStandalone&&a(l.body)&&r(c.body)&&(i(l.body),o(c.body))}else u.closeStrip.open&&i(l.body,null,!0);return p},s.prototype.Decorator=s.prototype.MustacheStatement=function(u){return u.strip},s.prototype.PartialStatement=s.prototype.CommentStatement=function(u){var l=u.strip||{};return{inlineStandalone:!0,open:l.open,close:l.close}},t.default=s,e.exports=t.default})(cr,cr.exports);var Xd=cr.exports,pe={};pe.__esModule=!0,pe.SourceLocation=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},pe.id=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},pe.stripFlags=function(e,t){return{open:e.charAt(2)==="~",close:t.charAt(t.length-3)==="~"}},pe.stripComment=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},pe.preparePath=function(e,t,n){n=this.locInfo(n);for(var s=e?"@":"",a=[],r=0,o=0,i=t.length;o<i;o++){var u=t[o].part,l=t[o].original!==u;if(s+=(t[o].separator||"")+u,l||u!==".."&&u!=="."&&u!=="this")a.push(u);else{if(a.length>0)throw new hr.default("Invalid path: "+s,{loc:n});u===".."&&r++}}return{type:"PathExpression",data:e,depth:r,parts:a,original:s,loc:n}},pe.prepareMustache=function(e,t,n,s,a,r){var o=s.charAt(3)||s.charAt(2),i=o!=="{"&&o!=="&";return{type:/\*/.test(s)?"Decorator":"MustacheStatement",path:e,params:t,hash:n,escaped:i,strip:a,loc:this.locInfo(r)}},pe.prepareRawBlock=function(e,t,n,s){Ss(e,n),s=this.locInfo(s);var a={type:"Program",body:t,strip:{},loc:s};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:a,openStrip:{},inverseStrip:{},closeStrip:{},loc:s}},pe.prepareBlock=function(e,t,n,s,a,r){s&&s.path&&Ss(e,s);var o=/\*/.test(e.open);t.blockParams=e.blockParams;var i=void 0,u=void 0;if(n){if(o)throw new hr.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=s.strip),u=n.strip,i=n.program}return a&&(a=i,i=t,t=a),{type:o?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:i,openStrip:e.strip,inverseStrip:u,closeStrip:s&&s.strip,loc:this.locInfo(r)}},pe.prepareProgram=function(e,t){if(!t&&e.length){var n=e[0].loc,s=e[e.length-1].loc;n&&s&&(t={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:s.end.line,column:s.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},pe.preparePartialBlock=function(e,t,n,s){return Ss(e,n),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:n&&n.strip,loc:this.locInfo(s)}};var hr=function(e){return e&&e.__esModule?e:{default:e}}(Te);function Ss(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var n={loc:e.path.loc};throw new hr.default(e.path.original+" doesn't match "+t,n)}}function ol(e){return e&&e.__esModule?e:{default:e}}Ut.__esModule=!0,Ut.parseWithoutProcessing=ho,Ut.parse=function(e,t){var n=ho(e,t);return new Jd.default(t).accept(n)};var pr=ol(zd),Jd=ol(Xd),Zd=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(pe),Qd=P;Ut.parser=pr.default;var En={};function ho(e,t){return e.type==="Program"?e:(pr.default.yy=En,En.locInfo=function(n){return new En.SourceLocation(t&&t.srcName,n)},pr.default.parse(e))}Qd.extend(En,Zd);var Mt={};function il(e){return e&&e.__esModule?e:{default:e}}Mt.__esModule=!0,Mt.Compiler=mr,Mt.precompile=function(e,t,n){if(e==null||typeof e!="string"&&e.type!=="Program")throw new $t.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var s=n.parse(e,t),a=new n.Compiler().compile(s,t);return new n.JavaScriptCompiler().compile(a,t)},Mt.compile=function(e,t,n){if(t===void 0&&(t={}),e==null||typeof e!="string"&&e.type!=="Program")throw new $t.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=Kt.extend({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var s=void 0;function a(){var o=n.parse(e,t),i=new n.Compiler().compile(o,t),u=new n.JavaScriptCompiler().compile(i,t,void 0,!0);return n.template(u)}function r(o,i){return s||(s=a()),s.call(this,o,i)}return r._setup=function(o){return s||(s=a()),s._setup(o)},r._child=function(o,i,u,l){return s||(s=a()),s._child(o,i,u,l)},r};var $t=il(Te),Kt=P,It=il(rl),eh=[].slice;function mr(){}function ll(e,t){if(e===t)return!0;if(Kt.isArray(e)&&Kt.isArray(t)&&e.length===t.length){for(var n=0;n<e.length;n++)if(!ll(e[n],t[n]))return!1;return!0}}function po(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}mr.prototype={compiler:mr,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var s=this.opcodes[n],a=e.opcodes[n];if(s.opcode!==a.opcode||!ll(s.args,a.args))return!1}for(t=this.children.length,n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=Kt.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),n=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[n]=t,this.useDepths=this.useDepths||t.useDepths,n},accept:function(e){if(!this[e.type])throw new $t.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,s=0;s<n;s++)this.accept(t[s]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){po(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var s=this.classifySexpr(e);s==="helper"?this.helperSexpr(e,t,n):s==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),s=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,s.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new $t.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var s=e.name.original,a=e.name.type==="SubExpression";a&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var r=e.indent||"";this.options.preventIndent&&r&&(this.opcode("appendContent",r),r=""),this.opcode("invokePartial",a,s,r),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){po(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var s=e.path,a=s.parts[0],r=t!=null||n!=null;this.opcode("getContext",s.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),s.strict=!0,this.accept(s),this.opcode("invokeAmbiguous",a,r)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var s=this.setupFullMustacheParams(e,t,n),a=e.path,r=a.parts[0];if(this.options.knownHelpers[r])this.opcode("invokeKnownHelper",s.length,r);else{if(this.options.knownHelpersOnly)throw new $t.default("You specified knownHelpersOnly, but used the unknown helper "+r,e);a.strict=!0,a.falsy=!0,this.accept(a),this.opcode("invokeHelper",s.length,a.original,It.default.helpers.simpleId(a))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=It.default.helpers.scopedId(e),s=!e.depth&&!n&&this.blockParamIndex(t);s?this.opcode("lookupBlockParam",s,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,s=t.length;for(this.opcode("pushHash");n<s;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:eh.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=It.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),s=!n&&It.default.helpers.helperExpression(e),a=!n&&(s||t);if(a&&!s){var r=e.path.parts[0],o=this.options;o.knownHelpers[r]?s=!0:o.knownHelpersOnly&&(a=!1)}return s?"helper":a?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(!e.parts||It.default.helpers.scopedId(e)||e.depth||(n=this.blockParamIndex(e.parts[0])),n){var s=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,s)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,s){var a=e.params;return this.pushParams(a),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",s),a},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var s=this.options.blockParams[t],a=s&&Kt.indexOf(s,e);if(s&&a>=0)return[t,a]}}};var mo,go,gr={exports:{}},fr={exports:{}},ln={},Es={},un={},cn={};function th(){if(mo)return cn;mo=1;var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");return cn.encode=function(t){if(0<=t&&t<e.length)return e[t];throw new TypeError("Must be between 0 and 63: "+t)},cn.decode=function(t){return 65<=t&&t<=90?t-65:97<=t&&t<=122?t-97+26:48<=t&&t<=57?t-48+52:t==43?62:t==47?63:-1},cn}function ul(){if(go)return un;go=1;var e=th();return un.encode=function(t){var n,s="",a=function(r){return r<0?1+(-r<<1):0+(r<<1)}(t);do n=31&a,(a>>>=5)>0&&(n|=32),s+=e.encode(n);while(a>0);return s},un.decode=function(t,n,s){var a,r,o,i,u=t.length,l=0,d=0;do{if(n>=u)throw new Error("Expected more digits in base 64 VLQ value.");if((r=e.decode(t.charCodeAt(n++)))===-1)throw new Error("Invalid base64 digit: "+t.charAt(n-1));a=!!(32&r),l+=(r&=31)<<d,d+=5}while(a);s.value=(i=(o=l)>>1,1&~o?i:-i),s.rest=n},un}var fo,yo={};function Wt(){return fo||(fo=1,function(e){e.getArg=function(c,h,p){if(h in c)return c[h];if(arguments.length===3)return p;throw new Error('"'+h+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function s(c){var h=c.match(t);return h?{scheme:h[1],auth:h[2],host:h[3],port:h[4],path:h[5]}:null}function a(c){var h="";return c.scheme&&(h+=c.scheme+":"),h+="//",c.auth&&(h+=c.auth+"@"),c.host&&(h+=c.host),c.port&&(h+=":"+c.port),c.path&&(h+=c.path),h}function r(c){var h=c,p=s(c);if(p){if(!p.path)return c;h=p.path}for(var m,y=e.isAbsolute(h),f=h.split(/\/+/),b=0,S=f.length-1;S>=0;S--)(m=f[S])==="."?f.splice(S,1):m===".."?b++:b>0&&(m===""?(f.splice(S+1,b),b=0):(f.splice(S,2),b--));return(h=f.join("/"))===""&&(h=y?"/":"."),p?(p.path=h,a(p)):h}function o(c,h){c===""&&(c="."),h===""&&(h=".");var p=s(h),m=s(c);if(m&&(c=m.path||"/"),p&&!p.scheme)return m&&(p.scheme=m.scheme),a(p);if(p||h.match(n))return h;if(m&&!m.host&&!m.path)return m.host=h,a(m);var y=h.charAt(0)==="/"?h:r(c.replace(/\/+$/,"")+"/"+h);return m?(m.path=y,a(m)):y}e.urlParse=s,e.urlGenerate=a,e.normalize=r,e.join=o,e.isAbsolute=function(c){return c.charAt(0)==="/"||t.test(c)},e.relative=function(c,h){c===""&&(c="."),c=c.replace(/\/$/,"");for(var p=0;h.indexOf(c+"/")!==0;){var m=c.lastIndexOf("/");if(m<0||(c=c.slice(0,m)).match(/^([^\/]+:\/)?\/*$/))return h;++p}return Array(p+1).join("../")+h.substr(c.length+1)};var i=!("__proto__"in Object.create(null));function u(c){return c}function l(c){if(!c)return!1;var h=c.length;if(h<9||c.charCodeAt(h-1)!==95||c.charCodeAt(h-2)!==95||c.charCodeAt(h-3)!==111||c.charCodeAt(h-4)!==116||c.charCodeAt(h-5)!==111||c.charCodeAt(h-6)!==114||c.charCodeAt(h-7)!==112||c.charCodeAt(h-8)!==95||c.charCodeAt(h-9)!==95)return!1;for(var p=h-10;p>=0;p--)if(c.charCodeAt(p)!==36)return!1;return!0}function d(c,h){return c===h?0:c===null?1:h===null?-1:c>h?1:-1}e.toSetString=i?u:function(c){return l(c)?"$"+c:c},e.fromSetString=i?u:function(c){return l(c)?c.slice(1):c},e.compareByOriginalPositions=function(c,h,p){var m=d(c.source,h.source);return m!==0||(m=c.originalLine-h.originalLine)!==0||(m=c.originalColumn-h.originalColumn)!==0||p||(m=c.generatedColumn-h.generatedColumn)!==0||(m=c.generatedLine-h.generatedLine)!==0?m:d(c.name,h.name)},e.compareByGeneratedPositionsDeflated=function(c,h,p){var m=c.generatedLine-h.generatedLine;return m!==0||(m=c.generatedColumn-h.generatedColumn)!==0||p||(m=d(c.source,h.source))!==0||(m=c.originalLine-h.originalLine)!==0||(m=c.originalColumn-h.originalColumn)!==0?m:d(c.name,h.name)},e.compareByGeneratedPositionsInflated=function(c,h){var p=c.generatedLine-h.generatedLine;return p!==0||(p=c.generatedColumn-h.generatedColumn)!==0||(p=d(c.source,h.source))!==0||(p=c.originalLine-h.originalLine)!==0||(p=c.originalColumn-h.originalColumn)!==0?p:d(c.name,h.name)},e.parseSourceMapInput=function(c){return JSON.parse(c.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(c,h,p){if(h=h||"",c&&(c[c.length-1]!=="/"&&h[0]!=="/"&&(c+="/"),h=c+h),p){var m=s(p);if(!m)throw new Error("sourceMapURL could not be parsed");if(m.path){var y=m.path.lastIndexOf("/");y>=0&&(m.path=m.path.substring(0,y+1))}h=o(a(m),h)}return r(h)}}(yo)),yo}var _o,Ts={};function cl(){if(_o)return Ts;_o=1;var e=Wt(),t=Object.prototype.hasOwnProperty,n=typeof Map<"u";function s(){this._array=[],this._set=n?new Map:Object.create(null)}return s.fromArray=function(a,r){for(var o=new s,i=0,u=a.length;i<u;i++)o.add(a[i],r);return o},s.prototype.size=function(){return n?this._set.size:Object.getOwnPropertyNames(this._set).length},s.prototype.add=function(a,r){var o=n?a:e.toSetString(a),i=n?this.has(a):t.call(this._set,o),u=this._array.length;i&&!r||this._array.push(a),i||(n?this._set.set(a,u):this._set[o]=u)},s.prototype.has=function(a){if(n)return this._set.has(a);var r=e.toSetString(a);return t.call(this._set,r)},s.prototype.indexOf=function(a){if(n){var r=this._set.get(a);if(r>=0)return r}else{var o=e.toSetString(a);if(t.call(this._set,o))return this._set[o]}throw new Error('"'+a+'" is not in the set.')},s.prototype.at=function(a){if(a>=0&&a<this._array.length)return this._array[a];throw new Error("No element indexed by "+a)},s.prototype.toArray=function(){return this._array.slice()},Ts.ArraySet=s,Ts}var bo,vo,ws={};function nh(){if(bo)return ws;bo=1;var e=Wt();function t(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}return t.prototype.unsortedForEach=function(n,s){this._array.forEach(n,s)},t.prototype.add=function(n){var s,a,r,o,i,u;s=this._last,a=n,r=s.generatedLine,o=a.generatedLine,i=s.generatedColumn,u=a.generatedColumn,o>r||o==r&&u>=i||e.compareByGeneratedPositionsInflated(s,a)<=0?(this._last=n,this._array.push(n)):(this._sorted=!1,this._array.push(n))},t.prototype.toArray=function(){return this._sorted||(this._array.sort(e.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},ws.MappingList=t,ws}function So(){if(vo)return Es;vo=1;var e=ul(),t=Wt(),n=cl().ArraySet,s=nh().MappingList;function a(r){r||(r={}),this._file=t.getArg(r,"file",null),this._sourceRoot=t.getArg(r,"sourceRoot",null),this._skipValidation=t.getArg(r,"skipValidation",!1),this._sources=new n,this._names=new n,this._mappings=new s,this._sourcesContents=null}return a.prototype._version=3,a.fromSourceMap=function(r){var o=r.sourceRoot,i=new a({file:r.file,sourceRoot:o});return r.eachMapping(function(u){var l={generated:{line:u.generatedLine,column:u.generatedColumn}};u.source!=null&&(l.source=u.source,o!=null&&(l.source=t.relative(o,l.source)),l.original={line:u.originalLine,column:u.originalColumn},u.name!=null&&(l.name=u.name)),i.addMapping(l)}),r.sources.forEach(function(u){var l=u;o!==null&&(l=t.relative(o,u)),i._sources.has(l)||i._sources.add(l);var d=r.sourceContentFor(u);d!=null&&i.setSourceContent(u,d)}),i},a.prototype.addMapping=function(r){var o=t.getArg(r,"generated"),i=t.getArg(r,"original",null),u=t.getArg(r,"source",null),l=t.getArg(r,"name",null);this._skipValidation||this._validateMapping(o,i,u,l),u!=null&&(u=String(u),this._sources.has(u)||this._sources.add(u)),l!=null&&(l=String(l),this._names.has(l)||this._names.add(l)),this._mappings.add({generatedLine:o.line,generatedColumn:o.column,originalLine:i!=null&&i.line,originalColumn:i!=null&&i.column,source:u,name:l})},a.prototype.setSourceContent=function(r,o){var i=r;this._sourceRoot!=null&&(i=t.relative(this._sourceRoot,i)),o!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[t.toSetString(i)]=o):this._sourcesContents&&(delete this._sourcesContents[t.toSetString(i)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))},a.prototype.applySourceMap=function(r,o,i){var u=o;if(o==null){if(r.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);u=r.file}var l=this._sourceRoot;l!=null&&(u=t.relative(l,u));var d=new n,c=new n;this._mappings.unsortedForEach(function(h){if(h.source===u&&h.originalLine!=null){var p=r.originalPositionFor({line:h.originalLine,column:h.originalColumn});p.source!=null&&(h.source=p.source,i!=null&&(h.source=t.join(i,h.source)),l!=null&&(h.source=t.relative(l,h.source)),h.originalLine=p.line,h.originalColumn=p.column,p.name!=null&&(h.name=p.name))}var m=h.source;m==null||d.has(m)||d.add(m);var y=h.name;y==null||c.has(y)||c.add(y)},this),this._sources=d,this._names=c,r.sources.forEach(function(h){var p=r.sourceContentFor(h);p!=null&&(i!=null&&(h=t.join(i,h)),l!=null&&(h=t.relative(l,h)),this.setSourceContent(h,p))},this)},a.prototype._validateMapping=function(r,o,i,u){if(o&&typeof o.line!="number"&&typeof o.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(r&&"line"in r&&"column"in r&&r.line>0&&r.column>=0)||o||i||u)&&!(r&&"line"in r&&"column"in r&&o&&"line"in o&&"column"in o&&r.line>0&&r.column>=0&&o.line>0&&o.column>=0&&i))throw new Error("Invalid mapping: "+JSON.stringify({generated:r,source:i,original:o,name:u}))},a.prototype._serializeMappings=function(){for(var r,o,i,u,l=0,d=1,c=0,h=0,p=0,m=0,y="",f=this._mappings.toArray(),b=0,S=f.length;b<S;b++){if(r="",(o=f[b]).generatedLine!==d)for(l=0;o.generatedLine!==d;)r+=";",d++;else if(b>0){if(!t.compareByGeneratedPositionsInflated(o,f[b-1]))continue;r+=","}r+=e.encode(o.generatedColumn-l),l=o.generatedColumn,o.source!=null&&(u=this._sources.indexOf(o.source),r+=e.encode(u-m),m=u,r+=e.encode(o.originalLine-1-h),h=o.originalLine-1,r+=e.encode(o.originalColumn-c),c=o.originalColumn,o.name!=null&&(i=this._names.indexOf(o.name),r+=e.encode(i-p),p=i)),y+=r}return y},a.prototype._generateSourcesContent=function(r,o){return r.map(function(i){if(!this._sourcesContents)return null;o!=null&&(i=t.relative(o,i));var u=t.toSetString(i);return Object.prototype.hasOwnProperty.call(this._sourcesContents,u)?this._sourcesContents[u]:null},this)},a.prototype.toJSON=function(){var r={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(r.file=this._file),this._sourceRoot!=null&&(r.sourceRoot=this._sourceRoot),this._sourcesContents&&(r.sourcesContent=this._generateSourcesContent(r.sources,r.sourceRoot)),r},a.prototype.toString=function(){return JSON.stringify(this.toJSON())},Es.SourceMapGenerator=a,Es}var Eo,Nt={},To={};function sh(){return Eo||(Eo=1,function(e){function t(n,s,a,r,o,i){var u=Math.floor((s-n)/2)+n,l=o(a,r[u],!0);return l===0?u:l>0?s-u>1?t(u,s,a,r,o,i):i==e.LEAST_UPPER_BOUND?s<r.length?s:-1:u:u-n>1?t(n,u,a,r,o,i):i==e.LEAST_UPPER_BOUND?u:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,s,a,r){if(s.length===0)return-1;var o=t(-1,s.length,n,s,a,r||e.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&a(s[o],s[o-1],!0)===0;)--o;return o}}(To)),To}var wo,Io,Is={};function rh(){if(wo)return Is;function e(n,s,a){var r=n[s];n[s]=n[a],n[a]=r}function t(n,s,a,r){if(a<r){var o=a-1;e(n,(d=a,c=r,Math.round(d+Math.random()*(c-d))),r);for(var i=n[r],u=a;u<r;u++)s(n[u],i)<=0&&e(n,o+=1,u);e(n,o+1,u);var l=o+1;t(n,s,a,l-1),t(n,s,l+1,r)}var d,c}return wo=1,Is.quickSort=function(n,s){t(n,s,0,n.length-1)},Is}var No,ko,Ns={};function ah(){return ko||(ko=1,ln.SourceMapGenerator=So().SourceMapGenerator,ln.SourceMapConsumer=function(){if(Io)return Nt;Io=1;var e=Wt(),t=sh(),n=cl().ArraySet,s=ul(),a=rh().quickSort;function r(l,d){var c=l;return typeof l=="string"&&(c=e.parseSourceMapInput(l)),c.sections!=null?new u(c,d):new o(c,d)}function o(l,d){var c=l;typeof l=="string"&&(c=e.parseSourceMapInput(l));var h=e.getArg(c,"version"),p=e.getArg(c,"sources"),m=e.getArg(c,"names",[]),y=e.getArg(c,"sourceRoot",null),f=e.getArg(c,"sourcesContent",null),b=e.getArg(c,"mappings"),S=e.getArg(c,"file",null);if(h!=this._version)throw new Error("Unsupported version: "+h);y&&(y=e.normalize(y)),p=p.map(String).map(e.normalize).map(function(v){return y&&e.isAbsolute(y)&&e.isAbsolute(v)?e.relative(y,v):v}),this._names=n.fromArray(m.map(String),!0),this._sources=n.fromArray(p,!0),this._absoluteSources=this._sources.toArray().map(function(v){return e.computeSourceURL(y,v,d)}),this.sourceRoot=y,this.sourcesContent=f,this._mappings=b,this._sourceMapURL=d,this.file=S}function i(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function u(l,d){var c=l;typeof l=="string"&&(c=e.parseSourceMapInput(l));var h=e.getArg(c,"version"),p=e.getArg(c,"sections");if(h!=this._version)throw new Error("Unsupported version: "+h);this._sources=new n,this._names=new n;var m={line:-1,column:0};this._sections=p.map(function(y){if(y.url)throw new Error("Support for url field in sections not implemented.");var f=e.getArg(y,"offset"),b=e.getArg(f,"line"),S=e.getArg(f,"column");if(b<m.line||b===m.line&&S<m.column)throw new Error("Section offsets must be ordered and non-overlapping.");return m=f,{generatedOffset:{generatedLine:b+1,generatedColumn:S+1},consumer:new r(e.getArg(y,"map"),d)}})}return r.fromSourceMap=function(l,d){return o.fromSourceMap(l,d)},r.prototype._version=3,r.prototype.__generatedMappings=null,Object.defineProperty(r.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),r.prototype.__originalMappings=null,Object.defineProperty(r.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),r.prototype._charIsMappingSeparator=function(l,d){var c=l.charAt(d);return c===";"||c===","},r.prototype._parseMappings=function(l,d){throw new Error("Subclasses must implement _parseMappings")},r.GENERATED_ORDER=1,r.ORIGINAL_ORDER=2,r.GREATEST_LOWER_BOUND=1,r.LEAST_UPPER_BOUND=2,r.prototype.eachMapping=function(l,d,c){var h,p=d||null;switch(c||r.GENERATED_ORDER){case r.GENERATED_ORDER:h=this._generatedMappings;break;case r.ORIGINAL_ORDER:h=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var m=this.sourceRoot;h.map(function(y){var f=y.source===null?null:this._sources.at(y.source);return{source:f=e.computeSourceURL(m,f,this._sourceMapURL),generatedLine:y.generatedLine,generatedColumn:y.generatedColumn,originalLine:y.originalLine,originalColumn:y.originalColumn,name:y.name===null?null:this._names.at(y.name)}},this).forEach(l,p)},r.prototype.allGeneratedPositionsFor=function(l){var d=e.getArg(l,"line"),c={source:e.getArg(l,"source"),originalLine:d,originalColumn:e.getArg(l,"column",0)};if(c.source=this._findSourceIndex(c.source),c.source<0)return[];var h=[],p=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,t.LEAST_UPPER_BOUND);if(p>=0){var m=this._originalMappings[p];if(l.column===void 0)for(var y=m.originalLine;m&&m.originalLine===y;)h.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++p];else for(var f=m.originalColumn;m&&m.originalLine===d&&m.originalColumn==f;)h.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++p]}return h},Nt.SourceMapConsumer=r,o.prototype=Object.create(r.prototype),o.prototype.consumer=r,o.prototype._findSourceIndex=function(l){var d,c=l;if(this.sourceRoot!=null&&(c=e.relative(this.sourceRoot,c)),this._sources.has(c))return this._sources.indexOf(c);for(d=0;d<this._absoluteSources.length;++d)if(this._absoluteSources[d]==l)return d;return-1},o.fromSourceMap=function(l,d){var c=Object.create(o.prototype),h=c._names=n.fromArray(l._names.toArray(),!0),p=c._sources=n.fromArray(l._sources.toArray(),!0);c.sourceRoot=l._sourceRoot,c.sourcesContent=l._generateSourcesContent(c._sources.toArray(),c.sourceRoot),c.file=l._file,c._sourceMapURL=d,c._absoluteSources=c._sources.toArray().map(function(w){return e.computeSourceURL(c.sourceRoot,w,d)});for(var m=l._mappings.toArray().slice(),y=c.__generatedMappings=[],f=c.__originalMappings=[],b=0,S=m.length;b<S;b++){var v=m[b],T=new i;T.generatedLine=v.generatedLine,T.generatedColumn=v.generatedColumn,v.source&&(T.source=p.indexOf(v.source),T.originalLine=v.originalLine,T.originalColumn=v.originalColumn,v.name&&(T.name=h.indexOf(v.name)),f.push(T)),y.push(T)}return a(c.__originalMappings,e.compareByOriginalPositions),c},o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),o.prototype._parseMappings=function(l,d){for(var c,h,p,m,y,f=1,b=0,S=0,v=0,T=0,w=0,I=l.length,k=0,U={},D={},N=[],X=[];k<I;)if(l.charAt(k)===";")f++,k++,b=0;else if(l.charAt(k)===",")k++;else{for((c=new i).generatedLine=f,m=k;m<I&&!this._charIsMappingSeparator(l,m);m++);if(p=U[h=l.slice(k,m)])k+=h.length;else{for(p=[];k<m;)s.decode(l,k,D),y=D.value,k=D.rest,p.push(y);if(p.length===2)throw new Error("Found a source, but no line and column");if(p.length===3)throw new Error("Found a source and line, but no column");U[h]=p}c.generatedColumn=b+p[0],b=c.generatedColumn,p.length>1&&(c.source=T+p[1],T+=p[1],c.originalLine=S+p[2],S=c.originalLine,c.originalLine+=1,c.originalColumn=v+p[3],v=c.originalColumn,p.length>4&&(c.name=w+p[4],w+=p[4])),X.push(c),typeof c.originalLine=="number"&&N.push(c)}a(X,e.compareByGeneratedPositionsDeflated),this.__generatedMappings=X,a(N,e.compareByOriginalPositions),this.__originalMappings=N},o.prototype._findMapping=function(l,d,c,h,p,m){if(l[c]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+l[c]);if(l[h]<0)throw new TypeError("Column must be greater than or equal to 0, got "+l[h]);return t.search(l,d,p,m)},o.prototype.computeColumnSpans=function(){for(var l=0;l<this._generatedMappings.length;++l){var d=this._generatedMappings[l];if(l+1<this._generatedMappings.length){var c=this._generatedMappings[l+1];if(d.generatedLine===c.generatedLine){d.lastGeneratedColumn=c.generatedColumn-1;continue}}d.lastGeneratedColumn=1/0}},o.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},c=this._findMapping(d,this._generatedMappings,"generatedLine","generatedColumn",e.compareByGeneratedPositionsDeflated,e.getArg(l,"bias",r.GREATEST_LOWER_BOUND));if(c>=0){var h=this._generatedMappings[c];if(h.generatedLine===d.generatedLine){var p=e.getArg(h,"source",null);p!==null&&(p=this._sources.at(p),p=e.computeSourceURL(this.sourceRoot,p,this._sourceMapURL));var m=e.getArg(h,"name",null);return m!==null&&(m=this._names.at(m)),{source:p,line:e.getArg(h,"originalLine",null),column:e.getArg(h,"originalColumn",null),name:m}}}return{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(l){return l==null})},o.prototype.sourceContentFor=function(l,d){if(!this.sourcesContent)return null;var c=this._findSourceIndex(l);if(c>=0)return this.sourcesContent[c];var h,p=l;if(this.sourceRoot!=null&&(p=e.relative(this.sourceRoot,p)),this.sourceRoot!=null&&(h=e.urlParse(this.sourceRoot))){var m=p.replace(/^file:\/\//,"");if(h.scheme=="file"&&this._sources.has(m))return this.sourcesContent[this._sources.indexOf(m)];if((!h.path||h.path=="/")&&this._sources.has("/"+p))return this.sourcesContent[this._sources.indexOf("/"+p)]}if(d)return null;throw new Error('"'+p+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(l){var d=e.getArg(l,"source");if((d=this._findSourceIndex(d))<0)return{line:null,column:null,lastColumn:null};var c={source:d,originalLine:e.getArg(l,"line"),originalColumn:e.getArg(l,"column")},h=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,e.getArg(l,"bias",r.GREATEST_LOWER_BOUND));if(h>=0){var p=this._originalMappings[h];if(p.source===c.source)return{line:e.getArg(p,"generatedLine",null),column:e.getArg(p,"generatedColumn",null),lastColumn:e.getArg(p,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},Nt.BasicSourceMapConsumer=o,u.prototype=Object.create(r.prototype),u.prototype.constructor=r,u.prototype._version=3,Object.defineProperty(u.prototype,"sources",{get:function(){for(var l=[],d=0;d<this._sections.length;d++)for(var c=0;c<this._sections[d].consumer.sources.length;c++)l.push(this._sections[d].consumer.sources[c]);return l}}),u.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},c=t.search(d,this._sections,function(p,m){return p.generatedLine-m.generatedOffset.generatedLine||p.generatedColumn-m.generatedOffset.generatedColumn}),h=this._sections[c];return h?h.consumer.originalPositionFor({line:d.generatedLine-(h.generatedOffset.generatedLine-1),column:d.generatedColumn-(h.generatedOffset.generatedLine===d.generatedLine?h.generatedOffset.generatedColumn-1:0),bias:l.bias}):{source:null,line:null,column:null,name:null}},u.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(l){return l.consumer.hasContentsOfAllSources()})},u.prototype.sourceContentFor=function(l,d){for(var c=0;c<this._sections.length;c++){var h=this._sections[c].consumer.sourceContentFor(l,!0);if(h)return h}if(d)return null;throw new Error('"'+l+'" is not in the SourceMap.')},u.prototype.generatedPositionFor=function(l){for(var d=0;d<this._sections.length;d++){var c=this._sections[d];if(c.consumer._findSourceIndex(e.getArg(l,"source"))!==-1){var h=c.consumer.generatedPositionFor(l);if(h)return{line:h.line+(c.generatedOffset.generatedLine-1),column:h.column+(c.generatedOffset.generatedLine===h.line?c.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},u.prototype._parseMappings=function(l,d){this.__generatedMappings=[],this.__originalMappings=[];for(var c=0;c<this._sections.length;c++)for(var h=this._sections[c],p=h.consumer._generatedMappings,m=0;m<p.length;m++){var y=p[m],f=h.consumer._sources.at(y.source);f=e.computeSourceURL(h.consumer.sourceRoot,f,this._sourceMapURL),this._sources.add(f),f=this._sources.indexOf(f);var b=null;y.name&&(b=h.consumer._names.at(y.name),this._names.add(b),b=this._names.indexOf(b));var S={source:f,generatedLine:y.generatedLine+(h.generatedOffset.generatedLine-1),generatedColumn:y.generatedColumn+(h.generatedOffset.generatedLine===y.generatedLine?h.generatedOffset.generatedColumn-1:0),originalLine:y.originalLine,originalColumn:y.originalColumn,name:b};this.__generatedMappings.push(S),typeof S.originalLine=="number"&&this.__originalMappings.push(S)}a(this.__generatedMappings,e.compareByGeneratedPositionsDeflated),a(this.__originalMappings,e.compareByOriginalPositions)},Nt.IndexedSourceMapConsumer=u,Nt}().SourceMapConsumer,ln.SourceNode=function(){if(No)return Ns;No=1;var e=So().SourceMapGenerator,t=Wt(),n=/(\r?\n)/,s="$$$isSourceNode$$$";function a(r,o,i,u,l){this.children=[],this.sourceContents={},this.line=r??null,this.column=o??null,this.source=i??null,this.name=l??null,this[s]=!0,u!=null&&this.add(u)}return a.fromStringWithSourceMap=function(r,o,i){var u=new a,l=r.split(n),d=0,c=function(){return f()+(f()||"");function f(){return d<l.length?l[d++]:void 0}},h=1,p=0,m=null;return o.eachMapping(function(f){if(m!==null){if(!(h<f.generatedLine)){var b=(S=l[d]||"").substr(0,f.generatedColumn-p);return l[d]=S.substr(f.generatedColumn-p),p=f.generatedColumn,y(m,b),void(m=f)}y(m,c()),h++,p=0}for(;h<f.generatedLine;)u.add(c()),h++;if(p<f.generatedColumn){var S=l[d]||"";u.add(S.substr(0,f.generatedColumn)),l[d]=S.substr(f.generatedColumn),p=f.generatedColumn}m=f},this),d<l.length&&(m&&y(m,c()),u.add(l.splice(d).join(""))),o.sources.forEach(function(f){var b=o.sourceContentFor(f);b!=null&&(i!=null&&(f=t.join(i,f)),u.setSourceContent(f,b))}),u;function y(f,b){if(f===null||f.source===void 0)u.add(b);else{var S=i?t.join(i,f.source):f.source;u.add(new a(f.originalLine,f.originalColumn,S,b,f.name))}}},a.prototype.add=function(r){if(Array.isArray(r))r.forEach(function(o){this.add(o)},this);else{if(!r[s]&&typeof r!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+r);r&&this.children.push(r)}return this},a.prototype.prepend=function(r){if(Array.isArray(r))for(var o=r.length-1;o>=0;o--)this.prepend(r[o]);else{if(!r[s]&&typeof r!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+r);this.children.unshift(r)}return this},a.prototype.walk=function(r){for(var o,i=0,u=this.children.length;i<u;i++)(o=this.children[i])[s]?o.walk(r):o!==""&&r(o,{source:this.source,line:this.line,column:this.column,name:this.name})},a.prototype.join=function(r){var o,i,u=this.children.length;if(u>0){for(o=[],i=0;i<u-1;i++)o.push(this.children[i]),o.push(r);o.push(this.children[i]),this.children=o}return this},a.prototype.replaceRight=function(r,o){var i=this.children[this.children.length-1];return i[s]?i.replaceRight(r,o):typeof i=="string"?this.children[this.children.length-1]=i.replace(r,o):this.children.push("".replace(r,o)),this},a.prototype.setSourceContent=function(r,o){this.sourceContents[t.toSetString(r)]=o},a.prototype.walkSourceContents=function(r){for(var o=0,i=this.children.length;o<i;o++)this.children[o][s]&&this.children[o].walkSourceContents(r);var u=Object.keys(this.sourceContents);for(o=0,i=u.length;o<i;o++)r(t.fromSetString(u[o]),this.sourceContents[u[o]])},a.prototype.toString=function(){var r="";return this.walk(function(o){r+=o}),r},a.prototype.toStringWithSourceMap=function(r){var o={code:"",line:1,column:0},i=new e(r),u=!1,l=null,d=null,c=null,h=null;return this.walk(function(p,m){o.code+=p,m.source!==null&&m.line!==null&&m.column!==null?(l===m.source&&d===m.line&&c===m.column&&h===m.name||i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name}),l=m.source,d=m.line,c=m.column,h=m.name,u=!0):u&&(i.addMapping({generated:{line:o.line,column:o.column}}),l=null,u=!1);for(var y=0,f=p.length;y<f;y++)p.charCodeAt(y)===10?(o.line++,o.column=0,y+1===f?(l=null,u=!1):u&&i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name})):o.column++}),this.walkSourceContents(function(p,m){i.setSourceContent(p,m)}),{code:o.code,map:i}},Ns.SourceNode=a,Ns}().SourceNode),ln}(function(e,t){t.__esModule=!0;var n=P,s=void 0;try{var a=ah();s=a.SourceNode}catch{}function r(i,u,l){if(n.isArray(i)){for(var d=[],c=0,h=i.length;c<h;c++)d.push(u.wrap(i[c],l));return d}return typeof i=="boolean"||typeof i=="number"?i+"":i}function o(i){this.srcFile=i,this.source=[]}s||((s=function(i,u,l,d){this.src="",d&&this.add(d)}).prototype={add:function(i){n.isArray(i)&&(i=i.join("")),this.src+=i},prepend:function(i){n.isArray(i)&&(i=i.join("")),this.src=i+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),o.prototype={isEmpty:function(){return!this.source.length},prepend:function(i,u){this.source.unshift(this.wrap(i,u))},push:function(i,u){this.source.push(this.wrap(i,u))},merge:function(){var i=this.empty();return this.each(function(u){i.add(["  ",u,`
`])}),i},each:function(i){for(var u=0,l=this.source.length;u<l;u++)i(this.source[u])},empty:function(){var i=this.currentLocation||{start:{}};return new s(i.start.line,i.start.column,this.srcFile)},wrap:function(i){var u=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return i instanceof s?i:(i=r(i,this,u),new s(u.start.line,u.start.column,this.srcFile,i))},functionCall:function(i,u,l){return l=this.generateList(l),this.wrap([i,u?"."+u+"(":"(",l,")"])},quotedString:function(i){return'"'+(i+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(i){var u=this,l=[];Object.keys(i).forEach(function(c){var h=r(i[c],u);h!=="undefined"&&l.push([u.quotedString(c),":",h])});var d=this.generateList(l);return d.prepend("{"),d.add("}"),d},generateList:function(i){for(var u=this.empty(),l=0,d=i.length;l<d;l++)l&&u.add(","),u.add(r(i[l],this));return u},generateArray:function(i){var u=this.generateList(i);return u.prepend("["),u.add("]"),u}},t.default=o,e.exports=t.default})(fr,fr.exports);var oh=fr.exports;(function(e,t){function n(l){return l&&l.__esModule?l:{default:l}}t.__esModule=!0;var s=ge,a=n(Te),r=P,o=n(oh);function i(l){this.value=l}function u(){}u.prototype={nameLookup:function(l,d){return this.internalNameLookup(l,d)},depthedLookup:function(l){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(l),")"]},compilerInfo:function(){var l=s.COMPILER_REVISION;return[l,s.REVISION_CHANGES[l]]},appendToBuffer:function(l,d,c){return r.isArray(l)||(l=[l]),l=this.source.wrap(l,d),this.environment.isSimple?["return ",l,";"]:c?["buffer += ",l,";"]:(l.appendToBuffer=!0,l)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(l,d){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",l,",",JSON.stringify(d),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(l,d,c,h){this.environment=l,this.options=d,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!h,this.name=this.environment.name,this.isChild=!!c,this.context=c||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(l,d),this.useDepths=this.useDepths||l.useDepths||l.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||l.useBlockParams;var p=l.opcodes,m=void 0,y=void 0,f=void 0,b=void 0;for(f=0,b=p.length;f<b;f++)m=p[f],this.source.currentLocation=m.loc,y=y||m.loc,this[m.opcode].apply(this,m.args);if(this.source.currentLocation=y,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new a.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),h?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var S=this.createFunctionContext(h);if(this.isChild)return S;var v={compiler:this.compilerInfo(),main:S};this.decorators&&(v.main_d=this.decorators,v.useDecorators=!0);var T=this.context,w=T.programs,I=T.decorators;for(f=0,b=w.length;f<b;f++)w[f]&&(v[f]=w[f],I[f]&&(v[f+"_d"]=I[f],v.useDecorators=!0));return this.environment.usePartial&&(v.usePartial=!0),this.options.data&&(v.useData=!0),this.useDepths&&(v.useDepths=!0),this.useBlockParams&&(v.useBlockParams=!0),this.options.compat&&(v.compat=!0),h?v.compilerOptions=this.options:(v.compiler=JSON.stringify(v.compiler),this.source.currentLocation={start:{line:1,column:0}},v=this.objectLiteral(v),d.srcName?(v=v.toStringWithSourceMap({file:d.destName})).map=v.map&&v.map.toString():v=v.toString()),v},preamble:function(){this.lastContext=0,this.source=new o.default(this.options.srcName),this.decorators=new o.default(this.options.srcName)},createFunctionContext:function(l){var d=this,c="",h=this.stackVars.concat(this.registers.list);h.length>0&&(c+=", "+h.join(", "));var p=0;Object.keys(this.aliases).forEach(function(f){var b=d.aliases[f];b.children&&b.referenceCount>1&&(c+=", alias"+ ++p+"="+f,b.children[0]="alias"+p)}),this.lookupPropertyFunctionIsUsed&&(c+=", "+this.lookupPropertyFunctionVarDeclaration());var m=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&m.push("blockParams"),this.useDepths&&m.push("depths");var y=this.mergeSource(c);return l?(m.push(y),Function.apply(this,m)):this.source.wrap(["function(",m.join(","),`) {
  `,y,"}"])},mergeSource:function(l){var d=this.environment.isSimple,c=!this.forceBuffer,h=void 0,p=void 0,m=void 0,y=void 0;return this.source.each(function(f){f.appendToBuffer?(m?f.prepend("  + "):m=f,y=f):(m&&(p?m.prepend("buffer += "):h=!0,y.add(";"),m=y=void 0),p=!0,d||(c=!1))}),c?m?(m.prepend("return "),y.add(";")):p||this.source.push('return "";'):(l+=", buffer = "+(h?"":this.initializeBuffer()),m?(m.prepend("return buffer + "),y.add(";")):this.source.push("return buffer;")),l&&this.source.prepend("var "+l.substring(2)+(h?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(l){var d=this.aliasable("container.hooks.blockHelperMissing"),c=[this.contextName(0)];this.setupHelperArgs(l,0,c);var h=this.popStack();c.splice(1,0,h),this.push(this.source.functionCall(d,"call",c))},ambiguousBlockValue:function(){var l=this.aliasable("container.hooks.blockHelperMissing"),d=[this.contextName(0)];this.setupHelperArgs("",0,d,!0),this.flushInline();var c=this.topStack();d.splice(1,0,c),this.pushSource(["if (!",this.lastHelper,") { ",c," = ",this.source.functionCall(l,"call",d),"}"])},appendContent:function(l){this.pendingContent?l=this.pendingContent+l:this.pendingLocation=this.source.currentLocation,this.pendingContent=l},append:function(){if(this.isInline())this.replaceStack(function(d){return[" != null ? ",d,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var l=this.popStack();this.pushSource(["if (",l," != null) { ",this.appendToBuffer(l,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(l){this.lastContext=l},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(l,d,c,h){var p=0;h||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(l[p++])),this.resolvePath("context",l,p,d,c)},lookupBlockParam:function(l,d){this.useBlockParams=!0,this.push(["blockParams[",l[0],"][",l[1],"]"]),this.resolvePath("context",d,1)},lookupData:function(l,d,c){l?this.pushStackLiteral("container.data(data, "+l+")"):this.pushStackLiteral("data"),this.resolvePath("data",d,0,!0,c)},resolvePath:function(l,d,c,h,p){var m=this;if(this.options.strict||this.options.assumeObjects)this.push(function(f,b,S,v,T){var w=b.popStack(),I=S.length;for(f&&I--;v<I;v++)w=b.nameLookup(w,S[v],T);return f?[b.aliasable("container.strict"),"(",w,", ",b.quotedString(S[v]),", ",JSON.stringify(b.source.currentLocation)," )"]:w}(this.options.strict&&p,this,d,c,l));else for(var y=d.length;c<y;c++)this.replaceStack(function(f){var b=m.nameLookup(f,d[c],l);return h?[" && ",b]:[" != null ? ",b," : ",f]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(l,d){this.pushContext(),this.pushString(d),d!=="SubExpression"&&(typeof l=="string"?this.pushString(l):this.pushStackLiteral(l))},emptyHash:function(l){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(l?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var l=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(l.ids)),this.stringParams&&(this.push(this.objectLiteral(l.contexts)),this.push(this.objectLiteral(l.types))),this.push(this.objectLiteral(l.values))},pushString:function(l){this.pushStackLiteral(this.quotedString(l))},pushLiteral:function(l){this.pushStackLiteral(l)},pushProgram:function(l){l!=null?this.pushStackLiteral(this.programExpression(l)):this.pushStackLiteral(null)},registerDecorator:function(l,d){var c=this.nameLookup("decorators",d,"decorator"),h=this.setupHelperArgs(d,l);this.decorators.push(["fn = ",this.decorators.functionCall(c,"",["fn","props","container",h])," || fn;"])},invokeHelper:function(l,d,c){var h=this.popStack(),p=this.setupHelper(l,d),m=[];c&&m.push(p.name),m.push(h),this.options.strict||m.push(this.aliasable("container.hooks.helperMissing"));var y=["(",this.itemsSeparatedBy(m,"||"),")"],f=this.source.functionCall(y,"call",p.callParams);this.push(f)},itemsSeparatedBy:function(l,d){var c=[];c.push(l[0]);for(var h=1;h<l.length;h++)c.push(d,l[h]);return c},invokeKnownHelper:function(l,d){var c=this.setupHelper(l,d);this.push(this.source.functionCall(c.name,"call",c.callParams))},invokeAmbiguous:function(l,d){this.useRegister("helper");var c=this.popStack();this.emptyHash();var h=this.setupHelper(0,l,d),p=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",l,"helper")," || ",c,")"];this.options.strict||(p[0]="(helper = ",p.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",p,h.paramsInit?["),(",h.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",h.callParams)," : helper))"])},invokePartial:function(l,d,c){var h=[],p=this.setupParams(d,1,h);l&&(d=this.popStack(),delete p.name),c&&(p.indent=JSON.stringify(c)),p.helpers="helpers",p.partials="partials",p.decorators="container.decorators",l?h.unshift(d):h.unshift(this.nameLookup("partials",d,"partial")),this.options.compat&&(p.depths="depths"),p=this.objectLiteral(p),h.push(p),this.push(this.source.functionCall("container.invokePartial","",h))},assignToHash:function(l){var d=this.popStack(),c=void 0,h=void 0,p=void 0;this.trackIds&&(p=this.popStack()),this.stringParams&&(h=this.popStack(),c=this.popStack());var m=this.hash;c&&(m.contexts[l]=c),h&&(m.types[l]=h),p&&(m.ids[l]=p),m.values[l]=d},pushId:function(l,d,c){l==="BlockParam"?this.pushStackLiteral("blockParams["+d[0]+"].path["+d[1]+"]"+(c?" + "+JSON.stringify("."+c):"")):l==="PathExpression"?this.pushString(d):l==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:u,compileChildren:function(l,d){for(var c=l.children,h=void 0,p=void 0,m=0,y=c.length;m<y;m++){h=c[m],p=new this.compiler;var f=this.matchExistingProgram(h);if(f==null){this.context.programs.push("");var b=this.context.programs.length;h.index=b,h.name="program"+b,this.context.programs[b]=p.compile(h,d,this.context,!this.precompile),this.context.decorators[b]=p.decorators,this.context.environments[b]=h,this.useDepths=this.useDepths||p.useDepths,this.useBlockParams=this.useBlockParams||p.useBlockParams,h.useDepths=this.useDepths,h.useBlockParams=this.useBlockParams}else h.index=f.index,h.name="program"+f.index,this.useDepths=this.useDepths||f.useDepths,this.useBlockParams=this.useBlockParams||f.useBlockParams}},matchExistingProgram:function(l){for(var d=0,c=this.context.environments.length;d<c;d++){var h=this.context.environments[d];if(h&&h.equals(l))return h}},programExpression:function(l){var d=this.environment.children[l],c=[d.index,"data",d.blockParams];return(this.useBlockParams||this.useDepths)&&c.push("blockParams"),this.useDepths&&c.push("depths"),"container.program("+c.join(", ")+")"},useRegister:function(l){this.registers[l]||(this.registers[l]=!0,this.registers.list.push(l))},push:function(l){return l instanceof i||(l=this.source.wrap(l)),this.inlineStack.push(l),l},pushStackLiteral:function(l){this.push(new i(l))},pushSource:function(l){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),l&&this.source.push(l)},replaceStack:function(l){var d=["("],c=void 0,h=void 0,p=void 0;if(!this.isInline())throw new a.default("replaceStack on non-inline");var m=this.popStack(!0);if(m instanceof i)d=["(",c=[m.value]],p=!0;else{h=!0;var y=this.incrStack();d=["((",this.push(y)," = ",m,")"],c=this.topStack()}var f=l.call(this,c);p||this.popStack(),h&&this.stackSlot--,this.push(d.concat(f,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var l=this.inlineStack;this.inlineStack=[];for(var d=0,c=l.length;d<c;d++){var h=l[d];if(h instanceof i)this.compileStack.push(h);else{var p=this.incrStack();this.pushSource([p," = ",h,";"]),this.compileStack.push(p)}}},isInline:function(){return this.inlineStack.length},popStack:function(l){var d=this.isInline(),c=(d?this.inlineStack:this.compileStack).pop();if(!l&&c instanceof i)return c.value;if(!d){if(!this.stackSlot)throw new a.default("Invalid stack pop");this.stackSlot--}return c},topStack:function(){var l=this.isInline()?this.inlineStack:this.compileStack,d=l[l.length-1];return d instanceof i?d.value:d},contextName:function(l){return this.useDepths&&l?"depths["+l+"]":"depth"+l},quotedString:function(l){return this.source.quotedString(l)},objectLiteral:function(l){return this.source.objectLiteral(l)},aliasable:function(l){var d=this.aliases[l];return d?(d.referenceCount++,d):((d=this.aliases[l]=this.source.wrap(l)).aliasable=!0,d.referenceCount=1,d)},setupHelper:function(l,d,c){var h=[];return{params:h,paramsInit:this.setupHelperArgs(d,l,h,c),name:this.nameLookup("helpers",d,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(h)}},setupParams:function(l,d,c){var h={},p=[],m=[],y=[],f=!c,b=void 0;f&&(c=[]),h.name=this.quotedString(l),h.hash=this.popStack(),this.trackIds&&(h.hashIds=this.popStack()),this.stringParams&&(h.hashTypes=this.popStack(),h.hashContexts=this.popStack());var S=this.popStack(),v=this.popStack();(v||S)&&(h.fn=v||"container.noop",h.inverse=S||"container.noop");for(var T=d;T--;)b=this.popStack(),c[T]=b,this.trackIds&&(y[T]=this.popStack()),this.stringParams&&(m[T]=this.popStack(),p[T]=this.popStack());return f&&(h.args=this.source.generateArray(c)),this.trackIds&&(h.ids=this.source.generateArray(y)),this.stringParams&&(h.types=this.source.generateArray(m),h.contexts=this.source.generateArray(p)),this.options.data&&(h.data="data"),this.useBlockParams&&(h.blockParams="blockParams"),h},setupHelperArgs:function(l,d,c,h){var p=this.setupParams(l,d,c);return p.loc=JSON.stringify(this.source.currentLocation),p=this.objectLiteral(p),h?(this.useRegister("options"),c.push("options"),["options=",p]):c?(c.push(p),""):p}},function(){for(var l="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),d=u.RESERVED_WORDS={},c=0,h=l.length;c<h;c++)d[l[c]]=!0}(),u.isValidJavaScriptVariableName=function(l){return!u.RESERVED_WORDS[l]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(l)},t.default=u,e.exports=t.default})(gr,gr.exports);var ih=gr.exports;(function(e,t){function n(p){return p&&p.__esModule?p:{default:p}}t.__esModule=!0;var s=n(Wd),a=n(rl),r=Ut,o=Mt,i=n(ih),u=n(al),l=n(sl),d=s.default.create;function c(){var p=d();return p.compile=function(m,y){return o.compile(m,y,p)},p.precompile=function(m,y){return o.precompile(m,y,p)},p.AST=a.default,p.Compiler=o.Compiler,p.JavaScriptCompiler=i.default,p.Parser=r.parser,p.parse=r.parse,p.parseWithoutProcessing=r.parseWithoutProcessing,p}var h=c();h.create=c,l.default(h),h.Visitor=u.default,h.default=h,t.default=h,e.exports=t.default})(js,js.exports);const lh=vr(js.exports),uh=`
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
	<files_modified>
{{#each filesModified}}
		{{{this}}}
{{/each}}
	</files_modified>
{{/if}}
{{#if filesCreated}}
	<files_created>
{{#each filesCreated}}
		{{{this}}}
{{/each}}
	</files_created>
{{/if}}
{{#if filesDeleted}}
	<files_deleted>
{{#each filesDeleted}}
		{{{this}}}
{{/each}}
	</files_deleted>
{{/if}}
{{#if filesViewed}}
	<files_viewed>
{{#each filesViewed}}
		{{{this}}}
{{/each}}
	</files_viewed>
{{/if}}
{{#if terminalCommands}}
	<terminal_commands>
{{#each terminalCommands}}
		{{{this}}}
{{/each}}
	</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim(),ch=lh.compile(uh);function dh(e){for(const t of e.filesModified)e.filesViewed.delete(t)}function hh(e,t){try{const n=JSON.parse(e.input_json);switch(e.tool_name){case"str-replace-editor":n.path&&t.filesModified.add(n.path);break;case"save-file":n.path&&t.filesCreated.add(n.path);break;case"remove-files":if(n.file_paths&&Array.isArray(n.file_paths))for(const s of n.file_paths)t.filesDeleted.add(s);break;case"view":n.path&&t.filesViewed.add(n.path);break;case"launch-process":n.command&&t.terminalCommands.add(n.command)}}catch(n){console.warn("Failed to parse tool use input:",n)}}function kt(e,t,n,s="files"){if(e.size===0)return null;const a=Array.from(e).sort((u,l)=>u.localeCompare(l)),r=u=>Vs(u,n);if(a.length<=t)return a.map(r);const o=a.slice(0,t).map(r),i=a.length-t;return o.push(`... ${i} more ${s}`),o}function ph(e,t){let n=e.userMessage,s=e.agentFinalResponse;n.length>t.userMessageCharsLimit&&(n=Vs(n,t.userMessageCharsLimit)),s.length>t.agentResponseCharsLimit&&(s=Vs(s,t.agentResponseCharsLimit));const a=e.agentActionsSummary.filesModified.size>0||e.agentActionsSummary.filesCreated.size>0||e.agentActionsSummary.filesDeleted.size>0||e.agentActionsSummary.filesViewed.size>0||e.agentActionsSummary.terminalCommands.size>0,r={userMessage:n,agentResponse:s&&s.trim()!==""?s:null,hasActions:a,filesModified:kt(e.agentActionsSummary.filesModified,t.numFilesModifiedLimit,t.actionCharsLimit),filesCreated:kt(e.agentActionsSummary.filesCreated,t.numFilesCreatedLimit,t.actionCharsLimit),filesDeleted:kt(e.agentActionsSummary.filesDeleted,t.numFilesDeletedLimit,t.actionCharsLimit),filesViewed:kt(e.agentActionsSummary.filesViewed,t.numFilesViewedLimit,t.actionCharsLimit),terminalCommands:kt(e.agentActionsSummary.terminalCommands,t.numTerminalCommandsLimit,t.actionCharsLimit,"commands"),wasInterrupted:e.wasInterrupted,continues:e.continues};return ch(r)}function mh(e){var n,s;let t=e.request_message||"";return(n=e.structured_request_nodes)!=null&&n.some(a=>a.image_node||a.image_id_node)&&(t+=`
[User attached image]`),(s=e.structured_request_nodes)!=null&&s.some(a=>a.file_node||a.file_id_node)&&(t+=`
[User attached document]`),t}function Co(e){var t,n,s,a,r,o,i,u,l;try{if(!e)return console.log("historySummaryParams is empty. Using default params"),q;const d=JSON.parse(e),c={triggerOnHistorySizeChars:V(d.trigger_on_history_size_chars,q.triggerOnHistorySizeChars),historyTailSizeCharsToExclude:V(d.history_tail_size_chars_to_exclude,q.historyTailSizeCharsToExclude),triggerOnHistorySizeCharsWhenCacheExpiring:V(d.trigger_on_history_size_chars_when_cache_expiring,q.triggerOnHistorySizeCharsWhenCacheExpiring),prompt:V(d.prompt,q.prompt),cacheTTLMs:V(d.cache_ttl_ms,q.cacheTTLMs),bufferTimeBeforeCacheExpirationMs:V(d.buffer_time_before_cache_expiration_ms,q.bufferTimeBeforeCacheExpirationMs),summaryNodeRequestMessageTemplate:V(d.summary_node_request_message_template,q.summaryNodeRequestMessageTemplate),summaryNodeResponseMessage:V(d.summary_node_response_message,q.summaryNodeResponseMessage),abridgedHistoryParams:{totalCharsLimit:V((t=d.abridged_history_params)==null?void 0:t.total_chars_limit,q.abridgedHistoryParams.totalCharsLimit),userMessageCharsLimit:V((n=d.abridged_history_params)==null?void 0:n.user_message_chars_limit,q.abridgedHistoryParams.userMessageCharsLimit),agentResponseCharsLimit:V((s=d.abridged_history_params)==null?void 0:s.agent_response_chars_limit,q.abridgedHistoryParams.agentResponseCharsLimit),actionCharsLimit:V((a=d.abridged_history_params)==null?void 0:a.action_chars_limit,q.abridgedHistoryParams.actionCharsLimit),numFilesModifiedLimit:V((r=d.abridged_history_params)==null?void 0:r.num_files_modified_limit,q.abridgedHistoryParams.numFilesModifiedLimit),numFilesCreatedLimit:V((o=d.abridged_history_params)==null?void 0:o.num_files_created_limit,q.abridgedHistoryParams.numFilesCreatedLimit),numFilesDeletedLimit:V((i=d.abridged_history_params)==null?void 0:i.num_files_deleted_limit,q.abridgedHistoryParams.numFilesDeletedLimit),numFilesViewedLimit:V((u=d.abridged_history_params)==null?void 0:u.num_files_viewed_limit,q.abridgedHistoryParams.numFilesViewedLimit),numTerminalCommandsLimit:V((l=d.abridged_history_params)==null?void 0:l.num_terminal_commands_limit,q.abridgedHistoryParams.numTerminalCommandsLimit)}};c.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),c.summaryNodeRequestMessageTemplate=q.summaryNodeRequestMessageTemplate);const h={...c,prompt:c.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",h),c}catch(d){return console.error("Failed to parse history_summary_params:",d),q}}class gh{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,n){const s=new AbortController,a=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(a)},n);this._controllers.add(s),this._timeoutIds.add(a)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function ks(e){return e.reduce((t,n)=>t+dl(n),0)}function dl(e){let t=0;return e.request_nodes?t+=JSON.stringify(e.request_nodes).length:t+=(e.request_message||"").length,e.response_nodes?t+=JSON.stringify(e.response_nodes).length:t+=(e.response_text||"").length,t}class fh{constructor(t,n,s){g(this,"historySummaryVersion",3);g(this,"_callbacksManager",new gh);g(this,"_params");this._conversationModel=t,this._extensionClient=n,this._chatFlagModel=s,this._params=Co(s.historySummaryParams),s.subscribe(a=>{this._params=Co(a.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}generateAbridgedHistoryText(t){const n=new Set(t.map(o=>o.request_id)),s=function(o){const i=[];let u=null;for(const l of o){if(!H(l))continue;const d=l;if(Nh(d)||(u&&(u.agentFinalResponse.trim()===""&&(u.wasInterrupted=!0),i.push(u)),u={userMessage:mh(d),agentActionsSummary:{filesModified:new Set,filesCreated:new Set,filesDeleted:new Set,filesViewed:new Set,terminalCommands:new Set},agentFinalResponse:"",wasInterrupted:!1,continues:!1}),!u)continue;let c=!1;if(d.structured_output_nodes)for(const h of d.structured_output_nodes)h.type===R.TOOL_USE&&h.tool_use&&(c=!0,hh(h.tool_use,u.agentActionsSummary));!c&&d.response_text&&(u.agentFinalResponse=d.response_text)}u&&(u.agentFinalResponse.trim()===""&&(u.continues=!0),i.push(u));for(const l of i)dh(l.agentActionsSummary);return i}(this._conversationModel.chatHistory.filter(o=>o.request_id&&n.has(o.request_id)));let a=0;const r=[];for(let o=s.length-1;o>=0;o--){const i=ph(s[o],this._params.abridgedHistoryParams);if(a+i.length>this._params.abridgedHistoryParams.totalCharsLimit)break;r.push(i),a+=i.length}return r.reverse(),r.join(`
`)}clearStaleHistorySummaryNodes(t){return t.filter(n=>!mt(n)||n.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const n=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;n>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},n)}preprocessChatHistory(t){const n=t.findLastIndex(s=>mt(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(n>0&&(console.info(`Using history summary node found at index ${n} with requestId: ${t[n].request_id}`),t=t.slice(n)),t=t.filter(s=>!mt(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!mt(s)),t}async maybeAddHistorySummaryNode(t=!1,n){var k,U,D;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),a=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",a),a<=0)return!1;const{head:r,tail:o,headSizeChars:i,tailSizeChars:u}=function(N,X,ot,A){if(N.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const fe=[],B=[];let ye=0,Me=0,Jt=0;for(let Wn=N.length-1;Wn>=0;Wn--){const zn=N[Wn],Zt=dl(zn);ye+Zt<X||B.length<A?(B.push(zn),Jt+=Zt):(fe.push(zn),Me+=Zt),ye+=Zt}return ye<ot?(B.push(...fe),{head:[],tail:B.reverse(),headSizeChars:0,tailSizeChars:ye}):{head:fe.reverse(),tail:B.reverse(),headSizeChars:Me,tailSizeChars:Jt}}(s,this._params.historyTailSizeCharsToExclude,a,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",i," tailSizeChars: ",u),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const l=ks(s),d=ks(r),c=ks(o),h={totalHistoryCharCount:l,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:r.length,headLastRequestId:((k=r.at(-1))==null?void 0:k.request_id)??"",tailCharCount:c,tailExchangeCount:o.length,tailLastRequestId:((U=o.at(-1))==null?void 0:U.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let p=((D=r.at(-1))==null?void 0:D.response_nodes)??[],m=p.filter(N=>N.type===R.TOOL_USE);m.length>0&&(r.at(-1).response_nodes=p.filter(N=>N.type!==R.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const y=this.generateAbridgedHistoryText(r);console.info("Abridged history text size: %d characters.",y.length);const f=Date.now(),b=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),S=Date.now();if(console.info("Summary text size: %d characters.",b.responseText.length),h.summaryCharCount=b.responseText.length,h.summarizationDurationMs=S-f,h.isAborted=!!(n!=null&&n.aborted),this._extensionClient.reportAgentRequestEvent({eventName:An.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:b.requestId??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:h}}),n==null?void 0:n.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!b.requestId||b.responseText.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;let v=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",`<summary>
${b.responseText}
</summary>`);v.includes("{abridged_history}")&&(v=v.replace("{abridged_history}",`<abridged_history>
${y}
</abridged_history>`));const T=this._params.summaryNodeResponseMessage,w={chatItemType:pt.historySummary,summaryVersion:this.historySummaryVersion,request_id:b.requestId,request_message:v,response_text:T,structured_output_nodes:[{id:m.map(N=>N.id).reduce((N,X)=>Math.max(N,X),-1)+1,type:R.RAW_RESPONSE,content:T},...m],status:C.success,seen_state:te.seen,timestamp:new Date().toISOString()},I=this._conversationModel.chatHistory.findLastIndex(N=>N.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",I),this._conversationModel.insertChatItem(I,w),!0}}function hl(e){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}class dn{constructor(){this._disposables=[]}add(t){if(t===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(t),t}addAll(...t){t.forEach(n=>this.add(n))}adopt(t){this._disposables.push(...t._disposables),t._disposables.length=0}dispose(){for(const t of this._disposables)t.dispose();this._disposables.length=0}}class yh{constructor(t=new dn,n=new dn){this._disposables=new dn,this._priorityDisposables=new dn,this._disposables.adopt(t),this._priorityDisposables.adopt(n)}addDisposable(t,n=!1){return n?this._priorityDisposables.add(t):this._disposables.add(t)}addDisposables(...t){this._disposables.addAll(...t)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}var Ie=(e=>(e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink",e))(Ie||{});const Yr=class Yr{static setClientWorkspaces(t){this._instance===void 0?this._instance=t:hl().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}};Yr._instance=void 0;let yr=Yr;const $e=()=>yr.getClientWorkspaces(),_h=[".md",".mdc"],xo=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class Vr extends yh{constructor(){super(),this._logger=hl()}async loadRules({includeGuidelines:t=!1,query:n,maxResults:s,contextRules:a}={}){this._logger.debug(`Loading rules with includeGuidelines=${t}, query=${n}, maxResults=${s}`),this._logger.debug("Using file system approach to load rules");const r=await this.loadDirectory((void 0)(ct,At));let o;if(this._logger.debug(`Loaded ${r.length} rules from directory`),t){const i=await this.loadGuidelinesFiles();this._logger.debug(`Loaded ${i.length} guidelines rules`),o=[...i,...r]}else o=r;if(n&&n.trim()){const i=n.toLowerCase().trim();o=o.filter(u=>{const l=u.path.toLowerCase().includes(i),d=u.content.toLowerCase().includes(i);return l||d}),this._logger.debug(`Filtered to ${o.length} rules matching query: ${n}`)}return s&&s>0&&(o=o.slice(0,s),this._logger.debug(`Limited to ${o.length} rules`)),this._logger.debug(`Returning ${o.length} total rules`),a!==void 0&&(o=Vr.filterRulesByContext(o,a),this._logger.debug(`Filtered to ${o.length} rules based on context`)),o}static filterRulesByContext(t,n){return[...t.filter(s=>s.type!==ne.MANUAL),...t.filter(s=>s.type===ne.MANUAL&&n.some(a=>a.path===s.path))]}static calculateRulesAndGuidelinesCharacterCount(t){const{rules:n,workspaceGuidelinesContent:s,contextRules:a=[]}=t,r=n.filter(l=>l.type===ne.ALWAYS_ATTACHED).reduce((l,d)=>l+d.content.length+d.path.length,0),o=n.filter(l=>l.type===ne.AGENT_REQUESTED).reduce((l,d)=>{var c;return l+100+(((c=d.description)==null?void 0:c.length)??0)+d.path.length},0),i=r+n.filter(l=>l.type===ne.MANUAL).filter(l=>a.some(d=>d.path===l.path)).reduce((l,d)=>l+d.content.length+d.path.length,0)+o+s.length,u=t.rulesAndGuidelinesLimit&&i>t.rulesAndGuidelinesLimit;return{totalCharacterCount:i,isOverLimit:u,warningMessage:u&&t.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${i} chars)
        exceeds the limit of ${t.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}async loadGuidelinesFiles(){const t=[],n=$e();if(!n)return this._logger.warn("Client workspaces not initialized"),t;const s=await n.getWorkspaceRoot();if(!s)return t;const a=(void 0)(s,ls),r=await n.getPathInfo(a);if(r.exists&&r.type===Ie.File)try{const o=(await n.readFile(a)).contents;if(!o)return this._logger.warn(`Guidelines file is empty: ${a}`),t;const i=tt.parseRuleFile(o,ls);t.push({path:ls,content:i.content,type:i.type,description:i.description})}catch(o){this._logger.error(`Error loading guidelines file ${a}: ${String(o)}`)}return t}async loadDirectory(t){const n=[];try{const s=$e();if(!s)return this._logger.warn("Client workspaces not initialized"),n;const a=await s.getWorkspaceRoot();if(!a)return this._logger.warn("No workspace root found"),n;const r=(void 0)(a,t);this._logger.debug(`Looking for rules in: ${r}`);const o=await s.getPathInfo(r);return this._logger.debug(`Path info for ${r}: ${JSON.stringify(o)}`),o.exists&&o.type===Ie.Directory?(this._logger.debug(`Rules folder exists at ${r}`),await this.processRuleDirectory(s,r,n,""),this._logger.debug(`Loaded ${n.length} rules from ${r}`),n):(this._logger.debug(`Rules folder not found at ${r}`),n)}catch(s){return this._logger.error(`Error loading rules: ${String(s)}`),n}}async loadDirectoryFromPath(t){const n=[];try{const s=$e();if(!s)return this._logger.warn("Client workspaces not initialized"),n;let a;if(!(void 0)(t)){const o=await s.getWorkspaceRoot();if(!o)return this._logger.warn("No workspace root found"),n;a=(void 0)(o,t),this._logger.debug(`Loading rules from workspace-relative path: ${a}`)}const r=await s.getPathInfo(a);return r.exists&&r.type===Ie.Directory?(this._logger.debug(`Rules folder exists at ${a}`),await this.processRuleDirectory(s,a,n,""),this._logger.debug(`Loaded ${n.length} rules from ${a}`),n):(this._logger.debug(`Rules folder not found at ${a}`),n)}catch(s){return this._logger.error(`Error loading rules from path: ${String(s)}`),n}}async processRuleDirectory(t,n,s,a){const r=await t.listDirectory(n,1,!1);if(r.errorMessage)this._logger.error(`Error listing directory ${n}: ${r.errorMessage}`);else{this._logger.debug(`Processing directory: ${n}, found ${r.entries.length} entries`);for(const o of r.entries){const i=(void 0)(n,o),u=(void 0)(a,o),l=await t.getPathInfo(i);if(l.exists)if(l.type===Ie.Directory)this._logger.debug(`Processing subdirectory: ${o}`),await this.processRuleDirectory(t,i,s,u);else if(l.type===Ie.File&&_h.some(d=>o.endsWith(d))){this._logger.debug(`Processing rule file: ${o}`);try{const d=(await t.readFile(i)).contents||"",c=tt.parseRuleFile(d,o);s.push({path:u,content:c.content,type:c.type,description:c.description}),this._logger.debug(`Successfully loaded rule: ${u}`)}catch(d){this._logger.error(`Error loading rule file ${i}: ${String(d)}`)}}else l.type===Ie.File&&this._logger.debug(`Skipping non-markdown file: ${o}`)}}}async createRule(t,n=!1){const s=$e();if(!s)throw new Error("Client workspaces not initialized");const a=await s.getWorkspaceRoot();if(!a)throw new Error("No workspace root found");let r=(void 0)(a,ct,At);n&&(r=(void 0)(r,"imported"));const o=t.path.endsWith(".md")?t.path:`${t.path}.md`,i=(void 0)(r,o),u=await s.getQualifiedPathName(i);if(!u)throw new Error(`Unable to get qualified path for: ${i}`);if((await s.getPathInfo(i)).exists)throw new Error(`Rule file already exists: ${o}`);const l=tt.formatRuleFileForMarkdown(t);return await s.writeFile(u,l),{...t,path:o}}async deleteRule(t){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const n=$e();if(!n)throw new Error("Client workspaces not initialized");const s=await n.getWorkspaceRoot();if(!s)throw new Error("No workspace root found");let a;if((void 0)(t)||(a=(void 0)(s,ct,At,t)),(await n.getPathInfo(a)).exists){const r=await n.getQualifiedPathName(a);r&&(await n.deleteFile(r),this._logger.debug(`Deleted rule file: ${a}`)),this._logger.debug(`Deleted rule file: ${a}`)}}async updateRuleFile(t,n){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const s=$e();if(!s)throw new Error("Client workspaces not initialized");const a=await s.getWorkspaceRoot();if(!a)throw new Error("No workspace root found");let r;(void 0)(t)||(r=t.startsWith(ct)?(void 0)(a,t):(void 0)(a,ct,At,t));const o=await s.getQualifiedPathName(r);if(!o)throw new Error(`Unable to get qualified path for: ${r}`);await s.writeFile(o,n),this._logger.debug(`Updated rule file: ${r}`)}async importFile(t,n){const s=$e();if(!s)throw new Error("Client workspaces not initialized");let a,r;if(!(void 0)(t)){const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");a=(void 0)(i,t),r=t,this._logger.debug(`Importing file from workspace-relative path: ${a}`)}const o=await s.getPathInfo(a);if(!o.exists||o.type!==Ie.File)return this._logger.error(`File not found: ${a}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const i=(await s.readFile(a)).contents;if(!i)return this._logger.error(`File is empty: ${a}`),{successfulImports:0,duplicates:0,totalAttempted:1};const u=tt.parseRuleFile(i,r),l=(void 0)(r).name.replace(".","");return await this.createRule({path:l,content:u.content,type:u.type},n),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(i){return this._logger.error(`Error importing file ${t}: ${String(i)}`),{successfulImports:0,duplicates:String(i).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(t,n){try{const s=await this.loadDirectoryFromPath(t);if(s.length===0)return this._logger.debug(`No rules found in directory: ${t}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${s.length} existing rules from ${t}`);let a=0,r=0;const o=s.length;for(const i of s)try{const u=(void 0)(i.path).name,l=(void 0)(i.path),d=l==="."?u:(void 0)(l,u);await this.createRule({path:d,content:i.content,type:i.type},n),a++,this._logger.debug(`Successfully imported rule: ${i.path} -> ${d}`)}catch(u){this._logger.warn(`Failed to import rule ${i.path}: ${String(u)}`),String(u).includes("already exists")&&r++}return this._logger.info(`Imported ${a} rules from ${t}, ${r} duplicates skipped`),{successfulImports:a,duplicates:r,totalAttempted:o}}catch(s){return this._logger.error(`Error importing directory: ${String(s)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const t=$e();if(!t)return this._logger.warn("No workspace available for auto-import detection"),[];const n=await t.getWorkspaceRoot();if(!n)return this._logger.warn("No workspace root found for auto-import detection"),[];const s=[];for(const{directory:a,file:r,name:o}of xo){let i=!1,u=!1;if(a)try{const l=(void 0)(n,a),d=await t.getPathInfo(l);i=d.exists===!0&&d.type===Ie.Directory}catch(l){this._logger.debug(`Error checking directory ${a}: ${String(l)}`)}if(r)try{const l=(void 0)(n,r),d=await t.getPathInfo(l);u=d.exists===!0&&d.type===Ie.File}catch(l){this._logger.debug(`Error checking file ${r}: ${String(l)}`)}i&&u?s.push({label:o,description:`Import existing rules from ${a} and ${r}`,directory:a,file:r}):i?s.push({label:o,description:`Import existing rules from ${a}`,directory:a}):u&&s.push({label:o,description:`Import existing rules from ${r}`,file:r})}return s}async processAutoImportSelection(t){const n=xo.find(u=>u.name===t);if(!n)throw new Error(`Unknown auto-import option: ${t}`);const s=[];n.directory&&s.push(this.importDirectory(n.directory,!0)),n.file&&s.push(this.importFile(n.file,!0));const a=await Promise.all(s),r=a.reduce((u,l)=>u+l.successfulImports,0),o=a.reduce((u,l)=>u+l.duplicates,0),i=a.reduce((u,l)=>u+l.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${t}, imported: ${r}, duplicates: ${o}, total attempted: ${i}`),{importedRulesCount:r,duplicatesCount:o,totalAttempted:i,source:t}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}for(var Cs=256,bh=[];Cs--;)bh[Cs]=(Cs+256).toString(16).substring(1);const vh={THREAD_CREATION_ATTEMPTED:"thread_creation_attempted",SEND_ACTION_TRIGGERED:"send_action_triggered",CANCEL_ACTION_TRIGGERED:"cancel_action_triggered",RESEND_ACTION_TRIGGERED:"resend_action_triggered",AGENT_EXECUTION_MODE_TOGGLED:"agent_execution_mode_toggled",MESSAGE_SEND_RETRY_CLICKED:"message_send_retry_clicked",MESSAGE_SEND_TIMING:"message_send_timing"},xs=new Map,Sh=()=>{let e=Promise.resolve();const t=new Map,n=new Map,s=crypto.randomUUID(),a={end:r=>{const o=t.get(r);return console.debug("END LINK: ",r,s),o==null||o(),a},start:async(r,o)=>{const{promise:i,unlock:u,reject:l}=(c=>{let h=()=>{},p=()=>{},m=(y,f)=>()=>{f&&clearTimeout(f),y()};return{promise:new Promise((y,f)=>{let b,S=()=>{f("Chain was reset")};c&&c>0&&(b=setTimeout(S,c)),p=m(S,b),h=m(y,b)}),unlock:h,reject:p}})(o),d=e;return e=i.finally(()=>{t.delete(r),n.delete(r)}),t.set(r,()=>{u(),t.delete(r)}),n.set(r,()=>{l(),n.delete(r)}),await d,console.debug("START LINK: ",r,s),a},rejectAll:()=>{e=Promise.resolve();try{n.forEach(r=>{r(new Error("Chain was reset"))})}finally{t.clear(),n.clear()}},unlockAll:()=>{e=Promise.resolve();try{t.forEach(r=>{r()})}finally{t.clear(),n.clear()}}};return a},Tn="temp-fe";class le{constructor(t,n,s,a,r,o){g(this,"_state");g(this,"_subscribers",new Set);g(this,"_focusModel",new ju);g(this,"_onSendExchangeListeners",[]);g(this,"_onNewConversationListeners",[]);g(this,"_onHistoryDeleteListeners",[]);g(this,"_onBeforeChangeConversationListeners",[]);g(this,"_eventTracker");g(this,"_totalCharactersCacheThrottleMs",1e3);g(this,"_totalCharactersStore");g(this,"_chatHistorySummarizationModel");g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"setConversation",(t,n=!0,s=!0)=>{const a=t.id!==this._state.id;a&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,i])=>{if(i.requestId&&i.toolUseId){const{requestId:u,toolUseId:l}=Ya(o);return u===i.requestId&&l===i.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",us(i)),[o,i]}return[o,{...i,...Ya(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&a&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=le.isEmpty(t);if(a&&r){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(H)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),a&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});g(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});g(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});g(this,"setName",t=>{this.update({name:t})});g(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});g(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});g(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[us(t)]:t}})});g(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:M.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[us({requestId:t,toolUseId:n})]||{phase:M.new});g(this,"getLastToolUseId",()=>{var s,a;const t=this.lastExchange;if(!t)return;const n=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===R.TOOL_USE))??[]).at(-1);return n?(a=n.tool_use)==null?void 0:a.tool_use_id:void 0});g(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:M.unknown};const n=function(a=[]){let r;for(const o of a){if(o.type===R.TOOL_USE)return o;o.type===R.TOOL_USE_START&&(r=o)}return r}(t==null?void 0:t.structured_output_nodes);return n?this.getToolUseState(t.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:M.unknown}});g(this,"addExchange",(t,n)=>{const s=this._state.chatHistory;let a,r;a=n===void 0?[...s,t]:n===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,n),t,...s.slice(n)],H(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Ui.unset,feedbackNote:""}}:void 0),this.update({chatHistory:a,...r?{feedbackStates:r}:{},lastUrl:void 0})});g(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});g(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});g(this,"updateExchangeById",(t,n,s=!1)=>{var i;const a=this.exchangeWithRequestId(n);if(a===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(a.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(u=[]){const l=xc(u);return l&&l.type===R.TOOL_USE?u.filter(d=>d.type!==R.TOOL_USE_START):u}([...a.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==a.stop_reason&&a.stop_reason&&t.stop_reason===vl.REASON_UNSPECIFIED&&(t.stop_reason=a.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...a.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const r=(i=(t.structured_output_nodes||[]).find(u=>u.type===R.MAIN_TEXT_FINISHED))==null?void 0:i.content;r&&r!==t.response_text&&(t.response_text=r);let o=this._state.isShareable||As({...a,...t});return this.update({chatHistory:this.chatHistory.map(u=>u.request_id===n?{...u,...t}:u),isShareable:o}),!0});g(this,"clearMessagesFromHistory",t=>{const n=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:n})});g(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});g(this,"clearHistoryFrom",async(t,n=!0)=>{const s=this.historyFrom(t,n),a=s.map(o=>o.request_id).filter(o=>o!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:a,toolUseIds:r}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(i=>i(o))})});g(this,"clearMessageFromHistory",t=>{const n=this.chatHistory.find(a=>a.request_id===t),s=n?this._collectToolUseIdsFromMessages([n]):[];this.update({chatHistory:this.chatHistory.filter(a=>a.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});g(this,"_collectToolUseIdsFromMessages",t=>{var s;const n=[];for(const a of t)if(H(a)&&a.structured_output_nodes)for(const r of a.structured_output_nodes)r.type===R.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&n.push(r.tool_use.tool_use_id);return n});g(this,"historyTo",(t,n=!1)=>{const s=this.chatHistory.findIndex(a=>a.request_id===t);return s===-1?[]:this.chatHistory.slice(0,n?s+1:s)});g(this,"historyFrom",(t,n=!0)=>{const s=this.chatHistory.findIndex(a=>a.request_id===t);return s===-1?[]:this.chatHistory.slice(n?s:s+1)});g(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});g(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:C.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));g(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});g(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);g(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});g(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const n={seen_state:te.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...n}:s)})});g(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));g(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});g(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});g(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(a=>t.has(a.id)||a.recentFile||a.selection||a.sourceFolder),s=this._specialContextInputModel.recentItems.filter(a=>!(t.has(a.id)||a.recentFile||a.selection||a.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});g(this,"saveDraftExchange",(t,n)=>{var o,i,u;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),a=n!==((i=this.draftExchange)==null?void 0:i.rich_text_json_repr);if(!s&&!a)return;const r=(u=this.draftExchange)==null?void 0:u.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:r,status:C.draft}})});g(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});g(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!wt(this)){const a=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&a&&this.updateConversationTitle()}}).finally(()=>{var s;wt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:An.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});g(this,"cancelMessage",async()=>{var n;if(!this.canCancelMessage||!((n=this.lastExchange)!=null&&n.request_id))return;const t=this.lastExchange.request_id;this.updateExchangeById({status:C.cancelled},t),await this._extensionClient.cancelChatStream(t)});g(this,"sendInstructionExchange",async(t,n)=>{let s=`${Tn}-${crypto.randomUUID()}`;const a={status:C.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:te.unseen,timestamp:new Date().toISOString()};this.addExchange(a);for await(const r of this._extensionClient.sendInstructionMessage(a,n)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});g(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});g(this,"checkAndGenerateAgentTitle",()=>{var n;if(!(!wt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Hs(s))).length===1&&!((n=this.extraData)!=null&&n.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});g(this,"sendSummaryExchange",()=>{const t={status:C.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:pt.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});g(this,"generateCommitMessage",async()=>{let t=`${Tn}-${crypto.randomUUID()}`;const n={status:C.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:te.unseen,chatItemType:pt.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});g(this,"sendExchange",async(t,n=!1,s)=>{var c,h,p;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let a=`${Tn}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(le.isNew(this._state)){const m=crypto.randomUUID(),y=this._state.id;try{await this._extensionClient.migrateConversationId(y,m)}catch(f){console.error("Failed to migrate conversation checkpoints:",f)}this._state={...this._state,id:m},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(m),this._subscribers.forEach(f=>f(this))}t=Ro(t);let o={status:C.sent,request_id:a,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:r,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:te.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(m=>m(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},a,!1);const i=Date.now();let u=!1,l=0;for await(const m of this.sendUserMessage(a,o,n,s)){if(((c=this.exchangeWithRequestId(a))==null?void 0:c.status)!==C.sent||!this.updateExchangeById(m,a,!0))return;if(a=m.request_id||a,!u&&wt(this)){const y=Date.now();l=y-i,this._extensionClient.reportAgentRequestEvent({eventName:An.firstTokenReceived,conversationId:this.id,requestId:a,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:i,firstTokenReceivedTimestampMs:y,timeToFirstTokenMs:l}}}),u=!0}}const d=Date.now()-i;(p=this._eventTracker)==null||p.trackEvent(vh.MESSAGE_SEND_TIMING,{requestId:a,timeToFirstTokenMs:l,timeToLastTokenMs:d,responseLength:(h=t==null?void 0:t.response_text)==null?void 0:h.length,chatHistoryLength:this.chatHistory.length,modelId:o.model_id}),this._chatHistorySummarizationModel.maybeScheduleSummarization(d)});g(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:C.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Pi.chatUseSuggestedQuestion)});g(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});g(this,"recoverExchange",async t=>{var a;if(!t.request_id||t.status!==C.sent)return;let n=t.request_id;const s=(a=t.structured_output_nodes)==null?void 0:a.filter(r=>r.type===R.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},n);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,n,!0))return;n=r.request_id||n}});g(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{H(n)&&this._loadContextFromExchange(n)})});g(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});g(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{H(n)&&this._unloadContextFromExchange(n)})});g(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});g(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});g(this,"_jsonToStructuredRequest",t=>{const n=[],s=r=>{var i;const o=n.at(-1);if((o==null?void 0:o.type)===K.TEXT){const u=((i=o.text_node)==null?void 0:i.content)??"",l={...o,text_node:{content:u+r}};n[n.length-1]=l}else n.push({id:n.length,type:K.TEXT,text_node:{content:r}})},a=r=>{var o,i,u,l,d;if(r.type==="doc"||r.type==="paragraph")for(const c of r.content??[])a(c);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((o=r.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(i=r.attrs)==null?void 0:i.src);if(r.attrs.isLoading)return;const c=(u=r.attrs)==null?void 0:u.title,h=El(c);Tl(c)?n.push({id:n.length,type:K.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:h}}):n.push({id:n.length,type:K.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:c}})}else if(r.type==="mention"){const c=(l=r.attrs)==null?void 0:l.data;c&&$i(c)?n.push({id:n.length,type:K.TEXT,text_node:{content:Oc(this._chatFlagModel,c.personality.type)}}):c&&vc(c)?n.push({id:n.length,type:K.TEXT,text_node:{content:fd.getTaskOrchestratorPrompt(c.task)}}):s(`@\`${(c==null?void 0:c.name)??(c==null?void 0:c.id)}\``)}else if(r.type==="askMode"){const c=(d=r.attrs)==null?void 0:d.prompt;c&&n.push({id:n.length,type:K.TEXT,text_node:{content:c}})}};return a(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=s,this._saveConversation=a,this._rulesModel=r,this._state={...le.create(o!=null&&o.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new fh(this,t,n)}get conversationId(){return this._state.id}insertChatItem(t,n){const s=[...this._state.chatHistory];s.splice(t,0,n),this.update({chatHistory:s})}_createTotalCharactersStore(){return dc(()=>{let t=0;const n=this._state.chatHistory;return this.convertHistoryToExchanges(n).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}setEventTracker(t){this._eventTracker=t}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,a)=>s+a,0))||0)<=4?z.PROTOTYPER:z.DEFAULT}catch(n){return console.error("Error determining persona type:",n),z.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:z.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const n=t.chatHistory.find(H);return n&&n.request_message?le.toSentenceCase(n.request_message):wt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===Rc}static isEmpty(t){var a;const n=t.chatHistory.filter(r=>H(r)),s=t.chatHistory.filter(r=>Th(r));return n.length===0&&s.length===0&&!((a=t.draftExchange)!=null&&a.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,n){return n==="lastMessageTimestamp"?le.lastMessageTimestamp(t):n==="lastInteractedAt"?le.lastInteractedAt(t):le.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const n=(s=t.chatHistory.findLast(H))==null?void 0:s.timestamp;return n?new Date(n):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!le.isEmpty(t)||le.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let s=n;for(const a of this._onBeforeChangeConversationListeners){const r=a(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return le.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??z.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return le.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return le.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const n=a=>Array.isArray(a)?a.some(n):!!a&&(a.type==="file"||!(!a.content||!Array.isArray(a.content))&&a.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(H)??null}get lastExchange(){return this.chatHistory.findLast(H)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>H(t)&&t.status===C.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>As(t)||qt(t)||mt(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const n=[];for(const s of t)if(As(s))n.push(Ao(s));else if(mt(s))n.push(Ao(s));else if(qt(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const a=Eh(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[a],response_nodes:[]};n.push(r)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===C.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let s,a="";const r=await this._addIdeStateNode(Ro({...t,request_id:n,status:C.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(n,r,!0))o.response_text&&(a+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:a,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,s){return[]}_resolveUnresolvedToolUses(t,n,s){var d,c,h;if(t.length===0)return[t,n];const a=t[t.length-1],r=((d=a.response_nodes)==null?void 0:d.filter(p=>p.type===R.TOOL_USE))??[];if(r.length===0)return[t,n];const o=new Set;(c=n.structured_request_nodes)==null||c.forEach(p=>{var m;p.type===K.TOOL_RESULT&&((m=p.tool_result_node)!=null&&m.tool_use_id)&&o.add(p.tool_result_node.tool_use_id)});const i=r.filter(p=>{var y;const m=(y=p.tool_use)==null?void 0:y.tool_use_id;return m&&!o.has(m)});if(i.length===0)return[t,n];const u=i.map((p,m)=>{const y=p.tool_use.tool_use_id;return function(f,b,S,v){const T=Cc(b,f,v);let w;if(T!==void 0)w=T;else{let I;switch(b.phase){case M.runnable:I="Tool was cancelled before running.";break;case M.new:I="Cancelled by user.";break;case M.checkingSafety:I="Tool was cancelled during safety check.";break;case M.running:I="Tool was cancelled while running.";break;case M.cancelling:I="Tool cancellation was interrupted.";break;case M.cancelled:I="Cancelled by user.";break;case M.error:I="Tool execution failed.";break;case M.completed:I="Tool completed but result was unavailable.";break;case M.unknown:default:I="Cancelled by user.",b.phase!==M.unknown&&console.error(`Unexpected tool state phase: ${b.phase}`)}w={tool_use_id:f,content:I,is_error:!0}}return{id:S,type:K.TOOL_RESULT,tool_result_node:w}}(y,this.getToolUseState(a.request_id,y),_r(n.structured_request_nodes??[])+m+1,this._chatFlagModel.enableDebugFeatures)});if((h=n.structured_request_nodes)==null?void 0:h.some(p=>p.type===K.TOOL_RESULT))return[t,{...n,structured_request_nodes:[...n.structured_request_nodes??[],...u]}];{const p={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:u,structured_output_nodes:[],status:C.success,hidden:!0};return s||this.addExchangeBeforeLast(p),[t.concat(this.convertHistoryToExchanges([p])),n]}}async*sendUserMessage(t,n,s,a){const r=this._chatFlagModel.enableParallelTools,o=await(r?((i,u,l)=>{const d=xs.get(i)??Sh();return xs.has(i)||xs.set(i,d),d.start(u,l)})("sendMessage",t):Promise.resolve({end:()=>{}}));try{for await(const i of this._sendUserMessage(t,n,s,a))yield i}finally{o.end(t)}}async*_sendUserMessage(t,n,s,a){var h;const r=this._specialContextInputModel.chatActiveContext;let o;if(n.chatHistory!==void 0)o=n.chatHistory;else{let p=this.successfulMessages;if(n.chatItemType===pt.summaryTitle){const m=p.findIndex(y=>y.chatItemType!==pt.agentOnboarding&&Hs(y));m!==-1&&(p=p.slice(m))}o=this.convertHistoryToExchanges(p)}this._chatFlagModel.enableParallelTools&&([o,n]=this._resolveUnresolvedToolUses(o,n,s));let i=this.personaType;if(n.structured_request_nodes){const p=n.structured_request_nodes.find(m=>m.type===K.CHANGE_PERSONALITY);p&&p.change_personality_node&&(i=p.change_personality_node.personality_type)}let u=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const p=wl(this._rulesModel.getCachedRules());u=Vr.filterRulesByContext(p,r.ruleFiles||[])}const l={text:n.request_message,chatHistory:o,silent:s,modelId:n.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(h=r.externalSources)==null?void 0:h.map(p=>p.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:i,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:a,rules:u},d=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const p of c){let m=p;t=p.request_id||t;for(const y of d)m=y.handleChunk(m)??m;yield m}for(const p of d)yield*p.handleComplete();this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...n}:s)}),!0)}async _addIdeStateNode(t){let n,s=(t.structured_request_nodes??[]).filter(a=>a.type!==K.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(a){console.error("Failed to add IDE state to exchange:",a)}return n?(s=[...s,{id:_r(s)+1,type:K.IDE_STATE,ide_state_node:n}],{...t,structured_request_nodes:s}):t}}function Eh(e,t){const n=(qt(e),e.fromTimestamp),s=(qt(e),e.toTimestamp),a=qt(e)&&e.revertTarget!==void 0;return{id:t,type:K.CHECKPOINT_REF,checkpoint_ref_node:{request_id:e.request_id||"",from_timestamp:n,to_timestamp:s,source:a?bl.CHECKPOINT_REVERT:void 0}}}function Ao(e){const t=(e.structured_output_nodes??[]).filter(n=>n.type===R.RAW_RESPONSE||n.type===R.TOOL_USE||n.type===R.TOOL_USE_START).map(n=>n.type===R.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:R.TOOL_USE}:n);return{request_message:e.request_message,response_text:e.response_text??"",request_id:e.request_id||"",request_nodes:e.structured_request_nodes??[],response_nodes:t}}function _r(e){return e.length>0?Math.max(...e.map(t=>t.id)):0}function Ro(e){var t;if(e.request_message.length>0&&!((t=e.structured_request_nodes)!=null&&t.some(n=>n.type===K.TEXT))){let n=e.structured_request_nodes??[];return n=[...n,{id:_r(n)+1,type:K.TEXT,text_node:{content:e.request_message}}],{...e,structured_request_nodes:n}}return e}const zp="augment-welcome";var C=(e=>(e.draft="draft",e.sent="sent",e.failed="failed",e.success="success",e.cancelled="cancelled",e))(C||{}),Ze=(e=>(e.running="running",e.awaitingUserAction="awaiting-user-action",e.notRunning="not-running",e))(Ze||{}),te=(e=>(e.seen="seen",e.unseen="unseen",e))(te||{}),pt=(e=>(e.signInWelcome="sign-in-welcome",e.generateCommitMessage="generate-commit-message",e.summaryResponse="summary-response",e.summaryTitle="summary-title",e.educateFeatures="educate-features",e.agentOnboarding="agent-onboarding",e.agenticTurnDelimiter="agentic-turn-delimiter",e.agenticRevertDelimiter="agentic-revert-delimiter",e.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",e.exchange="exchange",e.exchangePointer="exchange-pointer",e.historySummary="history-summary",e))(pt||{});function Mo(e){return H(e)||wh(e)||Ih(e)}function H(e){return!!e&&(e.chatItemType===void 0||e.chatItemType==="agent-onboarding")}function As(e){return H(e)&&e.status==="success"}function Th(e){return!!e&&e.chatItemType==="exchange-pointer"}function Xp(e){return e.chatItemType==="sign-in-welcome"}function wh(e){return e.chatItemType==="generate-commit-message"}function Jp(e){return e.chatItemType==="summary-response"}function Zp(e){return e.chatItemType==="educate-features"}function Ih(e){return e.chatItemType==="agent-onboarding"}function Qp(e){return e.chatItemType==="agentic-turn-delimiter"}function qt(e){return e.chatItemType==="agentic-checkpoint-delimiter"}function mt(e){return e.chatItemType==="history-summary"}function em(e){return e.revertTarget!==void 0}function tm(e,t){const n=function(a){if(!a)return;const r=a.findLast(o=>Mo(o.turn));return r?r.turn:void 0}(e);if(!((n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"))return!1;const s=function(a){return a?a.findLast(o=>{var i;return!((i=o.turn.request_id)!=null&&i.startsWith(Tn))&&Mo(o.turn)}):void 0}(e);return(s==null?void 0:s.turn.request_id)===t.request_id}function nm(e){var t;return((t=e.structured_output_nodes)==null?void 0:t.some(n=>n.type===R.TOOL_USE))??!1}function Nh(e){var t;return((t=e.structured_request_nodes)==null?void 0:t.some(n=>n.type===K.TOOL_RESULT))??!1}function sm(e){return!(!e||typeof e!="object")&&(!("request_id"in e)||typeof e.request_id=="string")&&(!("seen_state"in e)||e.seen_state==="seen"||e.seen_state==="unseen")}function rm(e){return(e==null?void 0:e.status)==="success"||(e==null?void 0:e.status)==="failed"||(e==null?void 0:e.status)==="cancelled"}function am(e){if(!e)return;const t=e.filter(n=>H(n.turn)).map(n=>{return"response_text"in(s=n.turn)?s.response_text??"":"";var s}).filter(n=>n.length>0);return t.length>0?t.join(`
`):void 0}function om(e){let t=[];if(!e)return t;for(const n of e){const s=n.turn;H(s)&&s.structured_output_nodes&&(t=t.concat(s.structured_output_nodes))}return t}function im(e){var n;let t=new Set;for(const s of e)if(s.type===R.TOOL_USE&&((n=s.tool_use)!=null&&n.input_json))try{const a=JSON.parse(s.tool_use.input_json).path;t.add(a)}catch(a){console.error("Failed to parse tool input JSON:",a)}return t.size}function lm(e){var t;return e.type===R.AGENT_MEMORY||e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="remember"}function um(e){var t;return e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="view"}function cm(e){var t;return e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="str-replace-editor"}async function*kh(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class Ch{constructor(t,n,s,a=5,r=4e3,o){g(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=s,this.maxRetries=a,this.baseDelay=r,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,s=!1;try{for(;!this._isCancelled;){const a=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let r,o,i=!1,u=!0;for await(const l of a){if(l.status===C.failed){if(l.isRetriable!==!0||s)return yield l;i=!0,u=l.shouldBackoff??!0,r=l.display_error_message,o=l.request_id;break}s=!0,yield l}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${r}`),void(yield{request_id:o??this.requestId,seen_state:te.unseen,status:C.failed,display_error_message:r,isRetriable:!1});if(u){const l=this.baseDelay*2**n;n++;for await(const d of kh(l))yield{request_id:this.requestId,status:C.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(d/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:C.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(a){console.error("Unexpected error in chat stream:",a),yield{request_id:this.requestId,seen_state:te.unseen,status:C.failed,display_error_message:a instanceof Error?a.message:String(a)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:te.unseen,status:C.cancelled}}}var dt=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(dt||{});class xh{constructor(t){g(this,"getHydratedTask",async t=>{const n={type:dt.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});g(this,"createTask",async(t,n,s)=>{const a={type:dt.createTaskRequest,data:{name:t,description:n,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});g(this,"updateTask",async(t,n,s)=>{const a={type:dt.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(a,3e4)});g(this,"setCurrentRootTaskUuid",t=>{const n={type:dt.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});g(this,"updateHydratedTask",async(t,n)=>{const s={type:dt.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=t}}var Ne=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(Ne||{}),wn=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(wn||{});class dm{constructor(t,n,s){g(this,"_taskClient");g(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:E.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(s){return(await Bu(Vu,new Ri({sendMessage:r=>{s.postMessage(r)},onReceiveMessage:r=>{const o=i=>{r(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});g(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:E.reportWebviewClientMetric,data:{webviewName:Oi.chat,client_metric:t,value:1}})});g(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.send({type:E.trackAnalyticsEvent,data:{eventName:t,properties:n}})});g(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:j.reportAgentSessionEvent,data:t})});g(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:j.reportAgentRequestEvent,data:t})});g(this,"getSuggestions",async(t,n=!1)=>{const s={rootPath:"",relPath:t},a=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),o=this.findFolders(s,3),i=this.findExternalSources(t,n),u=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[l,d,c,h,p]=await Promise.all([Ct(a,[]),Ct(r,[]),Ct(o,[]),Ct(i,[]),Ct(u,[])]),m=(f,b)=>({...Sc(f),[b]:f}),y=[...l.map(f=>m(f,"file")),...c.map(f=>m(f,"folder")),...d.map(f=>m(f,"recentFile")),...h.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...p.map(f=>({...Ec(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(t);f.length>0&&y.push(...f)}return y});g(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return ja;const n=t.toLowerCase();return ja.filter(s=>{const a=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return a.includes(n)||r.includes(n)})});g(this,"sendAction",t=>{this._host.postMessage({type:E.mainPanelPerformAction,data:t})});g(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:E.showAugmentPanel})});g(this,"showNotification",t=>{this._host.postMessage({type:E.showNotification,data:t})});g(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:E.openConfirmationModal,data:t},1e9)).data.ok);g(this,"clearMetadataFor",t=>{this._host.postMessage({type:E.chatClearMetadata,data:t})});g(this,"resolvePath",async(t,n=void 0)=>{const s=await this._asyncMsgSender.send({type:E.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(s.data)return s.data});g(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:E.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);g(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:E.getDiagnosticsRequest},1e3)).data);g(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findFileRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:E.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);g(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:Ne.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);g(this,"openFile",t=>{this._host.postMessage({type:E.openFile,data:t})});g(this,"saveFile",t=>this._host.postMessage({type:E.saveFile,data:t}));g(this,"loadFile",t=>this._host.postMessage({type:E.loadFile,data:t}));g(this,"openMemoriesFile",()=>{this._host.postMessage({type:E.openMemoriesFile})});g(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:E.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});g(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:E.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});g(this,"createFile",(t,n)=>{this._host.postMessage({type:E.chatCreateFile,data:{code:t,relPath:n}})});g(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:E.openScratchFileRequest,data:{content:t,language:n}},1e4)});g(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:E.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});g(this,"smartPaste",t=>{this._host.postMessage({type:E.chatSmartPaste,data:t})});g(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));g(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));g(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});g(this,"createTask",async(t,n,s)=>this._taskClient.createTask(t,n,s));g(this,"updateTask",async(t,n,s)=>this._taskClient.updateTask(t,n,s));g(this,"saveChat",async(t,n,s)=>this._asyncMsgSender.send({type:E.saveChat,data:{conversationId:t,chatHistory:n,title:s}},5e3));g(this,"updateUserGuidelines",t=>{this._host.postMessage({type:E.updateUserGuidelines,data:t})});g(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:E.updateWorkspaceGuidelines,data:t})});g(this,"openSettingsPage",t=>{this._host.postMessage({type:E.openSettingsPage,data:t})});g(this,"_activeRetryStreams",new Map);g(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:E.chatUserCancel,data:{requestId:t}},1e4)});g(this,"sendUserRating",async(t,n,s,a="")=>{const r={requestId:t,rating:s,note:a,mode:n},o={type:E.chatRating,data:r};return(await this._asyncMsgSender.send(o,3e4)).data});g(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:E.usedChat})});g(this,"createProject",t=>{this._host.postMessage({type:E.mainPanelCreateProject,data:{name:t}})});g(this,"openProjectFolder",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"open-folder"})});g(this,"closeProjectFolder",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"close-folder"})});g(this,"cloneRepository",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"clone-repository"})});g(this,"grantSyncPermission",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"grant-sync-permission"})});g(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:E.startRemoteMCPAuth,data:{name:t}})});g(this,"callTool",async(t,n,s,a,r,o)=>{const i={type:E.callTool,data:{chatRequestId:t,toolUseId:n,name:s,input:a,chatHistory:r,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});g(this,"cancelToolRun",async(t,n)=>{const s={type:E.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(s,0)});g(this,"checkSafe",async t=>{const n={type:vn.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});g(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:vn.closeAllToolProcesses},0)});g(this,"getToolIdentifier",async t=>{const n={type:vn.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});g(this,"getChatMode",async()=>{const t={type:j.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});g(this,"setChatMode",t=>{this._asyncMsgSender.send({type:E.chatModeChanged,data:{mode:t}})});g(this,"getAgentEditList",async(t,n)=>{const s={type:j.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});g(this,"hasChangesSince",async t=>{const n={type:j.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(s=>{var a,r;return((a=s.changesSummary)==null?void 0:a.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});g(this,"getToolCallCheckpoint",async t=>{const n={type:E.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});g(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:j.setCurrentConversation,data:{conversationId:t}})});g(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:j.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});g(this,"showAgentReview",(t,n,s,a=!0,r)=>{this._asyncMsgSender.sendToSidecar({type:j.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:s,retainFocus:a,useNativeDiffIfAvailable:r}})});g(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.chatAgentEditAcceptAll}),!0));g(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:j.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));g(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:E.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);g(this,"getAgentEditChangesByRequestId",async t=>{const n={type:j.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});g(this,"getAgentEditContentsByRequestId",async t=>{const n={type:j.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});g(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:E.triggerInitialOrientation})});g(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:E.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});g(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:E.toggleCollapseUnchangedRegions})});g(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:E.checkAgentAutoModeApproval},5e3)).data);g(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:E.setAgentAutoModeApproved,data:t},5e3)});g(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.checkHasEverUsedAgent},5e3)).data);g(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:j.setHasEverUsedAgent,data:t},5e3)});g(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.checkHasEverUsedRemoteAgent},5e3)).data);g(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:j.setHasEverUsedRemoteAgent,data:t},5e3)});g(this,"getChatRequestIdeState",async()=>{const t={type:E.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});g(this,"reportError",t=>{this._host.postMessage({type:E.reportError,data:t})});g(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});g(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=s,this._taskClient=new xh(n)}async*generateCommitMessage(){const t={type:E.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*Rs(n,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(t,n){const s={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},a={type:E.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(o){let i;try{for await(const u of o)i=u.data.requestId,yield{request_id:i,response_text:u.data.text,seen_state:te.unseen,status:C.sent};yield{request_id:i,seen_state:te.unseen,status:C.success}}catch(u){console.error("Error in chat instruction model reply stream:",u),yield{request_id:i,seen_state:te.unseen,status:C.failed}}}(r)}async openGuidelines(t){this._host.postMessage({type:E.openGuidelines,data:t})}async*getExistingChatStream(t,n,s){const a=s==null?void 0:s.flags.enablePreferenceCollection,r=a?1e9:6e4,o=a?1e9:3e5,i={type:E.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},u=this._asyncMsgSender.stream(i,r,o);yield*Rs(u,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(t,n){const s=n==null?void 0:n.flags.enablePreferenceCollection,a=s?1e9:1e5,r=s?1e9:3e5,o={type:E.chatUserMessage,data:t},i=this._asyncMsgSender.stream(o,a,r);yield*Rs(i,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:E.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const s=Va(await os(t)),a=n??`${await Ga(await is(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:E.chatSaveImageRequest,data:{filename:a,data:s}},1e4)).data}async saveAttachment(t,n){const s=Va(await os(t)),a=n??`${await Ga(await is(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:E.chatSaveAttachmentRequest,data:{filename:a,data:s}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:E.chatLoadImageRequest,data:t},1e4),s=n.data?await is(n.data):void 0;if(!s)return;let a="application/octet-stream";const r=t.split(".").at(-1);r==="png"?a="image/png":r!=="jpg"&&r!=="jpeg"||(a="image/jpeg");const o=new File([s],t,{type:a});return await os(o)}async deleteImage(t){await this._asyncMsgSender.send({type:E.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,s){const a=new Ch(t,n,(r,o)=>this.startChatStream(r,o),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(t,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:E.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const s={type:Sn.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const s={type:Sn.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(t){const n={type:Sn.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:wn.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const s={type:wn.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(t){const n={type:wn.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Rs(e,t=()=>{},n){let s;try{for await(const a of e){if(s=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),yield{request_id:s,seen_state:te.unseen,status:C.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const r={request_id:s,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:Ah(a.data.nodes),seen_state:te.unseen,status:C.sent,lastChunkId:a.data.chunkId};a.data.stop_reason!=null&&(r.stop_reason=a.data.stop_reason),yield r}yield{request_id:s,seen_state:te.unseen,status:C.success}}catch(a){let r,o;if(t({originalRequestId:s||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof gl&&n)switch(a.name){case"MessageTimeout":r=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":r=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:s,seen_state:te.unseen,status:C.failed,isRetriable:r,shouldBackoff:o}}}async function Ct(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function Ah(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==R.TOOL_USE||!t&&(t=!0,!0))}const hm=15,pm=1e3,Rh=25e4,mm=2e4;class gm{constructor(t){g(this,"_enableEditableHistory",!1);g(this,"_enablePreferenceCollection",!1);g(this,"_enableRetrievalDataCollection",!1);g(this,"_enableDebugFeatures",!1);g(this,"_enableConversationDebugUtils",!1);g(this,"_enableRichTextHistory",!1);g(this,"_enableAgentSwarmMode",!1);g(this,"_modelDisplayNameToId",{});g(this,"_fullFeatured",!0);g(this,"_enableExternalSourcesInChat",!1);g(this,"_smallSyncThreshold",15);g(this,"_bigSyncThreshold",1e3);g(this,"_enableSmartPaste",!1);g(this,"_enableDirectApply",!1);g(this,"_summaryTitles",!1);g(this,"_suggestedEditsAvailable",!1);g(this,"_enableShareService",!1);g(this,"_maxTrackableFileCount",Rh);g(this,"_enableDesignSystemRichTextEditor",!1);g(this,"_enableSources",!1);g(this,"_enableChatMermaidDiagrams",!1);g(this,"_smartPastePrecomputeMode",fl.visibleHover);g(this,"_useNewThreadsMenu",!1);g(this,"_enableChatMermaidDiagramsMinVersion",!1);g(this,"_enablePromptEnhancer",!1);g(this,"_idleNewSessionNotificationTimeoutMs");g(this,"_idleNewSessionMessageTimeoutMs");g(this,"_enableChatMultimodal",!1);g(this,"_enableAgentMode",!1);g(this,"_enableAgentAutoMode",!1);g(this,"_enableRichCheckpointInfo",!1);g(this,"_agentMemoriesFilePathName");g(this,"_conversationHistorySizeThresholdBytes",44040192);g(this,"_userTier","unknown");g(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});g(this,"_truncateChatHistory",!1);g(this,"_enableBackgroundAgents",!1);g(this,"_enableNewThreadsList",!1);g(this,"_customPersonalityPrompts",{});g(this,"_enablePersonalities",!1);g(this,"_enableRules",!1);g(this,"_memoryClassificationOnFirstToken",!1);g(this,"_enableGenerateCommitMessage",!1);g(this,"_modelRegistry",{});g(this,"_enableModelRegistry",!1);g(this,"_enableTaskList",!1);g(this,"_clientAnnouncement","");g(this,"_useHistorySummary",!1);g(this,"_historySummaryParams","");g(this,"_enableExchangeStorage",!1);g(this,"_enableToolUseStateStorage",!1);g(this,"_retryChatStreamTimeouts",!1);g(this,"_enableCommitIndexing",!1);g(this,"_enableMemoryRetrieval",!1);g(this,"_enableAgentTabs",!1);g(this,"_isVscodeVersionOutdated",!1);g(this,"_vscodeMinVersion","");g(this,"_enableGroupedTools",!1);g(this,"_remoteAgentsResumeHintAvailableTtlDays",0);g(this,"_enableParallelTools",!1);g(this,"_enableAgentGitTracker",!1);g(this,"_memoriesParams",{});g(this,"_subscribers",new Set);g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});g(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));g(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const s=t.toLowerCase(),a=n.toLowerCase();return s==="default"&&a!=="default"?-1:a==="default"&&s!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}var Mh=Al,Oh=/\s/,Ph=function(e){for(var t=e.length;t--&&Oh.test(e.charAt(t)););return t},Dh=/^\s+/,Lh=Ml,Fh=Rl,Uh=function(e){return e&&e.slice(0,Ph(e)+1).replace(Dh,"")},Oo=Sr,$h=function(e){return typeof e=="symbol"||Fh(e)&&Lh(e)=="[object Symbol]"},qh=/^[-+]0x[0-9a-f]+$/i,Hh=/^0b[01]+$/i,Bh=/^0o[0-7]+$/i,Gh=parseInt,Vh=Sr,Ms=function(){return Mh.Date.now()},Po=function(e){if(typeof e=="number")return e;if($h(e))return NaN;if(Oo(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Oo(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Uh(e);var n=Hh.test(e);return n||Bh.test(e)?Gh(e.slice(2),n?2:8):qh.test(e)?NaN:+e},jh=Math.max,Yh=Math.min,Kh=function(e,t,n){var s,a,r,o,i,u,l=0,d=!1,c=!1,h=!0;if(typeof e!="function")throw new TypeError("Expected a function");function p(S){var v=s,T=a;return s=a=void 0,l=S,o=e.apply(T,v)}function m(S){var v=S-u;return u===void 0||v>=t||v<0||c&&S-l>=r}function y(){var S=Ms();if(m(S))return f(S);i=setTimeout(y,function(v){var T=t-(v-u);return c?Yh(T,r-(v-l)):T}(S))}function f(S){return i=void 0,h&&s?p(S):(s=a=void 0,o)}function b(){var S=Ms(),v=m(S);if(s=arguments,a=this,u=S,v){if(i===void 0)return function(T){return l=T,i=setTimeout(y,t),d?p(T):o}(u);if(c)return clearTimeout(i),i=setTimeout(y,t),p(u)}return i===void 0&&(i=setTimeout(y,t)),o}return t=Po(t)||0,Vh(n)&&(d=!!n.leading,r=(c="maxWait"in n)?jh(Po(n.maxWait)||0,t):r,h="trailing"in n?!!n.trailing:h),b.cancel=function(){i!==void 0&&clearTimeout(i),l=0,s=u=a=i=void 0},b.flush=function(){return i===void 0?o:f(Ms())},b},Wh=Sr;const zh=vr(function(e,t,n){var s=!0,a=!0;if(typeof e!="function")throw new TypeError("Expected a function");return Wh(n)&&(s="leading"in n?!!n.leading:s,a="trailing"in n?!!n.trailing:a),Kh(e,t,{leading:s,maxWait:t,trailing:a})});class Xh{constructor(t){g(this,"SIDECAR_TIMEOUT_MS",5e3);g(this,"getRulesList",async(t=!0)=>{const n={type:Ne.getRulesListRequest,data:{includeGuidelines:t}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});g(this,"createRule",async t=>{const n={type:Ne.createRule,data:{ruleName:t.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});g(this,"getWorkspaceRoot",async()=>{const t={type:Ne.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});g(this,"updateRuleFile",async(t,n)=>{const s={type:Ne.updateRuleFile,data:{path:t,content:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});g(this,"deleteRule",async(t,n=!0)=>{const s={type:Ne.deleteRule,data:{path:t,confirmed:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});g(this,"processSelectedPaths",async(t,n=!0)=>{const s={type:Ne.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:n}},a=await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:a.data.importedRulesCount,directoryOrFile:a.data.directoryOrFile,errors:a.data.errors}});g(this,"getAutoImportOptions",async()=>{const t={type:Ne.autoImportRules};return await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)});g(this,"processAutoImportSelection",async t=>{const n={type:Ne.autoImportRulesSelectionRequest,data:{selectedLabel:t}},s=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:s.data.importedRulesCount,duplicatesCount:s.data.duplicatesCount,totalAttempted:s.data.totalAttempted,source:s.data.source}});this._asyncMsgSender=t}}class fm{constructor(t,n=!0){g(this,"_rulesFiles",Bt([]));g(this,"_loading",Bt(!0));g(this,"_extensionClientRules");g(this,"_requestRulesThrottled",zh(async()=>{this._loading.set(!0);try{const t=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(t)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=t,this.includeGuidelines=n,this._extensionClientRules=new Xh(this._msgBroker),this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==E.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(t){try{const n=await this._extensionClientRules.createRule(t);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const n=tt.formatRuleFileForMarkdown(t);try{await this._extensionClientRules.updateRuleFile(t.path,n)}catch(s){console.error("Failed to update rule file:",s)}await this.requestRules()}async deleteRule(t){try{await this._extensionClientRules.deleteRule(t,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(t){try{const n=await this._extensionClientRules.processSelectedPaths(t,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(t){try{const n=await this._extensionClientRules.processAutoImportSelection(t.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var Jh=Uo('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function Zh(e){var t=Jh();O(e,t)}const Ht=class Ht{constructor(t=void 0){g(this,"_lastFocusAnchorElement");g(this,"_focusedIndexStore",Bt(void 0));g(this,"focusedIndex",this._focusedIndexStore);g(this,"_rootElement");g(this,"_triggerElement");g(this,"_getItems",()=>{var s;const t=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${Ht.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});g(this,"_recomputeFocusAnchor",t=>{var r;const n=(r=this._parentContext)==null?void 0:r._getItems(),s=n==null?void 0:n.indexOf(t);if(s===void 0||n===void 0)return;const a=Math.max(s-1,0);this._lastFocusAnchorElement=n[a]});g(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=a=>{t.contains(a.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._removeFromTrapStack(),this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});g(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));g(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!t.shiftKey&&s===this._getItems().length-1||t.shiftKey&&s===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});g(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Ol)});g(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(s=>s===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});g(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=hn(t,n.length);this._focusedIndexStore.set(s)});g(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=hn(t,n.length),a=n[s];a==null||a.focus(),this._focusedIndexStore.set(s)});g(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});g(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=hn(t.findIndex(s=>s===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});g(this,"focusPrev",()=>{var s;const t=this._getItems();if(t.length===0)return;const n=hn(t.findIndex(a=>a===document.activeElement)-1,t.length);(s=t[n])==null||s.focus(),this._focusedIndexStore.set(n)});g(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await Ps())});g(this,"_addToTrapStack",()=>{this._rootElement&&Kr.add(this._rootElement)});g(this,"_removeFromTrapStack",()=>{this._rootElement&&Kr.remove(this._rootElement)});g(this,"handleOpenChange",t=>{t?this._addToTrapStack():this._removeFromTrapStack()});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};g(Ht,"CONTEXT_KEY","augment-dropdown-menu-focus"),g(Ht,"ITEM_CLASS","js-dropdown-menu__focusable-item");let he=Ht;function hn(e,t){return(e%t+t)%t}const gt="augment-dropdown-menu-content";var Qh=re("<div><!></div>"),ep=re('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Do(e,t){_e(t,!1);const[n,s]=He(),a=()=>ke(p,"$sizeState",n),r=Ae();let o=x(t,"size",8,2),i=x(t,"onEscapeKeyDown",8,()=>{}),u=x(t,"onClickOutside",8,()=>{}),l=x(t,"onRequestClose",8,()=>{}),d=x(t,"side",8,"top"),c=x(t,"align",8,"center");const h={size:Bt(o())},p=h.size;$o(gt,h);const m=oe(he.CONTEXT_KEY),y=oe(pn.CONTEXT_KEY);xe(()=>Ye(o()),()=>{p.set(o())}),xe(()=>{},()=>{Nl(Re(r,y.state),"$openState",n)}),ft(),be(),we("keydown",Il,function(f){if(ke(F(r),"$openState",n).open&&f.key==="Tab"&&!f.shiftKey){if(m.getCurrentFocusedIdx()!==void 0)return;f.preventDefault(),m==null||m.focusIdx(0)}}),Pl(e,{onEscapeKeyDown:i(),onClickOutside:u(),onRequestClose:l(),get side(){return d()},get align(){return c()},$$events:{keydown(f){De.call(this,t,f)}},children:(f,b)=>{var S=ep(),v=ce(S);Dl(v,{get size(){return a()},insetContent:!0,includeBackground:!1,children:(T,w)=>{var I=Qh(),k=ce(I);se(k,t,"default",{},null),Os(I,U=>{var D;return(D=m.registerRoot)==null?void 0:D.call(m,U)}),yt(()=>_t(I,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${a()}`,"svelte-o54ind")),O(T,I)},$$slots:{default:!0}}),O(f,S)},$$slots:{default:!0}}),ve(),s()}var tp=re('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),np=re('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),sp=re("<!> <!> <!>",1);function br(e,t){const n=qo(t),s=W(t,["children","$$slots","$$events","$$legacy"]),a=W(s,["highlight","disabled","color","onSelect"]);_e(t,!1);const[r,o]=He(),i=()=>ke(b,"$sizeState",r),u=Ae(),l=Ae(),d=Ae();let c=x(t,"highlight",24,()=>{}),h=x(t,"disabled",24,()=>{}),p=x(t,"color",24,()=>{}),m=x(t,"onSelect",8,()=>{});const y=oe(gt),f=oe(he.CONTEXT_KEY),b=y.size;function S(k){var N;if(h())return;const U=(N=f.rootElement)==null?void 0:N.querySelectorAll(`.${he.ITEM_CLASS}`);if(!U)return;const D=Array.from(U).findIndex(X=>X===k);D!==-1&&f.setFocusedIdx(D)}xe(()=>(F(u),F(l),Ye(a)),()=>{Re(u,a.class),Re(l,Bo(a,["class"]))}),xe(()=>(Ye(h()),Ye(c()),F(u)),()=>{Re(d,[h()?"":he.ITEM_CLASS,"c-dropdown-menu-augment__item",c()?"c-dropdown-menu-augment__item--highlighted":"",F(u)].join(" "))}),ft(),be();const v=In(()=>p()??"neutral"),T=In(()=>!p());var w=Wr(()=>zr("dropdown-menu-item","highlighted",c())),I=Wr(()=>zr("dropdown-menu-item","disabled",h()));yl(e,et({get class(){return F(d)},get size(){return i()},variant:"ghost",get color(){return F(v)},get highContrast(){return F(T)},alignment:"left",get disabled(){return h()}},()=>F(w),()=>F(I),()=>F(l),{$$events:{click:k=>{k.currentTarget instanceof HTMLElement&&S(k.currentTarget),m()(k)},mouseover:k=>{k.currentTarget instanceof HTMLElement&&S(k.currentTarget)},mousedown:k=>{k.preventDefault(),k.stopPropagation()}},children:(k,U)=>{var D=sp(),N=Ce(D),X=B=>{var ye=tp(),Me=ce(ye);se(Me,t,"iconLeft",{},null),O(B,ye)};Ot(N,B=>{nt(()=>n.iconLeft)&&B(X)});var ot=Pt(N,2);Ho(ot,{get size(){return i()},children:(B,ye)=>{var Me=Be(),Jt=Ce(Me);se(Jt,t,"default",{},null),O(B,Me)},$$slots:{default:!0}});var A=Pt(ot,2),fe=B=>{var ye=np(),Me=ce(ye);se(Me,t,"iconRight",{},null),O(B,ye)};Ot(A,B=>{nt(()=>n.iconRight)&&B(fe)}),O(k,D)},$$slots:{default:!0}})),ve(),o()}var rp=Uo("<svg><!></svg>");function ap(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]);var s=rp();Go(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 320 512",...n}));var a=ce(s);_l(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',!0),O(e,s)}function Lo(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);br(e,et({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>s,{children:(a,r)=>{var o=Be(),i=Ce(o);se(i,t,"default",{},null),O(a,o)},$$slots:{default:!0,iconRight:(a,r)=>{ap(a,{slot:"iconRight"})}}}))}var op=re("<div><!></div>");function Fo(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);_e(t,!1);let a=x(t,"defaultOpen",24,()=>{}),r=x(t,"open",24,()=>{}),o=x(t,"onOpenChange",24,()=>{}),i=x(t,"delayDurationMs",24,()=>{}),u=x(t,"nested",24,()=>{}),l=x(t,"onHoverStart",8,()=>{}),d=x(t,"onHoverEnd",8,()=>{}),c=x(t,"triggerOn",24,()=>[mn.Click]),h=Ae();const p=()=>{var w;return(w=F(h))==null?void 0:w.requestOpen()},m=()=>{var w;return(w=F(h))==null?void 0:w.requestClose()},y=w=>v.focusIdx(w),f=w=>v.setFocusedIdx(w),b=()=>v.getCurrentFocusedIdx(),S=oe(he.CONTEXT_KEY),v=new he(S);$o(he.CONTEXT_KEY,v);const T=v.focusedIndex;return be(),Vo(Ll(e,et({get defaultOpen(){return a()},get open(){return r()},onOpenChange:function(w){var I;v.handleOpenChange(w),(I=o())==null||I(w)},get delayDurationMs(){return i()},onHoverStart:l(),onHoverEnd:d(),get triggerOn(){return c()},get nested(){return u()}},()=>s,{children:(w,I)=>{var k=Be(),U=Ce(k);se(U,t,"default",{},null),O(w,k)},$$slots:{default:!0},$$legacy:!0})),w=>Re(h,w),()=>F(h)),it(t,"requestOpen",p),it(t,"requestClose",m),it(t,"focusIdx",y),it(t,"setFocusedIdx",f),it(t,"getCurrentFocusedIdx",b),it(t,"focusedIndex",T),ve({requestOpen:p,requestClose:m,focusIdx:y,setFocusedIdx:f,getCurrentFocusedIdx:b,focusedIndex:T})}var ip=re("<div></div>");function lp(e,t){let n=x(t,"size",8,1),s=x(t,"orientation",8,"horizontal"),a=x(t,"useCurrentColor",8,!1),r=x(t,"class",8,"");var o=ip();let i;yt(u=>i=_t(o,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${s()} ${r()}`,"svelte-o0csoy",i,u),[()=>({"c-separator--current-color":a()})],In),O(e,o)}var up=re("<div><!></div>"),cp=re('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),dp=re('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),hp=re('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),pp=re("<!> <input/> <!>",1),mp=re("<div><!> <!></div>");function gp(e,t){const n=qo(t),s=W(t,["children","$$slots","$$events","$$legacy"]),a=W(s,["variant","size","color","textInput","value","id"]);_e(t,!1);const r=Ae(),o=Ae(),i=Ae(),u=Cl();let l=x(t,"variant",8,"surface"),d=x(t,"size",8,2),c=x(t,"color",24,()=>{}),h=x(t,"textInput",28,()=>{}),p=x(t,"value",12,""),m=x(t,"id",24,()=>{});const y=`text-field-${Math.random().toString(36).substring(2,11)}`;function f(w){u("change",w)}xe(()=>Ye(m()),()=>{Re(r,m()||y)}),xe(()=>(F(o),F(i),Ye(a)),()=>{Re(o,a.class),Re(i,Bo(a,["class"]))}),ft(),be();var b=mp();_t(b,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var S=ce(b),v=w=>{var I=cp(),k=ce(I);se(k,t,"label",{},null),yt(()=>xl(I,"for",F(r))),O(w,I)};Ot(S,w=>{nt(()=>n.label)&&w(v)});var T=Pt(S,2);Fl(T,{get variant(){return l()},get size(){return d()},get color(){return c()},children:(w,I)=>{var k=pp(),U=Ce(k),D=A=>{var fe=dp(),B=ce(fe);se(B,t,"iconLeft",{},null),O(A,fe)};Ot(U,A=>{nt(()=>n.iconLeft)&&A(D)});var N=Pt(U,2);Go(N,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${F(o)}`,id:F(r),...F(i)}),void 0,"svelte-vuqlvc"),Vo(N,A=>h(A),()=>h());var X=Pt(N,2),ot=A=>{var fe=hp(),B=ce(fe);se(B,t,"iconRight",{},null),O(A,fe)};Ot(X,A=>{nt(()=>n.iconRight)&&A(ot)}),Ul(N,p),we("change",N,f),we("click",N,function(A){De.call(this,t,A)}),we("keydown",N,function(A){De.call(this,t,A)}),we("input",N,function(A){De.call(this,t,A)}),we("blur",N,function(A){De.call(this,t,A)}),we("dblclick",N,function(A){De.call(this,t,A)}),we("focus",N,function(A){De.call(this,t,A)}),we("mouseup",N,function(A){De.call(this,t,A)}),we("selectionchange",N,function(A){De.call(this,t,A)}),O(w,k)},$$slots:{default:!0}}),O(e,b),ve()}var fp=re("<div><!></div>"),yp=re("<div><!></div>");const ym={BreadcrumbBackItem:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);br(e,et({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>s,{children:(a,r)=>{var o=Be(),i=Ce(o);se(i,t,"default",{},null),O(a,o)},$$slots:{default:!0,iconLeft:(a,r)=>{Zh(a)}}}))},BreadcrumbItem:Lo,Content:Do,Item:br,Label:function(e,t){_e(t,!1);const[n,s]=He(),a=()=>ke(o,"$sizeState",n),r=Ae(),o=oe(gt).size;xe(()=>a(),()=>{Re(r,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${a()}`].join(" "))}),ft(),be();var i=op(),u=ce(i);Ho(u,{get size(){return a()},weight:"regular",children:(l,d)=>{var c=Be(),h=Ce(c);se(h,t,"default",{},null),O(l,c)},$$slots:{default:!0}}),yt(()=>_t(i,1,Xr(F(r)),"svelte-gehsvg")),O(e,i),ve(),s()},Root:Fo,Separator:function(e,t){_e(t,!1);const[n,s]=He(),a=oe(gt).size;be();var r=up();lp(ce(r),{size:4,orientation:"horizontal"}),yt(()=>_t(r,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${ke(a,"$sizeState",n)}`,"svelte-24h9u")),O(e,r),ve(),s()},Sub:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);_e(t,!1),be();const a=In(()=>(Ye(mn),nt(()=>[mn.Click,mn.Hover])));Fo(e,et({nested:!0,get triggerOn(){return F(a)}},()=>s,{children:(r,o)=>{var i=Be(),u=Ce(i);se(u,t,"default",{},null),O(r,i)},$$slots:{default:!0}})),ve()},SubContent:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);_e(t,!1);const[a,r]=He(),o=()=>ke(d,"$didOpen",a),i=oe(gt).size,u=oe(he.CONTEXT_KEY),l=oe(pn.CONTEXT_KEY),d=kl(l.state,c=>c.open);xe(()=>(o(),Ps),()=>{o()&&Ps().then(()=>u==null?void 0:u.focusIdx(0))}),xe(()=>o(),()=>{!o()&&(u==null||u.popNestedFocus())}),ft(),be(),Do(e,et(()=>s,{side:"right",align:"start",get size(){return ke(i,"$sizeState",a)},children:(c,h)=>{var p=Be(),m=Ce(p);se(m,t,"default",{},null),O(c,p)},$$slots:{default:!0}})),ve(),r()},SubTrigger:function(e,t){_e(t,!1);const[n,s]=He(),a=oe(pn.CONTEXT_KEY).state;be(),Jr(e,{children:(r,o)=>{Lo(r,{get highlight(){return ke(a,"$stateStore",n).open},children:(i,u)=>{var l=Be(),d=Ce(l);se(d,t,"default",{},null),O(i,l)},$$slots:{default:!0}})},$$slots:{default:!0}}),ve(),s()},TextFieldItem:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,["value"]);_e(t,!1);const[a,r]=He(),o=()=>ke(l,"$sizeState",a),i=Ae();let u=x(t,"value",12,"");const l=oe(gt).size;xe(()=>o(),()=>{Re(i,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${o()}`].join(" "))}),ft(),be();var d=fp();gp(ce(d),et({get class(){return Ye(he),nt(()=>he.ITEM_CLASS)},get size(){return o()}},()=>s,{get value(){return u()},set value(c){u(c)},$$legacy:!0})),yt(()=>_t(d,1,Xr(F(i)),"svelte-1xu00bc")),O(e,d),ve(),r()},Trigger:function(e,t){_e(t,!1);const[n,s]=He(),a=()=>ke(u,"$openState",n);let r=x(t,"referenceClientRect",24,()=>{});const o=oe(he.CONTEXT_KEY),i=oe(pn.CONTEXT_KEY),u=i.state;be(),Jr(e,{get referenceClientRect(){return r()},$$events:{keydown:async l=>{switch(l.key){case"ArrowUp":l.preventDefault(),l.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(-1);break;case"ArrowDown":l.preventDefault(),l.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(0);break;case"Enter":l.preventDefault(),l.stopPropagation(),o==null||o.clickFocusedItem()}}},children:(l,d)=>{var c=yp(),h=ce(c);se(h,t,"default",{},null),Os(c,p=>{var m;return(m=o.registerTrigger)==null?void 0:m.call(o,p)}),Os(c,p=>{var m;return(m=i.registerTrigger)==null?void 0:m.call(i,p)}),O(l,c)},$$slots:{default:!0}}),ve(),s()}};export{Lp as $,ls as A,_c as B,gm as C,ym as D,dm as E,ju as F,Mp as G,yd as H,Fp as I,bc as J,$i as K,Sc as L,Yu as M,Rc as N,Tc as O,Ic as P,wc as Q,fm as R,te as S,gp as T,br as U,$p as V,Up as W,Ap as X,Pi as Y,Pp as Z,Dp as _,tt as a,Kp as a$,Rp as a0,vc as a1,Nc as a2,ap as a3,Ku as a4,Jp as a5,sc as a6,zh as a7,Bp as a8,Ze as a9,lc as aA,ic as aB,de as aC,em as aD,sm as aE,Tn as aF,Mo as aG,Zh as aH,oc as aI,ac as aJ,mt as aK,rc as aL,Hp as aM,Fi as aN,Zu as aO,Gp as aP,md as aQ,zi as aR,Yp as aS,jp as aT,Br as aU,gd as aV,fd as aW,Rn as aX,Ac as aY,Ui as aZ,Wp as a_,Li as aa,Xu as ab,Mc as ac,zu as ad,Ao as ae,xc as af,Cc as ag,qp as ah,Vp as ai,Qp as aj,mm as ak,tc as al,nc as am,vh as an,Cp as ao,Op as ap,lp as aq,An as ar,Xp as as,wh as at,Zp as au,Ih as av,qt as aw,nm as ax,Nh as ay,tm as az,Qu as b,cc as b0,uc as b1,om as b2,cm as b3,lm as b4,im as b5,um as b6,am as b7,rm as b8,C as c,M as d,ec as e,Ju as f,ct as g,At as h,j as i,H as j,pt as k,Th as l,le as m,Rh as n,pm as o,hm as p,As as q,wt as r,zp as s,hc as t,pc as u,mc as v,gc as w,xp as x,fc as y,yc as z};
