var Dl=Object.defineProperty;var Ul=(n,e,t)=>e in n?Dl(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var Je=(n,e,t)=>Ul(n,typeof e!="symbol"?e+"":e,t);import{_ as en}from"./preload-helper-Dv6uf1Os.js";import{cz as Fl,cA as Gl,aO as To,bh as Bl,aS as jl,aP as re,aq as Kl,ar as Xi,b7 as Vl,ba as vo,bb as Eo,b8 as Hl,bm as Qi,at as ft,au as F,aQ as Ji,aK as Wl}from"./AugmentMessage-GSkxqNfK.js";import{k as Bt,j as Ai,g as zt,S as zl,w as Yl,x as ql,c as Ro,v as q,y as Ao,l as Xl,z as Ql,A as Jl,B as Zl,C as eu,a as ko,d as C,i as Xe,r as pe,f as ke,D as X}from"./_baseUniq-DK-r-Rl4.js";import{j as ki,m as I,d as tu,f as $e,g as jt,h as $,i as xi,l as Kt,e as nu}from"./_basePickBy-1NYhWL7q.js";import{c as te}from"./clone-C11hY-Tf.js";var ru=Object.prototype.hasOwnProperty,Ie=Fl(function(n,e){if(Gl(e)||To(e))Bl(e,Bt(e),n);else for(var t in e)ru.call(e,t)&&jl(n,t,e[t])});function xo(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),(t=t>i?i:t)<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=n[r+e];return s}function gn(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var s=n[e];s&&(i[r++]=s)}return i}function iu(n,e,t,r){for(var i=-1,s=n==null?0:n.length;++i<s;){var a=n[i];e(r,a,t(a),n)}return r}function su(n,e,t,r){return Ai(n,function(i,s,a){e(r,i,t(i),a)}),r}var Fn=Kl(function(n,e){return Xi(n)?function(t,r,i,s){var a=-1,o=Yl,c=!0,l=t.length,u=[],h=r.length;if(!l)return u;r.length>=200&&(o=ql,c=!1,r=new zl(r));e:for(;++a<l;){var d=t[a],f=d;if(d=d!==0?d:0,c&&f==f){for(var p=h;p--;)if(r[p]===f)continue e;u.push(d)}else o(r,f,s)||u.push(d)}return u}(n,Ro(e,1,Xi,!0)):[]});function ee(n,e,t){var r=n==null?0:n.length;return r?xo(n,(e=e===void 0?1:ki(e))<0?0:e,r):[]}function hn(n,e,t){var r=n==null?0:n.length;return r?xo(n,0,(e=r-(e=e===void 0?1:ki(e)))<0?0:e):[]}function au(n,e){for(var t=-1,r=n==null?0:n.length;++t<r;)if(!e(n[t],t,n))return!1;return!0}function ou(n,e){var t=!0;return Ai(n,function(r,i,s){return t=!!e(r,i,s)}),t}function _e(n,e,t){return(re(n)?au:ou)(n,zt(e))}function Me(n){return n&&n.length?n[0]:void 0}function xe(n,e){return Ro(I(n,e))}var Zi,es,cu=Object.prototype.hasOwnProperty,lu=(Zi=function(n,e,t){cu.call(n,t)?n[t].push(e):Vl(n,t,[e])},function(n,e){var t=re(n)?iu:su,r=es?es():{};return t(n,Zi,zt(e),r)}),uu="[object String]";function Te(n){return typeof n=="string"||!re(n)&&vo(n)&&Eo(n)==uu}var du=Math.max;function fe(n,e,t,r){n=To(n)?n:q(n),t=t?ki(t):0;var i=n.length;return t<0&&(t=du(i+t,0)),Te(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&Ao(n,e,t)>-1}function ts(n,e,t){return n!=null&&n.length?Ao(n,e,0):-1}var ns=Qi&&Qi.isRegExp,qe=ns?Hl(ns):function(n){return vo(n)&&Eo(n)=="[object RegExp]"},hu="Expected a function";function De(n,e){if(n==null)return{};var t=Xl(Ql(n),function(r){return[r]});return e=zt(e),tu(n,t,function(r,i){return e(r,i[0])})}function ar(n,e){return(re(n)?Jl:Zl)(n,function(t){if(typeof t!="function")throw new TypeError(hu);return function(){var r=arguments;switch(r.length){case 0:return!t.call(this);case 1:return!t.call(this,r[0]);case 2:return!t.call(this,r[0],r[1]);case 3:return!t.call(this,r[0],r[1],r[2])}return!t.apply(this,r)}}(zt(e)))}function fu(n,e){var t;return Ai(n,function(r,i,s){return!(t=e(r,i,s))}),!!t}function Io(n,e,t){return(re(n)?eu:fu)(n,zt(e))}function Jr(n){return n&&n.length?ko(n):[]}function le(n){return typeof n=="object"&&n!==null&&typeof n.$type=="string"}function Ge(n){return typeof n=="object"&&n!==null&&typeof n.$refText=="string"}function On(n){return typeof n=="object"&&n!==null&&le(n.container)&&Ge(n.reference)&&typeof n.message=="string"}class So{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return le(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});const i=r[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return r[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const r=this.getAllTypes(),i=[];for(const s of r)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}}function Vt(n){return typeof n=="object"&&n!==null&&Array.isArray(n.content)}function No(n){return typeof n=="object"&&n!==null&&typeof n.tokenType=="object"}function Co(n){return Vt(n)&&typeof n.fullText=="string"}class ce{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){const e=[],t=this.iterator();let r;do r=t.next(),r.value!==void 0&&e.push(r.value);while(!r.done);return e}toSet(){return new Set(this)}toMap(e,t){const r=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(r)}toString(){return this.join()}concat(e){const t=e[Symbol.iterator]();return new ce(()=>({first:this.startFn(),firstDone:!1}),r=>{let i;if(!r.firstDone){do if(i=this.nextFn(r.first),!i.done)return i;while(!i.done);r.firstDone=!0}do if(i=t.next(),!i.done)return i;while(!i.done);return Ce})}join(e=","){const t=this.iterator();let r,i="",s=!1;do r=t.next(),r.done||(s&&(i+=e),i+=pu(r.value)),s=!0;while(!r.done);return i}indexOf(e,t=0){const r=this.iterator();let i=0,s=r.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=r.next(),i++}return-1}every(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;)e(i.value,r),i=t.next(),r++}map(e){return new ce(this.startFn,t=>{const{done:r,value:i}=this.nextFn(t);return r?Ce:{done:!1,value:e(i)}})}filter(e){return new ce(this.startFn,t=>{let r;do if(r=this.nextFn(t),!r.done&&e(r.value))return r;while(!r.done);return Ce})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const r=this.iterator();let i=t,s=r.next();for(;!s.done;)i=i===void 0?s.value:e(i,s.value),s=r.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){const i=e.next();if(i.done)return r;const s=this.recursiveReduce(e,t,r);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;){if(e(i.value))return r;i=t.next(),r++}return-1}includes(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new ce(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(!s.done)return s;t.iterator=void 0}const{done:r,value:i}=this.nextFn(t.this);if(!r){const s=e(i);if(!Gn(s))return{done:!1,value:s};t.iterator=s[Symbol.iterator]()}}while(t.iterator);return Ce})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new ce(()=>({this:t.startFn()}),r=>{do{if(r.iterator){const a=r.iterator.next();if(!a.done)return a;r.iterator=void 0}const{done:i,value:s}=t.nextFn(r.this);if(!i){if(!Gn(s))return{done:!1,value:s};r.iterator=s[Symbol.iterator]()}}while(r.iterator);return Ce})}head(){const e=this.iterator().next();if(!e.done)return e.value}tail(e=1){return new ce(()=>{const t=this.startFn();for(let r=0;r<e;r++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new ce(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?Ce:this.nextFn(t.state)))}distinct(e){const t=new Set;return this.filter(r=>{const i=e?e(r):r;return!t.has(i)&&(t.add(i),!0)})}exclude(e,t){const r=new Set;for(const i of e){const s=t?t(i):i;r.add(s)}return this.filter(i=>{const s=t?t(i):i;return!r.has(s)})}}function pu(n){return typeof n=="string"?n:n===void 0?"undefined":typeof n.toString=="function"?n.toString():Object.prototype.toString.call(n)}function Gn(n){return!!n&&typeof n[Symbol.iterator]=="function"}const mu=new ce(()=>{},()=>Ce),Ce=Object.freeze({done:!0,value:void 0});function ne(...n){if(n.length===1){const e=n[0];if(e instanceof ce)return e;if(Gn(e))return new ce(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new ce(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:Ce)}return n.length>1?new ce(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<n.length){const t=n[e.collIndex++];Gn(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<n.length);return Ce}):mu}class Ii extends ce{constructor(e,t,r){super(()=>({iterators:r!=null&&r.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const s=i.iterators[i.iterators.length-1].next();if(!s.done)return i.iterators.push(t(s.value)[Symbol.iterator]()),s;i.iterators.pop()}return Ce})}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var Zr,Ze;function ei(n){return new Ii(n,e=>Vt(e)?e.content:[],{includeRoot:!0})}function ti(n){return{start:{character:n.startColumn-1,line:n.startLine-1},end:{character:n.endColumn,line:n.endLine-1}}}function Bn(n){if(!n)return;const{offset:e,end:t,range:r}=n;return{range:r,offset:e,end:t,length:t-e}}function gu(n,e){return function(r,i){if(r.end.line<i.start.line||r.end.line===i.start.line&&r.end.character<r.start.character)return Ze.Before;if(r.start.line>i.end.line||r.start.line===i.end.line&&r.start.character>i.end.character)return Ze.After;const s=r.start.line>i.start.line||r.start.line===i.start.line&&r.start.character>=i.start.character,a=r.end.line<i.end.line||r.end.line===i.end.line&&r.end.character<=i.end.character;return s&&a?Ze.Inside:s?Ze.OverlapBack:Ze.OverlapFront}(n,e)>Ze.After}(function(n){n.sum=function(e){return e.reduce((t,r)=>t+r,0)},n.product=function(e){return e.reduce((t,r)=>t*r,0)},n.min=function(e){return e.reduce((t,r)=>Math.min(t,r))},n.max=function(e){return e.reduce((t,r)=>Math.max(t,r))}})(Zr||(Zr={})),function(n){n[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside"}(Ze||(Ze={}));const yu=/^[\w\p{L}]$/u;function Tu(n,e){if(n){const t=function(r,i=!0){for(;r.container;){const s=r.container;let a=s.content.indexOf(r);for(;a>0;){a--;const o=s.content[a];if(i||!o.hidden)return o}r=s}}(n,!0);if(t&&rs(t,e))return t;if(Co(n))for(let r=n.content.findIndex(i=>!i.hidden)-1;r>=0;r--){const i=n.content[r];if(rs(i,e))return i}}}function rs(n,e){return No(n)&&e.includes(n.tokenType.name)}class is extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function or(n){throw new Error("Error! The input value was not handled.")}const Tr="AbstractRule",vr="AbstractType",ss="Condition",as="ValueLiteral",$o="AbstractElement",wo="BooleanLiteral",Lo="Conjunction",bo="Disjunction",Oo="InferredType";function _o(n){return D.isInstance(n,Oo)}const Po="Interface";function Mo(n){return D.isInstance(n,Po)}const Do="Negation",Uo="ParameterReference",Fo="ParserRule";function we(n){return D.isInstance(n,Fo)}const vu="ReturnType",Go="SimpleType",ni="TerminalRule";function pt(n){return D.isInstance(n,ni)}const Bo="Type";function jo(n){return D.isInstance(n,Bo)}const Ko="Action";function cr(n){return D.isInstance(n,Ko)}const Vo="Alternatives";function Ho(n){return D.isInstance(n,Vo)}const Wo="Assignment";function mt(n){return D.isInstance(n,Wo)}const zo="CharacterRange",Yo="CrossReference";function Si(n){return D.isInstance(n,Yo)}const qo="EndOfFile",Xo="Group";function Ni(n){return D.isInstance(n,Xo)}const Qo="Keyword";function gt(n){return D.isInstance(n,Qo)}const Jo="NegatedToken",Zo="RegexToken",ec="RuleCall";function yt(n){return D.isInstance(n,ec)}const tc="TerminalAlternatives",nc="TerminalGroup",rc="TerminalRuleCall",ic="UnorderedGroup";function sc(n){return D.isInstance(n,ic)}const ac="UntilToken",oc="Wildcard";class cc extends So{getAllTypes(){return["AbstractElement","AbstractRule","AbstractType","Action","Alternatives","ArrayLiteral","ArrayType","Assignment","BooleanLiteral","CharacterRange","Condition","Conjunction","CrossReference","Disjunction","EndOfFile","Grammar","GrammarImport","Group","InferredType","Interface","Keyword","NamedArgument","NegatedToken","Negation","NumberLiteral","Parameter","ParameterReference","ParserRule","ReferenceType","RegexToken","ReturnType","RuleCall","SimpleType","StringLiteral","TerminalAlternatives","TerminalGroup","TerminalRule","TerminalRuleCall","Type","TypeAttribute","TypeDefinition","UnionType","UnorderedGroup","UntilToken","ValueLiteral","Wildcard"]}computeIsSubtype(e,t){switch(e){case Ko:case Vo:case Wo:case zo:case Yo:case qo:case Xo:case Qo:case Jo:case Zo:case ec:case tc:case nc:case rc:case ic:case ac:case oc:return this.isSubtype($o,t);case"ArrayLiteral":case"NumberLiteral":case"StringLiteral":return this.isSubtype(as,t);case"ArrayType":case"ReferenceType":case Go:case"UnionType":return this.isSubtype("TypeDefinition",t);case wo:return this.isSubtype(ss,t)||this.isSubtype(as,t);case Lo:case bo:case Do:case Uo:return this.isSubtype(ss,t);case Oo:case Po:case Bo:return this.isSubtype(vr,t);case Fo:return this.isSubtype(Tr,t)||this.isSubtype(vr,t);case ni:return this.isSubtype(Tr,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return vr;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return Tr;case"Grammar:usedGrammars":return"Grammar";case"NamedArgument:parameter":case"ParameterReference:parameter":return"Parameter";case"TerminalRuleCall:rule":return ni;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case"AbstractElement":return{name:"AbstractElement",properties:[{name:"cardinality"},{name:"lookahead"}]};case"ArrayLiteral":return{name:"ArrayLiteral",properties:[{name:"elements",defaultValue:[]}]};case"ArrayType":return{name:"ArrayType",properties:[{name:"elementType"}]};case"BooleanLiteral":return{name:"BooleanLiteral",properties:[{name:"true",defaultValue:!1}]};case"Conjunction":return{name:"Conjunction",properties:[{name:"left"},{name:"right"}]};case"Disjunction":return{name:"Disjunction",properties:[{name:"left"},{name:"right"}]};case"Grammar":return{name:"Grammar",properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case"GrammarImport":return{name:"GrammarImport",properties:[{name:"path"}]};case"InferredType":return{name:"InferredType",properties:[{name:"name"}]};case"Interface":return{name:"Interface",properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case"NamedArgument":return{name:"NamedArgument",properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case"Negation":return{name:"Negation",properties:[{name:"value"}]};case"NumberLiteral":return{name:"NumberLiteral",properties:[{name:"value"}]};case"Parameter":return{name:"Parameter",properties:[{name:"name"}]};case"ParameterReference":return{name:"ParameterReference",properties:[{name:"parameter"}]};case"ParserRule":return{name:"ParserRule",properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case"ReferenceType":return{name:"ReferenceType",properties:[{name:"referenceType"}]};case"ReturnType":return{name:"ReturnType",properties:[{name:"name"}]};case"SimpleType":return{name:"SimpleType",properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case"StringLiteral":return{name:"StringLiteral",properties:[{name:"value"}]};case"TerminalRule":return{name:"TerminalRule",properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case"Type":return{name:"Type",properties:[{name:"name"},{name:"type"}]};case"TypeAttribute":return{name:"TypeAttribute",properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case"UnionType":return{name:"UnionType",properties:[{name:"types",defaultValue:[]}]};case"Action":return{name:"Action",properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case"Alternatives":return{name:"Alternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"Assignment":return{name:"Assignment",properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case"CharacterRange":return{name:"CharacterRange",properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case"CrossReference":return{name:"CrossReference",properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case"EndOfFile":return{name:"EndOfFile",properties:[{name:"cardinality"},{name:"lookahead"}]};case"Group":return{name:"Group",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case"Keyword":return{name:"Keyword",properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case"NegatedToken":return{name:"NegatedToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"RegexToken":return{name:"RegexToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case"RuleCall":return{name:"RuleCall",properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"TerminalAlternatives":return{name:"TerminalAlternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalGroup":return{name:"TerminalGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalRuleCall":return{name:"TerminalRuleCall",properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"UnorderedGroup":return{name:"UnorderedGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"UntilToken":return{name:"UntilToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"Wildcard":return{name:"Wildcard",properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const D=new cc;function lr(n,e){let t=n;for(;t;){if(e(t))return t;t=t.$container}}function Be(n){const e=function(r){for(;r.$container;)r=r.$container;return r}(n),t=e.$document;if(!t)throw new Error("AST node has no document.");return t}function Ci(n,e){if(!n)throw new Error("Node must be an AstNode.");const t=e==null?void 0:e.range;return new ce(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),r=>{for(;r.keyIndex<r.keys.length;){const i=r.keys[r.keyIndex];if(!i.startsWith("$")){const s=n[i];if(le(s)){if(r.keyIndex++,os(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;r.arrayIndex<s.length;){const a=s[r.arrayIndex++];if(le(a)&&os(a,t))return{done:!1,value:a}}r.arrayIndex=0}}r.keyIndex++}return Ce})}function yn(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Ii(n,t=>Ci(t,e))}function It(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Ii(n,t=>Ci(t,e),{includeRoot:!0})}function os(n,e){var t;if(!e)return!0;const r=(t=n.$cstNode)===null||t===void 0?void 0:t.range;return!!r&&gu(r,e)}function lc(n){return new ce(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const r=n[t];if(Ge(r))return e.keyIndex++,{done:!1,value:{reference:r,container:n,property:t}};if(Array.isArray(r)){for(;e.arrayIndex<r.length;){const i=e.arrayIndex++,s=r[i];if(Ge(s))return{done:!1,value:{reference:s,container:n,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Ce})}function uc(n){return Array.isArray(n)?[...n.map(uc)]:n}function w(n){return n.charCodeAt(0)}function Er(n,e){Array.isArray(n)?n.forEach(function(t){e.push(t)}):e.push(n)}function tn(n,e){if(n[e]===!0)throw"duplicate flag "+e;n[e],n[e]=!0}function Tt(n){if(n===void 0)throw Error("Internal Error - Should never get here!");return!0}function cs(n){return n.type==="Character"}const jn=[];for(let n=w("0");n<=w("9");n++)jn.push(n);const Kn=[w("_")].concat(jn);for(let n=w("a");n<=w("z");n++)Kn.push(n);for(let n=w("A");n<=w("Z");n++)Kn.push(n);const ls=[w(" "),w("\f"),w(`
`),w("\r"),w("	"),w("\v"),w("	"),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w("\u2028"),w("\u2029"),w(" "),w(" "),w("　"),w("\uFEFF")],Eu=/[0-9a-fA-F]/,Rn=/[0-9]/,Ru=/[1-9]/;class dc{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":tn(r,"global");break;case"i":tn(r,"ignoreCase");break;case"m":tn(r,"multiLine");break;case"u":tn(r,"unicode");break;case"y":tn(r,"sticky")}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":let t;switch(this.consumeChar("?"),this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead"}Tt(t);const r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return function(){throw Error("Internal Error - Should never get here!")}()}quantifier(e=!1){let t;const r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}")}if(e===!0&&t===void 0)return;Tt(t)}if(e!==!0||t!==void 0)return Tt(t)?(this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t):void 0}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group()}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Tt(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[w(`
`),w("\r"),w("\u2028"),w("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=jn;break;case"D":e=jn,t=!0;break;case"s":e=ls;break;case"S":e=ls,t=!0;break;case"w":e=Kn;break;case"W":e=Kn,t=!0}if(Tt(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=w("\f");break;case"n":e=w(`
`);break;case"r":e=w("\r");break;case"t":e=w("	");break;case"v":e=w("\v")}if(Tt(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:w("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){return{type:"Character",value:w(this.popChar())}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:return{type:"Character",value:w(this.popChar())}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const r=this.classAtom();if(r.type,cs(r)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,cs(i)){if(i.value<r.value)throw Error("Range out of order in character class");e.push({from:r.value,to:i.value})}else Er(r.value,e),e.push(w("-")),Er(i.value,e)}else Er(r.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:w("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;this.consumeChar("("),this.peekChar(0)==="?"?(this.consumeChar("?"),this.consumeChar(":"),e=!1):this.groupIdx++;const t=this.disjunction();this.consumeChar(")");const r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(Ru.test(e)===!1)throw Error("Expecting a positive integer");for(;Rn.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Rn.test(e)===!1)throw Error("Expecting an integer");for(;Rn.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:w(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Rn.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let r=0;r<e;r++){const i=this.popChar();if(Eu.test(i)===!1)throw Error("Expecting a HexDecimal digits");t+=i}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class Vn{visitChildren(e){for(const t in e){const r=e[t];e.hasOwnProperty(t)&&(r.type!==void 0?this.visit(r):Array.isArray(r)&&r.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e)}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}const Au=/\r?\n/gm,ku=new dc,Rr=new class extends Vn{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(n){this.multiline=!1,this.regex=n,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(n){n.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(n){const e=String.fromCharCode(n.value);if(this.multiline||e!==`
`||(this.multiline=!0),n.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=ur(e);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitSet(n){if(!this.multiline){const e=this.regex.substring(n.loc.begin,n.loc.end),t=new RegExp(e);this.multiline=!!`
`.match(t)}if(n.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const e=this.regex.substring(n.loc.begin,n.loc.end);this.endRegexpStack.push(e),this.isStarting&&(this.startRegexp+=e)}}visitChildren(n){n.type==="Group"&&n.quantifier||super.visitChildren(n)}};function xu(n){try{return typeof n=="string"&&(n=new RegExp(n)),n=n.toString(),Rr.reset(n),Rr.visit(ku.pattern(n)),Rr.multiline}catch{return!1}}function us(n){return(typeof n=="string"?new RegExp(n):n).test(" ")}function ur(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Iu(n,e){const t=function(i){typeof i=="string"&&(i=new RegExp(i));const s=i,a=i.source;let o=0;function c(){let l,u="";function h(f){u+=a.substr(o,f),o+=f}function d(f){u+="(?:"+a.substr(o,f)+"|$)",o+=f}for(;o<a.length;)switch(a[o]){case"\\":switch(a[o+1]){case"c":d(3);break;case"x":d(4);break;case"u":s.unicode?a[o+2]==="{"?d(a.indexOf("}",o)-o+1):d(6):d(2);break;case"p":case"P":s.unicode?d(a.indexOf("}",o)-o+1):d(2);break;case"k":d(a.indexOf(">",o)-o+1);break;default:d(2)}break;case"[":l=/\[(?:\\.|.)*?\]/g,l.lastIndex=o,l=l.exec(a)||[],d(l[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":h(1);break;case"{":l=/\{\d+,?\d*\}/g,l.lastIndex=o,l=l.exec(a),l?h(l[0].length):d(1);break;case"(":if(a[o+1]==="?")switch(a[o+2]){case":":u+="(?:",o+=3,u+=c()+"|$)";break;case"=":u+="(?=",o+=3,u+=c()+")";break;case"!":l=o,o+=3,c(),u+=a.substr(l,o-l);break;case"<":switch(a[o+3]){case"=":case"!":l=o,o+=4,c(),u+=a.substr(l,o-l);break;default:h(a.indexOf(">",o)-o+1),u+=c()+"|$)"}}else h(1),u+=c()+"|$)";break;case")":return++o,u;default:d(1)}return u}return new RegExp(c(),i.flags)}(n),r=e.match(t);return!!r&&r[0].length>0}function hc(n,e){const t=new Set,r=function(a){return a.rules.find(o=>we(o)&&o.entry)}(n);if(!r)return new Set(n.rules);const i=[r].concat(function(a){return a.rules.filter(o=>pt(o)&&o.hidden)}(n));for(const a of i)fc(a,t,e);const s=new Set;for(const a of n.rules)(t.has(a.name)||pt(a)&&a.hidden)&&s.add(a);return s}function fc(n,e,t){e.add(n.name),yn(n).forEach(r=>{if(yt(r)||t){const i=r.rule.ref;i&&!e.has(i.name)&&fc(i,e,t)}})}function pc(n,e,t){if(!n||!e)return;const r=$i(n,e,n.astNode,!0);return r.length!==0?r[t=t!==void 0?Math.max(0,Math.min(t,r.length-1)):0]:void 0}function $i(n,e,t,r){if(!r){const i=lr(n.grammarSource,mt);if(i&&i.feature===e)return[n]}return Vt(n)&&n.astNode===t?n.content.flatMap(i=>$i(i,e,t,!1)):[]}function Su(n,e,t){if(!n)return;const r=function(i,s,a){if(i.astNode!==a)return[];if(gt(i.grammarSource)&&i.grammarSource.value===s)return[i];const o=ei(i).iterator();let c;const l=[];do if(c=o.next(),!c.done){const u=c.value;u.astNode===a?gt(u.grammarSource)&&u.grammarSource.value===s&&l.push(u):o.prune()}while(!c.done);return l}(n,e,n==null?void 0:n.astNode);return r.length!==0?r[t=t!==void 0?Math.max(0,Math.min(t,r.length-1)):0]:void 0}function mc(n){let e=n;return _o(e)&&(cr(e.$container)?e=e.$container.$container:we(e.$container)?e=e.$container:or(e.$container)),gc(n,e,new Map)}function gc(n,e,t){var r,i;function s(a,o){let c;return lr(a,mt)||(c=gc(o,o,t)),t.set(n,c),c}if(t.has(n))return t.get(n);t.set(n,void 0);for(const a of yn(e)){if(mt(a)&&a.feature.toLowerCase()==="name")return t.set(n,a),a;if(yt(a)&&we(a.rule.ref))return s(a,a.rule.ref);if(i=a,D.isInstance(i,Go)&&((r=a.typeRef)===null||r===void 0?void 0:r.ref))return s(a,a.typeRef.ref)}}function wi(n){return yc(n,new Set)}function yc(n,e){if(e.has(n))return!0;e.add(n);for(const t of yn(n))if(yt(t)){if(!t.rule.ref||we(t.rule.ref)&&!yc(t.rule.ref,e))return!1}else if(mt(t)||cr(t))return!1;return!!n.definition}function Tc(n){if(n.inferredType)return n.inferredType.name;if(n.dataType)return n.dataType;if(n.returnType){const e=n.returnType.ref;if(e&&(we(e)||Mo(e)||jo(e)))return e.name}}function dr(n){var e,t;if(we(n))return wi(n)?n.name:(e=Tc(n))!==null&&e!==void 0?e:n.name;if(Mo(n)||jo(n)||(t=n,D.isInstance(t,vu)))return n.name;if(cr(n)){const r=function(i){var s;if(i.inferredType)return i.inferredType.name;if(!((s=i.type)===null||s===void 0)&&s.ref)return dr(i.type.ref)}(n);if(r)return r}else if(_o(n))return n.name;throw new Error("Cannot get name of Unknown Type")}function ri(n){const e={s:!1,i:!1,u:!1},t=kt(n.definition,e),r=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,r)}const Ar=/[\s\S]/.source;function kt(n,e){if(s=n,D.isInstance(s,tc))return He((i=n).elements.map(a=>kt(a)).join("|"),{cardinality:i.cardinality,lookahead:i.lookahead});if(function(a){return D.isInstance(a,nc)}(n))return He((r=n).elements.map(a=>kt(a)).join(""),{cardinality:r.cardinality,lookahead:r.lookahead});if(function(a){return D.isInstance(a,zo)}(n))return function(a){return a.right?He(`[${kr(a.left)}-${kr(a.right)}]`,{cardinality:a.cardinality,lookahead:a.lookahead,wrap:!1}):He(kr(a.left),{cardinality:a.cardinality,lookahead:a.lookahead,wrap:!1})}(n);if(function(a){return D.isInstance(a,rc)}(n)){const a=n.rule.ref;if(!a)throw new Error("Missing rule reference.");return He(kt(a.definition),{cardinality:n.cardinality,lookahead:n.lookahead})}if(function(a){return D.isInstance(a,Jo)}(n))return function(a){return He(`(?!${kt(a.terminal)})${Ar}*?`,{cardinality:a.cardinality,lookahead:a.lookahead})}(n);if(function(a){return D.isInstance(a,ac)}(n))return He(`${Ar}*?${kt((t=n).terminal)}`,{cardinality:t.cardinality,lookahead:t.lookahead});if(function(a){return D.isInstance(a,Zo)}(n)){const a=n.regex.lastIndexOf("/"),o=n.regex.substring(1,a),c=n.regex.substring(a+1);return e&&(e.i=c.includes("i"),e.s=c.includes("s"),e.u=c.includes("u")),He(o,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}if(function(a){return D.isInstance(a,oc)}(n))return He(Ar,{cardinality:n.cardinality,lookahead:n.lookahead});throw new Error(`Invalid terminal element: ${n==null?void 0:n.$type}`);var t,r,i,s}function kr(n){return ur(n.value)}function He(n,e){var t;return(e.wrap!==!1||e.lookahead)&&(n=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${n})`),e.cardinality?`${n}${e.cardinality}`:n}function ii(n){console&&console.error&&console.error(`Error: ${n}`)}function vc(n){console&&console.warn&&console.warn(`Warning: ${n}`)}function Ec(n){const e=new Date().getTime(),t=n();return{time:new Date().getTime()-e,value:t}}function Rc(n){function e(){}e.prototype=n;const t=new e;function r(){return typeof t.bar}return r(),r(),n}function Nu(n){return Te((e=n).LABEL)&&e.LABEL!==""?n.LABEL:n.name;var e}class je{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),C(this.definition,t=>{t.accept(e)})}}class me extends je{constructor(e){super([]),this.idx=1,Ie(this,De(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class Yt extends je{constructor(e){super(e.definition),this.orgText="",Ie(this,De(e,t=>t!==void 0))}}class ve extends je{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,Ie(this,De(e,t=>t!==void 0))}}class ie extends je{constructor(e){super(e.definition),this.idx=1,Ie(this,De(e,t=>t!==void 0))}}class Se extends je{constructor(e){super(e.definition),this.idx=1,Ie(this,De(e,t=>t!==void 0))}}class Ne extends je{constructor(e){super(e.definition),this.idx=1,Ie(this,De(e,t=>t!==void 0))}}class W extends je{constructor(e){super(e.definition),this.idx=1,Ie(this,De(e,t=>t!==void 0))}}class Ee extends je{constructor(e){super(e.definition),this.idx=1,Ie(this,De(e,t=>t!==void 0))}}class Re extends je{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,Ie(this,De(e,t=>t!==void 0))}}class G{constructor(e){this.idx=1,Ie(this,De(e,t=>t!==void 0))}accept(e){e.visit(this)}}function _n(n){function e(t){return I(t,_n)}if(n instanceof me){const t={type:"NonTerminal",name:n.nonTerminalName,idx:n.idx};return Te(n.label)&&(t.label=n.label),t}if(n instanceof ve)return{type:"Alternative",definition:e(n.definition)};if(n instanceof ie)return{type:"Option",idx:n.idx,definition:e(n.definition)};if(n instanceof Se)return{type:"RepetitionMandatory",idx:n.idx,definition:e(n.definition)};if(n instanceof Ne)return{type:"RepetitionMandatoryWithSeparator",idx:n.idx,separator:_n(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof Ee)return{type:"RepetitionWithSeparator",idx:n.idx,separator:_n(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof W)return{type:"Repetition",idx:n.idx,definition:e(n.definition)};if(n instanceof Re)return{type:"Alternation",idx:n.idx,definition:e(n.definition)};if(n instanceof G){const t={type:"Terminal",name:n.terminalType.name,label:Nu(n.terminalType),idx:n.idx};Te(n.label)&&(t.terminalLabel=n.label);const r=n.terminalType.PATTERN;return n.terminalType.PATTERN&&(t.pattern=qe(r)?r.source:r),t}if(n instanceof Yt)return{type:"Rule",name:n.name,orgText:n.orgText,definition:e(n.definition)};throw Error("non exhaustive match")}class qt{visit(e){const t=e;switch(t.constructor){case me:return this.visitNonTerminal(t);case ve:return this.visitAlternative(t);case ie:return this.visitOption(t);case Se:return this.visitRepetitionMandatory(t);case Ne:return this.visitRepetitionMandatoryWithSeparator(t);case Ee:return this.visitRepetitionWithSeparator(t);case W:return this.visitRepetition(t);case Re:return this.visitAlternation(t);case G:return this.visitTerminal(t);case Yt:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}function Hn(n,e=[]){return n instanceof ie||n instanceof W||n instanceof Ee||(n instanceof Re?Io(n.definition,t=>Hn(t,e)):!(n instanceof me&&fe(e,n))&&n instanceof je&&(n instanceof me&&e.push(n),_e(n.definition,t=>Hn(t,e))))}function Fe(n){if(n instanceof me)return"SUBRULE";if(n instanceof ie)return"OPTION";if(n instanceof Re)return"OR";if(n instanceof Se)return"AT_LEAST_ONE";if(n instanceof Ne)return"AT_LEAST_ONE_SEP";if(n instanceof Ee)return"MANY_SEP";if(n instanceof W)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}class hr{walk(e,t=[]){C(e.definition,(r,i)=>{const s=ee(e.definition,i+1);if(r instanceof me)this.walkProdRef(r,s,t);else if(r instanceof G)this.walkTerminal(r,s,t);else if(r instanceof ve)this.walkFlat(r,s,t);else if(r instanceof ie)this.walkOption(r,s,t);else if(r instanceof Se)this.walkAtLeastOne(r,s,t);else if(r instanceof Ne)this.walkAtLeastOneSep(r,s,t);else if(r instanceof Ee)this.walkManySep(r,s,t);else if(r instanceof W)this.walkMany(r,s,t);else{if(!(r instanceof Re))throw Error("non exhaustive match");this.walkOr(r,s,t)}})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){const i=t.concat(r);this.walk(e,i)}walkOption(e,t,r){const i=t.concat(r);this.walk(e,i)}walkAtLeastOne(e,t,r){const i=[new ie({definition:e.definition})].concat(t,r);this.walk(e,i)}walkAtLeastOneSep(e,t,r){const i=ds(e,t,r);this.walk(e,i)}walkMany(e,t,r){const i=[new ie({definition:e.definition})].concat(t,r);this.walk(e,i)}walkManySep(e,t,r){const i=ds(e,t,r);this.walk(e,i)}walkOr(e,t,r){const i=t.concat(r);C(e.definition,s=>{const a=new ve({definition:[s]});this.walk(a,i)})}}function ds(n,e,t){return[new ie({definition:[new G({terminalType:n.separator})].concat(n.definition)})].concat(e,t)}function ln(n){if(n instanceof me)return ln(n.referencedRule);if(n instanceof G)return[n.terminalType];if(function(e){return e instanceof ve||e instanceof ie||e instanceof W||e instanceof Se||e instanceof Ne||e instanceof Ee||e instanceof G||e instanceof Yt}(n))return function(e){let t=[];const r=e.definition;let i,s=0,a=r.length>s,o=!0;for(;a&&o;)i=r[s],o=Hn(i),t=t.concat(ln(i)),s+=1,a=r.length>s;return Jr(t)}(n);if(function(e){return e instanceof Re}(n))return function(e){const t=I(e.definition,r=>ln(r));return Jr($e(t))}(n);throw Error("non exhaustive match")}const Ac="_~IN~_";class Cu extends hr{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){const i=(s=e.referencedRule,a=e.idx,s.name+a+Ac+this.topProd.name);var s,a;const o=t.concat(r),c=ln(new ve({definition:o}));this.follows[i]=c}}let Pn={};const $u=new dc;function Wn(n){const e=n.toString();if(Pn.hasOwnProperty(e))return Pn[e];{const t=$u.pattern(e);return Pn[e]=t,t}}const kc="Complement Sets are not supported for first char optimization",zn=`Unable to use "first char" lexer optimizations:
`;function wu(n,e=!1){try{const t=Wn(n);return si(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===kc)e&&vc(`${zn}	Unable to optimize: < ${n.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";e&&(r=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),ii(`${zn}
	Failed parsing: < ${n.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}function si(n,e,t){switch(n.type){case"Disjunction":for(let i=0;i<n.value.length;i++)si(n.value[i],e,t);break;case"Alternative":const r=n.value;for(let i=0;i<r.length;i++){const s=r[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":An(a.value,e,t);break;case"Set":if(a.complement===!0)throw Error(kc);C(a.value,c=>{if(typeof c=="number")An(c,e,t);else{const l=c;if(t===!0)for(let u=l.from;u<=l.to;u++)An(u,e,t);else{for(let u=l.from;u<=l.to&&u<on;u++)An(u,e,t);if(l.to>=on){const u=l.from>=on?l.from:on,h=l.to,d=it(u),f=it(h);for(let p=d;p<=f;p++)e[p]=p}}}});break;case"Group":si(a.value,e,t);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&ai(a)===!1||a.type!=="Group"&&o===!1)break}break;default:throw Error("non exhaustive match!")}return q(e)}function An(n,e,t){const r=it(n);e[r]=r,t===!0&&function(i,s){const a=String.fromCharCode(i),o=a.toUpperCase();if(o!==a){const c=it(o.charCodeAt(0));s[c]=c}else{const c=a.toLowerCase();if(c!==a){const l=it(c.charCodeAt(0));s[l]=l}}}(n,e)}function hs(n,e){return jt(n.value,t=>{if(typeof t=="number")return fe(e,t);{const r=t;return jt(e,i=>r.from<=i&&i<=r.to)!==void 0}})}function ai(n){const e=n.quantifier;return!(!e||e.atLeast!==0)||!!n.value&&(re(n.value)?_e(n.value,ai):ai(n.value))}class Lu extends Vn{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":return void this.visitLookahead(e);case"NegativeLookahead":return void this.visitNegativeLookahead(e)}super.visitChildren(e)}}visitCharacter(e){fe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?hs(e,this.targetCharCodes)===void 0&&(this.found=!0):hs(e,this.targetCharCodes)!==void 0&&(this.found=!0)}}function Li(n,e){if(e instanceof RegExp){const t=Wn(e),r=new Lu(n);return r.visit(t),r.found}return jt(e,t=>fe(n,t.charCodeAt(0)))!==void 0}const dt="PATTERN",nn="defaultMode",xr="modes";let xc=typeof new RegExp("(?:)").sticky=="boolean";function bu(n,e){const t=(e=xi(e,{useSticky:xc,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(T,v)=>v()})).tracer;let r;t("initCharCodeToOptimizedIndexMap",()=>{(function(){if(F(Mn)){Mn=new Array(65536);for(let T=0;T<65536;T++)Mn[T]=T>255?255+~~(T/255):T}})()}),t("Reject Lexer.NA",()=>{r=ar(n,T=>T[dt]===ye.NA)});let i,s,a,o,c,l,u,h,d,f,p,m=!1;t("Transform Patterns",()=>{m=!1,i=I(r,T=>{const v=T[dt];if(qe(v)){const x=v.source;return x.length!==1||x==="^"||x==="$"||x==="."||v.ignoreCase?x.length!==2||x[0]!=="\\"||fe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],x[1])?e.useSticky?ps(v):fs(v):x[1]:x}if(ft(v))return m=!0,{exec:v};if(typeof v=="object")return m=!0,v;if(typeof v=="string"){if(v.length===1)return v;{const x=v.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),P=new RegExp(x);return e.useSticky?ps(P):fs(P)}}throw Error("non exhaustive match")})}),t("misc mapping",()=>{s=I(r,T=>T.tokenTypeIdx),a=I(r,T=>{const v=T.GROUP;if(v!==ye.SKIPPED){if(Te(v))return v;if(Xe(v))return!1;throw Error("non exhaustive match")}}),o=I(r,T=>{const v=T.LONGER_ALT;if(v)return re(v)?I(v,x=>ts(r,x)):[ts(r,v)]}),c=I(r,T=>T.PUSH_MODE),l=I(r,T=>$(T,"POP_MODE"))}),t("Line Terminator Handling",()=>{const T=Nc(e.lineTerminatorCharacters);u=I(r,v=>!1),e.positionTracking!=="onlyOffset"&&(u=I(r,v=>$(v,"LINE_BREAKS")?!!v.LINE_BREAKS:Sc(v,T)===!1&&Li(T,v.PATTERN)))}),t("Misc Mapping #2",()=>{h=I(r,Ic),d=I(i,Du),f=pe(r,(T,v)=>{const x=v.GROUP;return Te(x)&&x!==ye.SKIPPED&&(T[x]=[]),T},{}),p=I(i,(T,v)=>({pattern:i[v],longerAlt:o[v],canLineTerminator:u[v],isCustom:h[v],short:d[v],group:a[v],push:c[v],pop:l[v],tokenTypeIdx:s[v],tokenType:r[v]}))});let y=!0,E=[];return e.safeMode||t("First Char Optimization",()=>{E=pe(r,(T,v,x)=>{if(typeof v.PATTERN=="string"){const P=it(v.PATTERN.charCodeAt(0));Ir(T,P,p[x])}else if(re(v.START_CHARS_HINT)){let P;C(v.START_CHARS_HINT,U=>{const B=it(typeof U=="string"?U.charCodeAt(0):U);P!==B&&(P=B,Ir(T,B,p[x]))})}else if(qe(v.PATTERN))if(v.PATTERN.unicode)y=!1,e.ensureOptimizations&&ii(`${zn}	Unable to analyze < ${v.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const P=wu(v.PATTERN,e.ensureOptimizations);F(P)&&(y=!1),C(P,U=>{Ir(T,U,p[x])})}else e.ensureOptimizations&&ii(`${zn}	TokenType: <${v.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),y=!1;return T},[])}),{emptyGroups:f,patternIdxToConfig:p,charCodeToPatternIdxToConfig:E,hasCustom:m,canBeOptimized:y}}function Ou(n,e){let t=[];const r=function(a){const o=ke(a,u=>!$(u,dt)),c=I(o,u=>({message:"Token Type: ->"+u.name+"<- missing static 'PATTERN' property",type:H.MISSING_PATTERN,tokenTypes:[u]})),l=Fn(a,o);return{errors:c,valid:l}}(n);t=t.concat(r.errors);const i=function(a){const o=ke(a,u=>{const h=u[dt];return!(qe(h)||ft(h)||$(h,"exec")||Te(h))}),c=I(o,u=>({message:"Token Type: ->"+u.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:H.INVALID_PATTERN,tokenTypes:[u]})),l=Fn(a,o);return{errors:c,valid:l}}(r.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(function(a){let o=[];const c=ke(a,l=>qe(l[dt]));return o=o.concat(function(l){class u extends Vn{constructor(){super(...arguments),this.found=!1}visitEndAnchor(p){this.found=!0}}const h=ke(l,f=>{const p=f.PATTERN;try{const m=Wn(p),y=new u;return y.visit(m),y.found}catch{return _u.test(p.source)}});return I(h,f=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+f.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:H.EOI_ANCHOR_FOUND,tokenTypes:[f]}))}(c)),o=o.concat(function(l){class u extends Vn{constructor(){super(...arguments),this.found=!1}visitStartAnchor(p){this.found=!0}}const h=ke(l,f=>{const p=f.PATTERN;try{const m=Wn(p),y=new u;return y.visit(m),y.found}catch{return Pu.test(p.source)}});return I(h,f=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+f.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:H.SOI_ANCHOR_FOUND,tokenTypes:[f]}))}(c)),o=o.concat(function(l){const u=ke(l,d=>{const f=d[dt];return f instanceof RegExp&&(f.multiline||f.global)});return I(u,d=>({message:"Token Type: ->"+d.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:H.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[d]}))}(c)),o=o.concat(function(l){const u=[];let h=I(l,p=>pe(l,(m,y)=>(p.PATTERN.source!==y.PATTERN.source||fe(u,y)||y.PATTERN===ye.NA||(u.push(y),m.push(y)),m),[]));h=gn(h);const d=ke(h,p=>p.length>1);return I(d,p=>{const m=I(p,y=>y.name);return{message:`The same RegExp pattern ->${Me(p).PATTERN}<-has been used in all of the following Token Types: ${m.join(", ")} <-`,type:H.DUPLICATE_PATTERNS_FOUND,tokenTypes:p}})}(c)),o=o.concat(function(l){const u=ke(l,d=>d.PATTERN.test(""));return I(u,d=>({message:"Token Type: ->"+d.name+"<- static 'PATTERN' must not match an empty string",type:H.EMPTY_MATCH_PATTERN,tokenTypes:[d]}))}(c)),o}(s)),t=t.concat(function(a){const o=ke(a,l=>{if(!$(l,"GROUP"))return!1;const u=l.GROUP;return u!==ye.SKIPPED&&u!==ye.NA&&!Te(u)});return I(o,l=>({message:"Token Type: ->"+l.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:H.INVALID_GROUP_TYPE_FOUND,tokenTypes:[l]}))}(s)),t=t.concat(function(a,o){const c=ke(a,u=>u.PUSH_MODE!==void 0&&!fe(o,u.PUSH_MODE));return I(c,u=>({message:`Token Type: ->${u.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${u.PUSH_MODE}<-which does not exist`,type:H.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[u]}))}(s,e)),t=t.concat(function(a){const o=[],c=pe(a,(l,u,h)=>{const d=u.PATTERN;return d===ye.NA||(Te(d)?l.push({str:d,idx:h,tokenType:u}):qe(d)&&(f=d,jt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],p=>f.source.indexOf(p)!==-1)===void 0)&&l.push({str:d.source,idx:h,tokenType:u})),l;var f},[]);return C(a,(l,u)=>{C(c,({str:h,idx:d,tokenType:f})=>{if(u<d&&function(p,m){if(qe(m)){const y=m.exec(p);return y!==null&&y.index===0}if(ft(m))return m(p,0,[],{});if($(m,"exec"))return m.exec(p,0,[],{});if(typeof m=="string")return m===p;throw Error("non exhaustive match")}(h,l.PATTERN)){const p=`Token: ->${f.name}<- can never be matched.
Because it appears AFTER the Token Type ->${l.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;o.push({message:p,type:H.UNREACHABLE_PATTERN,tokenTypes:[l,f]})}})}),o}(s)),t}const _u=/[^\\][$]/,Pu=/[^\\[][\^]|^\^/;function fs(n){const e=n.ignoreCase?"i":"";return new RegExp(`^(?:${n.source})`,e)}function ps(n){const e=n.ignoreCase?"iy":"y";return new RegExp(`${n.source}`,e)}function Mu(n,e,t){const r=[];let i=!1;const s=ar(gn($e(q(n.modes))),o=>o[dt]===ye.NA),a=Nc(t);return e&&C(s,o=>{const c=Sc(o,a);if(c!==!1){const l=function(h,d){if(d.issue===H.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${h.name}> Token Type
	 Root cause: ${d.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(d.issue===H.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${h.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}(o,c),u={message:l,type:c.issue,tokenType:o};r.push(u)}else $(o,"LINE_BREAKS")?o.LINE_BREAKS===!0&&(i=!0):Li(a,o.PATTERN)&&(i=!0)}),e&&!i&&r.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:H.NO_LINE_BREAKS_FLAGS}),r}function Ic(n){const e=n.PATTERN;if(qe(e))return!1;if(ft(e)||$(e,"exec"))return!0;if(Te(e))return!1;throw Error("non exhaustive match")}function Du(n){return!(!Te(n)||n.length!==1)&&n.charCodeAt(0)}const Uu={test:function(n){const e=n.length;for(let t=this.lastIndex;t<e;t++){const r=n.charCodeAt(t);if(r===10)return this.lastIndex=t+1,!0;if(r===13)return n.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},lastIndex:0};function Sc(n,e){if($(n,"LINE_BREAKS"))return!1;if(qe(n.PATTERN)){try{Li(e,n.PATTERN)}catch(t){return{issue:H.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}if(Te(n.PATTERN))return!1;if(Ic(n))return{issue:H.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}function Nc(n){return I(n,e=>Te(e)?e.charCodeAt(0):e)}function Ir(n,e,t){n[e]===void 0?n[e]=[t]:n[e].push(t)}const on=256;let Mn=[];function it(n){return n<on?n:Mn[n]}function un(n,e){const t=n.tokenTypeIdx;return t===e.tokenTypeIdx||e.isParent===!0&&e.categoryMatchesMap[t]===!0}function Yn(n,e){return n.tokenTypeIdx===e.tokenTypeIdx}let ms=1;const gs={};function Tn(n){const e=function(t){let r=te(t),i=t,s=!0;for(;s;){i=gn($e(I(i,o=>o.CATEGORIES)));const a=Fn(i,r);r=r.concat(a),F(a)?s=!1:i=a}return r}(n);(function(t){C(t,r=>{$c(r)||(gs[ms]=r,r.tokenTypeIdx=ms++),ys(r)&&!re(r.CATEGORIES)&&(r.CATEGORIES=[r.CATEGORIES]),ys(r)||(r.CATEGORIES=[]),$(r,"categoryMatches")||(r.categoryMatches=[]),function(i){return $(i,"categoryMatchesMap")}(r)||(r.categoryMatchesMap={})})})(e),function(t){C(t,r=>{Cc([],r)})}(e),function(t){C(t,r=>{r.categoryMatches=[],C(r.categoryMatchesMap,(i,s)=>{r.categoryMatches.push(gs[s].tokenTypeIdx)})})}(e),C(e,t=>{t.isParent=t.categoryMatches.length>0})}function Cc(n,e){C(n,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),C(e.CATEGORIES,t=>{const r=n.concat(e);fe(r,t)||Cc(r,t)})}function $c(n){return $(n,"tokenTypeIdx")}function ys(n){return $(n,"CATEGORIES")}function Fu(n){return $(n,"tokenTypeIdx")}const Gu={buildUnableToPopLexerModeMessage:n=>`Unable to pop Lexer Mode after encountering Token ->${n.image}<- The Mode Stack is empty`,buildUnexpectedCharactersMessage:(n,e,t,r,i)=>`unexpected character: ->${n.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`};var H;(function(n){n[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(H||(H={}));const cn={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Gu,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(cn);class ye{constructor(e,t=cn){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${a}--> <${i}>`);const{time:o,value:c}=Ec(s),l=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&l(`${a}<-- <${i}> time: ${o}ms`),this.traceInitIndent--,c}return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=Ie({},cn,t);const r=this.config.traceInitPerf;r===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof r=="number"&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===cn.lineTerminatorsPattern)this.config.lineTerminatorsPattern=Uu;else if(this.config.lineTerminatorCharacters===cn.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),re(e)?i={modes:{defaultMode:te(e)},defaultMode:nn}:(s=!1,i=te(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(o,c,l){const u=[];return $(o,nn)||u.push({message:"A MultiMode Lexer cannot be initialized without a <"+nn+`> property in its definition
`,type:H.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),$(o,xr)||u.push({message:`A MultiMode Lexer cannot be initialized without a <modes> property in its definition
`,type:H.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),$(o,xr)&&$(o,nn)&&!$(o.modes,o.defaultMode)&&u.push({message:`A MultiMode Lexer cannot be initialized with a ${nn}: <${o.defaultMode}>which does not exist
`,type:H.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),$(o,xr)&&C(o.modes,(h,d)=>{C(h,(f,p)=>{if(Xe(f))u.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${d}> at index: <${p}>
`,type:H.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if($(f,"LONGER_ALT")){const m=re(f.LONGER_ALT)?f.LONGER_ALT:[f.LONGER_ALT];C(m,y=>{Xe(y)||fe(h,y)||u.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${y.name}> on token <${f.name}> outside of mode <${d}>
`,type:H.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),u}(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Mu(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},C(i.modes,(o,c)=>{i.modes[c]=ar(o,l=>Xe(l))});const a=Bt(i.modes);if(C(i.modes,(o,c)=>{this.TRACE_INIT(`Mode: <${c}> processing`,()=>{if(this.modes.push(c),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Ou(o,a))}),F(this.lexerDefinitionErrors)){let l;Tn(o),this.TRACE_INIT("analyzeTokenTypes",()=>{l=bu(o,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[c]=l.patternIdxToConfig,this.charCodeToPatternIdxToConfig[c]=l.charCodeToPatternIdxToConfig,this.emptyGroups=Ie({},this.emptyGroups,l.emptyGroups),this.hasCustom=l.hasCustom||this.hasCustom,this.canModeBeOptimized[c]=l.canBeOptimized}})}),this.defaultMode=i.defaultMode,!F(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const o=I(this.lexerDefinitionErrors,l=>l.message),c=o.join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+c)}C(this.lexerDefinitionWarning,o=>{vc(o.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(xc?(this.chopInput=Ji,this.match=this.matchWithTest):(this.updateLastIndex=X,this.match=this.matchWithExec),s&&(this.handleModes=X),this.trackStartLines===!1&&(this.computeNewColumn=Ji),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=X),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else{if(!/onlyOffset/i.test(this.config.positionTracking))throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.createTokenInstance=this.createOffsetOnlyToken}this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const o=pe(this.canModeBeOptimized,(c,l,u)=>(l===!1&&c.push(u),c),[]);if(t.ensureOptimizations&&!F(o))throw Error(`Lexer Modes: < ${o.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Pn={}}),this.TRACE_INIT("toFastProperties",()=>{Rc(this)})})}tokenize(e,t=this.defaultMode){if(!F(this.lexerDefinitionErrors)){const r=I(this.lexerDefinitionErrors,s=>s.message),i=r.join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,i,s,a,o,c,l,u,h,d,f,p,m,y,E;const T=e,v=T.length;let x=0,P=0;const U=this.hasCustom?0:Math.floor(e.length/10),B=new Array(U),Z=[];let se=this.trackStartLines?1:void 0,ge=this.trackStartLines?1:void 0;const A=function(Y){const ue={},Qe=Bt(Y);return C(Qe,de=>{const Oe=Y[de];if(!re(Oe))throw Error("non exhaustive match");ue[de]=[]}),ue}(this.emptyGroups),R=this.trackStartLines,k=this.config.lineTerminatorsPattern;let S=0,O=[],b=[];const L=[],Ae=[];let ae;function V(){return O}function at(Y){const ue=it(Y),Qe=b[ue];return Qe===void 0?Ae:Qe}Object.freeze(Ae);const Ml=Y=>{if(L.length===1&&Y.tokenType.PUSH_MODE===void 0){const ue=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(Y);Z.push({offset:Y.startOffset,line:Y.startLine,column:Y.startColumn,length:Y.image.length,message:ue})}else{L.pop();const ue=Kt(L);O=this.patternIdxToConfig[ue],b=this.charCodeToPatternIdxToConfig[ue],S=O.length;const Qe=this.canModeBeOptimized[ue]&&this.config.safeMode===!1;ae=b&&Qe?at:V}};function zi(Y){L.push(Y),b=this.charCodeToPatternIdxToConfig[Y],O=this.patternIdxToConfig[Y],S=O.length,S=O.length;const ue=this.canModeBeOptimized[Y]&&this.config.safeMode===!1;ae=b&&ue?at:V}let be;zi.call(this,t);const Yi=this.config.recoveryEnabled;for(;x<v;){c=null;const Y=T.charCodeAt(x),ue=ae(Y),Qe=ue.length;for(r=0;r<Qe;r++){be=ue[r];const de=be.pattern;l=null;const Oe=be.short;if(Oe!==!1?Y===Oe&&(c=de):be.isCustom===!0?(E=de.exec(T,x,B,A),E!==null?(c=E[0],E.payload!==void 0&&(l=E.payload)):c=null):(this.updateLastIndex(de,x),c=this.match(de,e,x)),c!==null){if(o=be.longerAlt,o!==void 0){const Ke=o.length;for(s=0;s<Ke;s++){const Ve=O[o[s]],ot=Ve.pattern;if(u=null,Ve.isCustom===!0?(E=ot.exec(T,x,B,A),E!==null?(a=E[0],E.payload!==void 0&&(u=E.payload)):a=null):(this.updateLastIndex(ot,x),a=this.match(ot,e,x)),a&&a.length>c.length){c=a,l=u,be=Ve;break}}}break}}if(c!==null){if(h=c.length,d=be.group,d!==void 0&&(f=be.tokenTypeIdx,p=this.createTokenInstance(c,x,f,be.tokenType,se,ge,h),this.handlePayload(p,l),d===!1?P=this.addToken(B,P,p):A[d].push(p)),e=this.chopInput(e,h),x+=h,ge=this.computeNewColumn(ge,h),R===!0&&be.canLineTerminator===!0){let de,Oe,Ke=0;k.lastIndex=0;do de=k.test(c),de===!0&&(Oe=k.lastIndex-1,Ke++);while(de===!0);Ke!==0&&(se+=Ke,ge=h-Oe,this.updateTokenEndLineColumnLocation(p,d,Oe,Ke,se,ge,h))}this.handleModes(be,Ml,zi,p)}else{const de=x,Oe=se,Ke=ge;let Ve=Yi===!1;for(;Ve===!1&&x<v;)for(e=this.chopInput(e,1),x++,i=0;i<S;i++){const ot=O[i],yr=ot.pattern,qi=ot.short;if(qi!==!1?T.charCodeAt(x)===qi&&(Ve=!0):ot.isCustom===!0?Ve=yr.exec(T,x,B,A)!==null:(this.updateLastIndex(yr,x),Ve=yr.exec(e)!==null),Ve===!0)break}if(m=x-de,ge=this.computeNewColumn(ge,m),y=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(T,de,m,Oe,Ke),Z.push({offset:de,line:Oe,column:Ke,length:m,message:y}),Yi===!1)break}}return this.hasCustom||(B.length=P),{tokens:B,groups:A,errors:Z}}handleModes(e,t,r,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&r.call(this,s)}else e.push!==void 0&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,i,s,a,o){let c,l;t!==void 0&&(c=r===o-1,l=c?-1:0,i===1&&c===!0||(e.endLine=s+l,e.endColumn=a-1-l))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,i){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:i}}createStartOnlyToken(e,t,r,i,s,a){return{image:e,startOffset:t,startLine:s,startColumn:a,tokenTypeIdx:r,tokenType:i}}createFullToken(e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:t+o-1,startLine:s,endLine:s,startColumn:a,endColumn:a+o-1,tokenTypeIdx:r,tokenType:i}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,++t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,r){return e.test(t)===!0?t.substring(r,e.lastIndex):null}matchWithExec(e,t){const r=e.exec(t);return r!==null?r[0]:null}}function St(n){return wc(n)?n.LABEL:n.name}function wc(n){return Te(n.LABEL)&&n.LABEL!==""}ye.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",ye.NA=/NOT_APPLICABLE/;const Bu="parent",Ts="categories",vs="label",Es="group",Rs="push_mode",As="pop_mode",ks="longer_alt",xs="line_breaks",Is="start_chars_hint";function Lc(n){return function(e){const t=e.pattern,r={};if(r.name=e.name,Xe(t)||(r.PATTERN=t),$(e,Bu))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return $(e,Ts)&&(r.CATEGORIES=e[Ts]),Tn([r]),$(e,vs)&&(r.LABEL=e[vs]),$(e,Es)&&(r.GROUP=e[Es]),$(e,As)&&(r.POP_MODE=e[As]),$(e,Rs)&&(r.PUSH_MODE=e[Rs]),$(e,ks)&&(r.LONGER_ALT=e[ks]),$(e,xs)&&(r.LINE_BREAKS=e[xs]),$(e,Is)&&(r.START_CHARS_HINT=e[Is]),r}(n)}const nt=Lc({name:"EOF",pattern:ye.NA});function bi(n,e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:n.tokenTypeIdx,tokenType:n}}function bc(n,e){return un(n,e)}Tn([nt]);const xt={buildMismatchTokenMessage:({expected:n,actual:e,previous:t,ruleName:r})=>`Expecting ${wc(n)?`--> ${St(n)} <--`:`token of type --> ${n.name} <--`} but found --> '${e.image}' <--`,buildNotAllInputParsedMessage:({firstRedundant:n,ruleName:e})=>"Redundant input, expecting EOF but found: "+n.image,buildNoViableAltMessage({expectedPathsPerAlt:n,actual:e,previous:t,customUserDescription:r,ruleName:i}){const s="Expecting: ",a=`
but found: '`+Me(e).image+"'";if(r)return s+r+a;{const o=pe(n,(l,u)=>l.concat(u),[]),c=I(o,l=>`[${I(l,u=>St(u)).join(", ")}]`);return s+`one of these possible Token sequences:
${I(c,(l,u)=>`  ${u+1}. ${l}`).join(`
`)}`+a}},buildEarlyExitMessage({expectedIterationPaths:n,actual:e,customUserDescription:t,ruleName:r}){const i="Expecting: ",s=`
but found: '`+Me(e).image+"'";return t?i+t+s:i+`expecting at least one iteration which starts with one of these possible Token sequences::
  <${I(n,a=>`[${I(a,o=>St(o)).join(",")}]`).join(" ,")}>`+s}};Object.freeze(xt);const ju={buildRuleNotFoundError:(n,e)=>"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+n.name+"<-"},ht={buildDuplicateFoundError(n,e){const t=n.name,r=Me(e),i=r.idx,s=Fe(r),a=(o=r)instanceof G?o.terminalType.name:o instanceof me?o.nonTerminalName:"";var o;let c=`->${s}${i>0?i:""}<- ${a?`with argument: ->${a}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${t}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError:n=>`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${n.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`,buildAlternationPrefixAmbiguityError(n){const e=I(n.prefixPath,r=>St(r)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;return`Ambiguous alternatives: <${n.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(n){const e=I(n.prefixPath,i=>St(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r+=`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r},buildEmptyRepetitionError(n){let e=Fe(n.repetition);return n.repetition.idx!==0&&(e+=n.repetition.idx),`The repetition <${e}> within Rule <${n.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError:n=>"deprecated",buildEmptyAlternationError:n=>`Ambiguous empty alternative: <${n.emptyChoiceIdx+1}> in <OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`,buildTooManyAlternativesError:n=>`An Alternation cannot have more than 256 alternatives:
<OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
 has ${n.alternation.definition.length+1} alternatives.`,buildLeftRecursionError(n){const e=n.topLevelRule.name;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${`${e} --> ${I(n.leftRecursionPath,t=>t.name).concat([e]).join(" --> ")}`}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError:n=>"deprecated",buildDuplicateRuleNameError(n){let e;return e=n.topLevelRule instanceof Yt?n.topLevelRule.name:n.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${n.grammarName}<-`}};class Ku extends qt{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){C(q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const r=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:r,type:he.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}class Vu extends hr{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=te(this.path.ruleStack).reverse(),this.occurrenceStack=te(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){F(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class Hu extends Vu{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(r),s=new ve({definition:i});this.possibleTokTypes=ln(s),this.found=!0}}}class fr extends hr{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class Wu extends fr{walkMany(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,r)}}class Ss extends fr{walkManySep(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,r)}}class zu extends fr{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,r)}}class Ns extends fr{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,r)}}function oi(n,e,t=[]){t=te(t);let r=[],i=0;function s(a){const o=oi(a.concat(ee(n,i+1)),e,t);return r.concat(o)}for(;t.length<e&&i<n.length;){const a=n[i];if(a instanceof ve||a instanceof me)return s(a.definition);if(a instanceof ie)r=s(a.definition);else{if(a instanceof Se)return s(a.definition.concat([new W({definition:a.definition})]));if(a instanceof Ne)return s([new ve({definition:a.definition}),new W({definition:[new G({terminalType:a.separator})].concat(a.definition)})]);if(a instanceof Ee){const o=a.definition.concat([new W({definition:[new G({terminalType:a.separator})].concat(a.definition)})]);r=s(o)}else if(a instanceof W){const o=a.definition.concat([new W({definition:a.definition})]);r=s(o)}else{if(a instanceof Re)return C(a.definition,o=>{F(o.definition)===!1&&(r=s(o.definition))}),r;if(!(a instanceof G))throw Error("non exhaustive match");t.push(a.terminalType)}}i++}return r.push({partialPath:t,suffixDef:ee(n,i)}),r}function Oc(n,e,t,r){const i="EXIT_NONE_TERMINAL",s=[i],a="EXIT_ALTERNATIVE";let o=!1;const c=e.length,l=c-r-1,u=[],h=[];for(h.push({idx:-1,def:n,ruleStack:[],occurrenceStack:[]});!F(h);){const d=h.pop();if(d===a){o&&Kt(h).idx<=l&&h.pop();continue}const f=d.def,p=d.idx,m=d.ruleStack,y=d.occurrenceStack;if(F(f))continue;const E=f[0];if(E===i){const T={idx:p,def:ee(f),ruleStack:hn(m),occurrenceStack:hn(y)};h.push(T)}else if(E instanceof G)if(p<c-1){const T=p+1;if(t(e[T],E.terminalType)){const v={idx:T,def:ee(f),ruleStack:m,occurrenceStack:y};h.push(v)}}else{if(p!==c-1)throw Error("non exhaustive match");u.push({nextTokenType:E.terminalType,nextTokenOccurrence:E.idx,ruleStack:m,occurrenceStack:y}),o=!0}else if(E instanceof me){const T=te(m);T.push(E.nonTerminalName);const v=te(y);v.push(E.idx);const x={idx:p,def:E.definition.concat(s,ee(f)),ruleStack:T,occurrenceStack:v};h.push(x)}else if(E instanceof ie){const T={idx:p,def:ee(f),ruleStack:m,occurrenceStack:y};h.push(T),h.push(a);const v={idx:p,def:E.definition.concat(ee(f)),ruleStack:m,occurrenceStack:y};h.push(v)}else if(E instanceof Se){const T=new W({definition:E.definition,idx:E.idx}),v={idx:p,def:E.definition.concat([T],ee(f)),ruleStack:m,occurrenceStack:y};h.push(v)}else if(E instanceof Ne){const T=new G({terminalType:E.separator}),v=new W({definition:[T].concat(E.definition),idx:E.idx}),x={idx:p,def:E.definition.concat([v],ee(f)),ruleStack:m,occurrenceStack:y};h.push(x)}else if(E instanceof Ee){const T={idx:p,def:ee(f),ruleStack:m,occurrenceStack:y};h.push(T),h.push(a);const v=new G({terminalType:E.separator}),x=new W({definition:[v].concat(E.definition),idx:E.idx}),P={idx:p,def:E.definition.concat([x],ee(f)),ruleStack:m,occurrenceStack:y};h.push(P)}else if(E instanceof W){const T={idx:p,def:ee(f),ruleStack:m,occurrenceStack:y};h.push(T),h.push(a);const v=new W({definition:E.definition,idx:E.idx}),x={idx:p,def:E.definition.concat([v],ee(f)),ruleStack:m,occurrenceStack:y};h.push(x)}else if(E instanceof Re)for(let T=E.definition.length-1;T>=0;T--){const v={idx:p,def:E.definition[T].definition.concat(ee(f)),ruleStack:m,occurrenceStack:y};h.push(v),h.push(a)}else if(E instanceof ve)h.push({idx:p,def:E.definition.concat(ee(f)),ruleStack:m,occurrenceStack:y});else{if(!(E instanceof Yt))throw Error("non exhaustive match");h.push(Yu(E,p,m,y))}}return u}function Yu(n,e,t,r){const i=te(t);i.push(n.name);const s=te(r);return s.push(1),{idx:e,def:n.definition,ruleStack:i,occurrenceStack:s}}var K;function ci(n){if(n instanceof ie||n==="Option")return K.OPTION;if(n instanceof W||n==="Repetition")return K.REPETITION;if(n instanceof Se||n==="RepetitionMandatory")return K.REPETITION_MANDATORY;if(n instanceof Ne||n==="RepetitionMandatoryWithSeparator")return K.REPETITION_MANDATORY_WITH_SEPARATOR;if(n instanceof Ee||n==="RepetitionWithSeparator")return K.REPETITION_WITH_SEPARATOR;if(n instanceof Re||n==="Alternation")return K.ALTERNATION;throw Error("non exhaustive match")}function Cs(n){const{occurrence:e,rule:t,prodType:r,maxLookahead:i}=n,s=ci(r);return s===K.ALTERNATION?pr(e,t,i):qn(e,t,s,i)}function qu(n,e,t,r){const i=n.length,s=_e(n,a=>_e(a,o=>o.length===1));if(e)return function(a){const o=I(a,c=>c.GATE);for(let c=0;c<i;c++){const l=n[c],u=l.length,h=o[c];if(h===void 0||h.call(this)!==!1)e:for(let d=0;d<u;d++){const f=l[d],p=f.length;for(let m=0;m<p;m++){const y=this.LA(m+1);if(t(y,f[m])===!1)continue e}return c}}};if(s&&!r){const a=I(n,c=>$e(c)),o=pe(a,(c,l,u)=>(C(l,h=>{$(c,h.tokenTypeIdx)||(c[h.tokenTypeIdx]=u),C(h.categoryMatches,d=>{$(c,d)||(c[d]=u)})}),c),{});return function(){const c=this.LA(1);return o[c.tokenTypeIdx]}}return function(){for(let a=0;a<i;a++){const o=n[a],c=o.length;e:for(let l=0;l<c;l++){const u=o[l],h=u.length;for(let d=0;d<h;d++){const f=this.LA(d+1);if(t(f,u[d])===!1)continue e}return a}}}}function Xu(n,e,t){const r=_e(n,s=>s.length===1),i=n.length;if(r&&!t){const s=$e(n);if(s.length===1&&F(s[0].categoryMatches)){const a=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===a}}{const a=pe(s,(o,c,l)=>(o[c.tokenTypeIdx]=!0,C(c.categoryMatches,u=>{o[u]=!0}),o),[]);return function(){const o=this.LA(1);return a[o.tokenTypeIdx]===!0}}}return function(){e:for(let s=0;s<i;s++){const a=n[s],o=a.length;for(let c=0;c<o;c++){const l=this.LA(c+1);if(e(l,a[c])===!1)continue e}return!0}return!1}}(function(n){n[n.OPTION=0]="OPTION",n[n.REPETITION=1]="REPETITION",n[n.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",n[n.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",n[n.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",n[n.ALTERNATION=5]="ALTERNATION"})(K||(K={}));class Qu extends hr{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,i){return e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.restDef=r.concat(i),!0)}walkOption(e,t,r){this.checkIsTarget(e,K.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,K.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,K.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,K.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,K.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class _c extends qt{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx!==this.targetOccurrence||this.targetProdType!==t||this.targetRef!==void 0&&e!==this.targetRef||(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,K.OPTION)}visitRepetition(e){this.checkIsTarget(e,K.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,K.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,K.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,K.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,K.ALTERNATION)}}function $s(n){const e=new Array(n);for(let t=0;t<n;t++)e[t]=[];return e}function Sr(n){let e=[""];for(let t=0;t<n.length;t++){const r=n[t],i=[];for(let s=0;s<e.length;s++){const a=e[s];i.push(a+"_"+r.tokenTypeIdx);for(let o=0;o<r.categoryMatches.length;o++){const c="_"+r.categoryMatches[o];i.push(a+c)}}e=i}return e}function Ju(n,e,t){for(let r=0;r<n.length;r++){if(r===t)continue;const i=n[r];for(let s=0;s<e.length;s++)if(i[e[s]]===!0)return!1}return!0}function Pc(n,e){const t=I(n,a=>oi([a],1)),r=$s(t.length),i=I(t,a=>{const o={};return C(a,c=>{const l=Sr(c.partialPath);C(l,u=>{o[u]=!0})}),o});let s=t;for(let a=1;a<=e;a++){const o=s;s=$s(o.length);for(let c=0;c<o.length;c++){const l=o[c];for(let u=0;u<l.length;u++){const h=l[u].partialPath,d=l[u].suffixDef,f=Sr(h);if(Ju(i,f,c)||F(d)||h.length===e){const p=r[c];if(li(p,h)===!1){p.push(h);for(let m=0;m<f.length;m++){const y=f[m];i[c][y]=!0}}}else{const p=oi(d,a+1,h);s[c]=s[c].concat(p),C(p,m=>{const y=Sr(m.partialPath);C(y,E=>{i[c][E]=!0})})}}}}return r}function pr(n,e,t,r){const i=new _c(n,K.ALTERNATION,r);return e.accept(i),Pc(i.result,t)}function qn(n,e,t,r){const i=new _c(n,t);e.accept(i);const s=i.result,a=new Qu(e,n,t).startWalking();return Pc([new ve({definition:s}),new ve({definition:a})],r)}function li(n,e){e:for(let t=0;t<n.length;t++){const r=n[t];if(r.length===e.length){for(let i=0;i<r.length;i++){const s=e[i],a=r[i];if(!(s===a||a.categoryMatchesMap[s.tokenTypeIdx]!==void 0))continue e}return!0}}return!1}function ws(n){return _e(n,e=>_e(e,t=>_e(t,r=>F(r.categoryMatches))))}function Zu(n,e,t,r){const i=xe(n,c=>function(l,u){const h=new td;l.accept(h);const d=h.allProductions,f=De(lu(d,ed),m=>m.length>1);return I(q(f),m=>{const y=Me(m),E=u.buildDuplicateFoundError(l,m),T=Fe(y),v={message:E,type:he.DUPLICATE_PRODUCTIONS,ruleName:l.name,dslName:T,occurrence:y.idx},x=Mc(y);return x&&(v.parameter=x),v})}(c,t)),s=function(c,l,u){const h=[],d=I(l,f=>f.name);return C(c,f=>{const p=f.name;if(fe(d,p)){const m=u.buildNamespaceConflictError(f);h.push({message:m,type:he.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:p})}}),h}(n,e,t),a=xe(n,c=>function(l,u){const h=new Oi;return l.accept(h),xe(h.alternations,f=>f.definition.length>255?[{message:u.buildTooManyAlternativesError({topLevelRule:l,alternation:f}),type:he.TOO_MANY_ALTS,ruleName:l.name,occurrence:f.idx}]:[])}(c,t)),o=xe(n,c=>function(l,u,h,d){const f=[];if(pe(u,(m,y)=>y.name===l.name?m+1:m,0)>1){const m=d.buildDuplicateRuleNameError({topLevelRule:l,grammarName:h});f.push({message:m,type:he.DUPLICATE_RULE_NAME,ruleName:l.name})}return f}(c,n,r,t));return i.concat(s,a,o)}function ed(n){return`${Fe(n)}_#_${n.idx}_#_${Mc(n)}`}function Mc(n){return n instanceof G?n.terminalType.name:n instanceof me?n.nonTerminalName:""}class td extends qt{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function Dc(n,e,t,r=[]){const i=[],s=Dn(e.definition);if(F(s))return[];{const a=n.name;fe(s,n)&&i.push({message:t.buildLeftRecursionError({topLevelRule:n,leftRecursionPath:r}),type:he.LEFT_RECURSION,ruleName:a});const o=xe(Fn(s,r.concat([n])),c=>{const l=te(r);return l.push(c),Dc(n,c,t,l)});return i.concat(o)}}function Dn(n){let e=[];if(F(n))return e;const t=Me(n);if(t instanceof me)e.push(t.referencedRule);else if(t instanceof ve||t instanceof ie||t instanceof Se||t instanceof Ne||t instanceof Ee||t instanceof W)e=e.concat(Dn(t.definition));else if(t instanceof Re)e=$e(I(t.definition,s=>Dn(s.definition)));else if(!(t instanceof G))throw Error("non exhaustive match");const r=Hn(t),i=n.length>1;if(r&&i){const s=ee(n);return e.concat(Dn(s))}return e}class Oi extends qt{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function nd(n,e,t){const r=new Oi;n.accept(r);let i=r.alternations;return i=ar(i,a=>a.ignoreAmbiguities===!0),xe(i,a=>{const o=a.idx,c=a.maxLookahead||e,l=pr(o,n,c,a),u=function(d,f,p,m){const y=[],E=pe(d,(v,x,P)=>(f.definition[P].ignoreAmbiguities===!0||C(x,U=>{const B=[P];C(d,(Z,se)=>{P!==se&&li(Z,U)&&f.definition[se].ignoreAmbiguities!==!0&&B.push(se)}),B.length>1&&!li(y,U)&&(y.push(U),v.push({alts:B,path:U}))}),v),[]);return I(E,v=>{const x=I(v.alts,P=>P+1);return{message:m.buildAlternationAmbiguityError({topLevelRule:p,alternation:f,ambiguityIndices:x,prefixPath:v.path}),type:he.AMBIGUOUS_ALTS,ruleName:p.name,occurrence:f.idx,alternatives:v.alts}})}(l,a,n,t),h=function(d,f,p,m){const y=pe(d,(T,v,x)=>{const P=I(v,U=>({idx:x,path:U}));return T.concat(P)},[]);return gn(xe(y,T=>{if(f.definition[T.idx].ignoreAmbiguities===!0)return[];const v=T.idx,x=T.path,P=ke(y,U=>{return f.definition[U.idx].ignoreAmbiguities!==!0&&U.idx<v&&(B=U.path,Z=x,B.length<Z.length&&_e(B,(se,ge)=>{const A=Z[ge];return se===A||A.categoryMatchesMap[se.tokenTypeIdx]}));var B,Z});return I(P,U=>{const B=[U.idx+1,v+1],Z=f.idx===0?"":f.idx;return{message:m.buildAlternationPrefixAmbiguityError({topLevelRule:p,alternation:f,ambiguityIndices:B,prefixPath:U.path}),type:he.AMBIGUOUS_PREFIX_ALTS,ruleName:p.name,occurrence:Z,alternatives:B}})}))}(l,a,n,t);return u.concat(h)})}class rd extends qt{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function id(n){const e=xi(n,{errMsgProvider:ju}),t={};return C(n.rules,r=>{t[r.name]=r}),function(r,i){const s=new Ku(r,i);return s.resolveRefs(),s.errors}(t,e.errMsgProvider)}const Uc="MismatchedTokenException",Fc="NoViableAltException",Gc="EarlyExitException",Bc="NotAllInputParsedException",jc=[Uc,Fc,Gc,Bc];function kn(n){return fe(jc,n.name)}Object.freeze(jc);class mr extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class Ls extends mr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Uc}}class sd extends mr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Fc}}class ad extends mr{constructor(e,t){super(e,t),this.name=Bc}}class od extends mr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Gc}}const Nr={},Kc="InRuleRecoveryException";class cd extends Error{constructor(e){super(e),this.name=Kc}}function ld(n,e,t,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let c=this.firstAfterRepMap[o];if(c===void 0){const d=this.getCurrRuleFullName();c=new s(this.getGAstProductions()[d],i).startWalking(),this.firstAfterRepMap[o]=c}let l=c.token,u=c.occurrence;const h=c.isEndOfRule;this.RULE_STACK.length===1&&h&&l===void 0&&(l=nt,u=1),l!==void 0&&u!==void 0&&this.shouldInRepetitionRecoveryBeTried(l,u,a)&&this.tryInRepetitionRecovery(n,e,t,l)}const Cr=1024,$r=1280,xn=1536;function wr(n,e,t){return t|e|n}class _i{constructor(e){var t;this.maxLookahead=(t=e==null?void 0:e.maxLookahead)!==null&&t!==void 0?t:ze.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(F(t)){const r=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...r,...i,...s]}return t}validateNoLeftRecursion(e){return xe(e,t=>Dc(t,t,ht))}validateEmptyOrAlternatives(e){return xe(e,t=>function(r,i){const s=new Oi;return r.accept(s),xe(s.alternations,a=>xe(hn(a.definition),(o,c)=>{const l=Oc([o],[],un,1);return F(l)?[{message:i.buildEmptyAlternationError({topLevelRule:r,alternation:a,emptyChoiceIdx:c}),type:he.NONE_LAST_EMPTY_ALT,ruleName:r.name,occurrence:a.idx,alternative:c+1}]:[]}))}(t,ht))}validateAmbiguousAlternationAlternatives(e,t){return xe(e,r=>nd(r,t,ht))}validateSomeNonEmptyLookaheadPath(e,t){return function(r,i,s){const a=[];return C(r,o=>{const c=new rd;o.accept(c);const l=c.allProductions;C(l,u=>{const h=ci(u),d=u.maxLookahead||i,f=qn(u.idx,o,h,d)[0];if(F($e(f))){const p=s.buildEmptyRepetitionError({topLevelRule:o,repetition:u});a.push({message:p,type:he.NO_NON_EMPTY_LOOKAHEAD,ruleName:o.name})}})}),a}(e,t,ht)}buildLookaheadForAlternation(e){return function(t,r,i,s,a,o){const c=pr(t,r,i);return o(c,s,ws(c)?Yn:un,a)}(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,qu)}buildLookaheadForOptional(e){return function(t,r,i,s,a,o){const c=qn(t,r,a,i),l=ws(c)?Yn:un;return o(c[0],l,s)}(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,ci(e.prodType),Xu)}}const In=new class extends qt{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(n){this.dslMethods.option.push(n)}visitRepetitionWithSeparator(n){this.dslMethods.repetitionWithSeparator.push(n)}visitRepetitionMandatory(n){this.dslMethods.repetitionMandatory.push(n)}visitRepetitionMandatoryWithSeparator(n){this.dslMethods.repetitionMandatoryWithSeparator.push(n)}visitRepetition(n){this.dslMethods.repetition.push(n)}visitAlternation(n){this.dslMethods.alternation.push(n)}};function bs(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.endOffset=e.endOffset):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset)}function Os(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.startColumn=e.startColumn,n.startLine=e.startLine,n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine)}const ud="name";function Vc(n,e){Object.defineProperty(n,ud,{enumerable:!1,configurable:!0,writable:!1,value:e})}function dd(n,e){const t=Bt(n),r=t.length;for(let i=0;i<r;i++){const s=n[t[i]],a=s.length;for(let o=0;o<a;o++){const c=s[o];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}function hd(n,e){const t=function(){};Vc(t,n+"BaseSemantics");const r={visit:function(i,s){if(re(i)&&(i=i[0]),!Xe(i))return this[i.name](i.children,s)},validateVisitor:function(){const i=function(s,a){return function(c,l){const u=ke(l,d=>ft(c[d])===!1),h=I(u,d=>({msg:`Missing visitor method: <${d}> on ${c.constructor.name} CST Visitor.`,type:ui.MISSING_METHOD,methodName:d}));return gn(h)}(s,a)}(this,e);if(!F(i)){const s=I(i,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}}};return(t.prototype=r).constructor=t,t._RULE_NAMES=e,t}var ui;(function(n){n[n.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",n[n.MISSING_METHOD=1]="MISSING_METHOD"})(ui||(ui={}));const gr={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(gr);const _s=!0,Ps=Math.pow(2,8)-1,Hc=Lc({name:"RECORDING_PHASE_TOKEN",pattern:ye.NA});Tn([Hc]);const Wc=bi(Hc,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(Wc);const fd={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};function rn(n,e,t,r=!1){Xn(t);const i=Kt(this.recordingProdStack),s=ft(e)?e:e.DEF,a=new n({definition:[],idx:t});return r&&(a.separator=e.SEP),$(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),gr}function pd(n,e){Xn(e);const t=Kt(this.recordingProdStack),r=re(n)===!1,i=r===!1?n:n.DEF,s=new Re({definition:[],idx:e,ignoreAmbiguities:r&&n.IGNORE_AMBIGUITIES===!0});$(n,"MAX_LOOKAHEAD")&&(s.maxLookahead=n.MAX_LOOKAHEAD);const a=Io(i,o=>ft(o.GATE));return s.hasPredicates=a,t.definition.push(s),C(i,o=>{const c=new ve({definition:[]});s.definition.push(c),$(o,"IGNORE_AMBIGUITIES")?c.ignoreAmbiguities=o.IGNORE_AMBIGUITIES:$(o,"GATE")&&(c.ignoreAmbiguities=!0),this.recordingProdStack.push(c),o.ALT.call(this),this.recordingProdStack.pop()}),gr}function Ms(n){return n===0?"":`${n}`}function Xn(n){if(n<0||n>Ps){const e=new Error(`Invalid DSL Method idx value: <${n}>
	Idx value must be a none negative value smaller than ${Ps+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}const Un=bi(nt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Un);const ze=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:xt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Sn=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var he,Lr,Ds;function Us(n=void 0){return function(){return n}}(function(n){n[n.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",n[n.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",n[n.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",n[n.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",n[n.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",n[n.LEFT_RECURSION=5]="LEFT_RECURSION",n[n.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",n[n.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",n[n.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",n[n.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",n[n.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",n[n.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",n[n.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",n[n.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(he||(he={}));class fn{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{Rc(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),C(this.definedRulesNames,i=>{const s=this[i].originalGrammarAction;let a;this.TRACE_INIT(`${i} Rule`,()=>{a=this.topLevelRuleRecord(i,s)}),this.gastProductionsCache[i]=a})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=id({rules:q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if(F(r)&&this.skipValidations===!1){const s=(i={rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),errMsgProvider:ht,grammarName:t},Zu((i=xi(i,{errMsgProvider:ht})).rules,i.tokenTypes,i.errMsgProvider,i.grammarName)),a=function(o){const c=o.lookaheadStrategy.validate({rules:o.rules,tokenTypes:o.tokenTypes,grammarName:o.grammarName});return I(c,l=>Object.assign({type:he.CUSTOM_LOOKAHEAD_VALIDATION},l))}({lookaheadStrategy:this.lookaheadStrategy,rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(s,a)}var i}),F(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=function(s){const a={};return C(s,o=>{const c=new Cu(o).startWalking();Ie(a,c)}),a}(q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(q(this.gastProductionsCache))})),!fn.DEFER_DEFINITION_ERRORS_HANDLING&&!F(this.definitionErrors))throw e=I(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const r=this;if(r.initErrorHandler(t),r.initLexerAdapter(),r.initLooksAhead(t),r.initRecognizerEngine(e,t),r.initRecoverable(t),r.initTreeBuilder(t),r.initContentAssist(),r.initGastRecorder(t),r.initPerformanceTracer(t),$(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=$(t,"skipValidations")?t.skipValidations:ze.skipValidations}}fn.DEFER_DEFINITION_ERRORS_HANDLING=!1,Lr=fn,Ds=[class{initRecoverable(n){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=$(n,"recoveryEnabled")?n.recoveryEnabled:ze.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=ld)}getTokenToInsert(n){const e=bi(n,"",NaN,NaN,NaN,NaN,NaN,NaN);return e.isInsertedInRecovery=!0,e}canTokenTypeBeInsertedInRecovery(n){return!0}canTokenTypeBeDeletedInRecovery(n){return!0}tryInRepetitionRecovery(n,e,t,r){const i=this.findReSyncTokenType(),s=this.exportLexerState(),a=[];let o=!1;const c=this.LA(1);let l=this.LA(1);const u=()=>{const h=this.LA(0),d=this.errorMessageProvider.buildMismatchTokenMessage({expected:r,actual:c,previous:h,ruleName:this.getCurrRuleFullName()}),f=new Ls(d,c,this.LA(0));f.resyncedTokens=hn(a),this.SAVE_ERROR(f)};for(;!o;){if(this.tokenMatcher(l,r))return void u();if(t.call(this))return u(),void n.apply(this,e);this.tokenMatcher(l,i)?o=!0:(l=this.SKIP_TOKEN(),this.addToResyncTokens(l,a))}this.importLexerState(s)}shouldInRepetitionRecoveryBeTried(n,e,t){return t!==!1&&!this.tokenMatcher(this.LA(1),n)&&!this.isBackTracking()&&!this.canPerformInRuleRecovery(n,this.getFollowsForInRuleRecovery(n,e))}getFollowsForInRuleRecovery(n,e){const t=this.getCurrentGrammarPath(n,e);return this.getNextPossibleTokenTypes(t)}tryInRuleRecovery(n,e){if(this.canRecoverWithSingleTokenInsertion(n,e))return this.getTokenToInsert(n);if(this.canRecoverWithSingleTokenDeletion(n)){const t=this.SKIP_TOKEN();return this.consumeToken(),t}throw new cd("sad sad panda")}canPerformInRuleRecovery(n,e){return this.canRecoverWithSingleTokenInsertion(n,e)||this.canRecoverWithSingleTokenDeletion(n)}canRecoverWithSingleTokenInsertion(n,e){if(!this.canTokenTypeBeInsertedInRecovery(n)||F(e))return!1;const t=this.LA(1);return jt(e,r=>this.tokenMatcher(t,r))!==void 0}canRecoverWithSingleTokenDeletion(n){return!!this.canTokenTypeBeDeletedInRecovery(n)&&this.tokenMatcher(this.LA(2),n)}isInCurrentRuleReSyncSet(n){const e=this.getCurrFollowKey();return fe(this.getFollowSetFromFollowKey(e),n)}findReSyncTokenType(){const n=this.flattenFollowSet();let e=this.LA(1),t=2;for(;;){const r=jt(n,i=>bc(e,i));if(r!==void 0)return r;e=this.LA(t),t++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return Nr;const n=this.getLastExplicitRuleShortName(),e=this.getLastExplicitRuleOccurrenceIndex(),t=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:e,inRule:this.shortRuleNameToFullName(t)}}buildFullFollowKeyStack(){const n=this.RULE_STACK,e=this.RULE_OCCURRENCE_STACK;return I(n,(t,r)=>r===0?Nr:{ruleName:this.shortRuleNameToFullName(t),idxInCallingRule:e[r],inRule:this.shortRuleNameToFullName(n[r-1])})}flattenFollowSet(){const n=I(this.buildFullFollowKeyStack(),e=>this.getFollowSetFromFollowKey(e));return $e(n)}getFollowSetFromFollowKey(n){if(n===Nr)return[nt];const e=n.ruleName+n.idxInCallingRule+Ac+n.inRule;return this.resyncFollows[e]}addToResyncTokens(n,e){return this.tokenMatcher(n,nt)||e.push(n),e}reSyncTo(n){const e=[];let t=this.LA(1);for(;this.tokenMatcher(t,n)===!1;)t=this.SKIP_TOKEN(),this.addToResyncTokens(t,e);return hn(e)}attemptInRepetitionRecovery(n,e,t,r,i,s,a){}getCurrentGrammarPath(n,e){return{ruleStack:this.getHumanReadableRuleStack(),occurrenceStack:te(this.RULE_OCCURRENCE_STACK),lastTok:n,lastTokOccurrence:e}}getHumanReadableRuleStack(){return I(this.RULE_STACK,n=>this.shortRuleNameToFullName(n))}},class{initLooksAhead(n){this.dynamicTokensEnabled=$(n,"dynamicTokensEnabled")?n.dynamicTokensEnabled:ze.dynamicTokensEnabled,this.maxLookahead=$(n,"maxLookahead")?n.maxLookahead:ze.maxLookahead,this.lookaheadStrategy=$(n,"lookaheadStrategy")?n.lookaheadStrategy:new _i({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(n){C(n,e=>{this.TRACE_INIT(`${e.name} Rule Lookahead`,()=>{const{alternation:t,repetition:r,option:i,repetitionMandatory:s,repetitionMandatoryWithSeparator:a,repetitionWithSeparator:o}=function(c){In.reset(),c.accept(In);const l=In.dslMethods;return In.reset(),l}(e);C(t,c=>{const l=c.idx===0?"":c.idx;this.TRACE_INIT(`${Fe(c)}${l}`,()=>{const u=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:e,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=wr(this.fullRuleNameToShort[e.name],256,c.idx);this.setLaFuncCache(h,u)})}),C(r,c=>{this.computeLookaheadFunc(e,c.idx,768,"Repetition",c.maxLookahead,Fe(c))}),C(i,c=>{this.computeLookaheadFunc(e,c.idx,512,"Option",c.maxLookahead,Fe(c))}),C(s,c=>{this.computeLookaheadFunc(e,c.idx,Cr,"RepetitionMandatory",c.maxLookahead,Fe(c))}),C(a,c=>{this.computeLookaheadFunc(e,c.idx,xn,"RepetitionMandatoryWithSeparator",c.maxLookahead,Fe(c))}),C(o,c=>{this.computeLookaheadFunc(e,c.idx,$r,"RepetitionWithSeparator",c.maxLookahead,Fe(c))})})})}computeLookaheadFunc(n,e,t,r,i,s){this.TRACE_INIT(`${s}${e===0?"":e}`,()=>{const a=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:e,rule:n,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:r}),o=wr(this.fullRuleNameToShort[n.name],t,e);this.setLaFuncCache(o,a)})}getKeyForAutomaticLookahead(n,e){return wr(this.getLastExplicitRuleShortName(),n,e)}getLaFuncFromCache(n){return this.lookAheadFuncsCache.get(n)}setLaFuncCache(n,e){this.lookAheadFuncsCache.set(n,e)}},class{initTreeBuilder(n){if(this.CST_STACK=[],this.outputCst=n.outputCst,this.nodeLocationTracking=$(n,"nodeLocationTracking")?n.nodeLocationTracking:ze.nodeLocationTracking,this.outputCst)if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Os,this.setNodeLocationFromNode=Os,this.cstPostRule=X,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=bs,this.setNodeLocationFromNode=bs,this.cstPostRule=X,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else{if(!/none/i.test(this.nodeLocationTracking))throw Error(`Invalid <nodeLocationTracking> config option: "${n.nodeLocationTracking}"`);this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=X,this.setInitialNodeLocation=X}else this.cstInvocationStateUpdate=X,this.cstFinallyStateUpdate=X,this.cstPostTerminal=X,this.cstPostNonTerminal=X,this.cstPostRule=X}setInitialNodeLocationOnlyOffsetRecovery(n){n.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(n){n.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(n){n.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(n){const e=this.LA(1);n.location={startOffset:e.startOffset,startLine:e.startLine,startColumn:e.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(n){const e={name:n,children:Object.create(null)};this.setInitialNodeLocation(e),this.CST_STACK.push(e)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(n){const e=this.LA(0),t=n.location;t.startOffset<=e.startOffset==1?(t.endOffset=e.endOffset,t.endLine=e.endLine,t.endColumn=e.endColumn):(t.startOffset=NaN,t.startLine=NaN,t.startColumn=NaN)}cstPostRuleOnlyOffset(n){const e=this.LA(0),t=n.location;t.startOffset<=e.startOffset==1?t.endOffset=e.endOffset:t.startOffset=NaN}cstPostTerminal(n,e){const t=this.CST_STACK[this.CST_STACK.length-1];var r,i,s;i=e,s=n,(r=t).children[s]===void 0?r.children[s]=[i]:r.children[s].push(i),this.setNodeLocationFromToken(t.location,e)}cstPostNonTerminal(n,e){const t=this.CST_STACK[this.CST_STACK.length-1];(function(r,i,s){r.children[i]===void 0?r.children[i]=[s]:r.children[i].push(s)})(t,e,n),this.setNodeLocationFromNode(t.location,n.location)}getBaseCstVisitorConstructor(){if(Xe(this.baseCstVisitorConstructor)){const n=hd(this.className,Bt(this.gastProductionsCache));return this.baseCstVisitorConstructor=n,n}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Xe(this.baseCstVisitorWithDefaultsConstructor)){const n=function(e,t,r){const i=function(){};Vc(i,e+"BaseSemanticsWithDefaults");const s=Object.create(r.prototype);return C(t,a=>{s[a]=dd}),(i.prototype=s).constructor=i,i}(this.className,Bt(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=n,n}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const n=this.RULE_STACK;return n[n.length-1]}getPreviousExplicitRuleShortName(){const n=this.RULE_STACK;return n[n.length-2]}getLastExplicitRuleOccurrenceIndex(){const n=this.RULE_OCCURRENCE_STACK;return n[n.length-1]}},class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(n){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=n,this.tokVectorLength=n.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Un}LA(n){const e=this.currIdx+n;return e<0||this.tokVectorLength<=e?Un:this.tokVector[e]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(n){this.currIdx=n}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}},class{initRecognizerEngine(n,e){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Yn,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},$(e,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(re(n)){if(F(n))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof n[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(re(n))this.tokensMap=pe(n,(r,i)=>(r[i.name]=i,r),{});else if($(n,"modes")&&_e($e(q(n.modes)),Fu)){const r=Jr($e(q(n.modes)));this.tokensMap=pe(r,(i,s)=>(i[s.name]=s,i),{})}else{if(!Wl(n))throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap=te(n)}this.tokensMap.EOF=nt;const t=_e($(n,"modes")?$e(q(n.modes)):q(n),r=>F(r.categoryMatches));this.tokenMatcher=t?Yn:un,Tn(q(this.tokensMap))}defineRule(n,e,t){if(this.selfAnalysisDone)throw Error(`Grammar rule <${n}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const r=$(t,"resyncEnabled")?t.resyncEnabled:Sn.resyncEnabled,i=$(t,"recoveryValueFunc")?t.recoveryValueFunc:Sn.recoveryValueFunc,s=this.ruleShortNameIdx<<12;let a;return this.ruleShortNameIdx++,this.shortRuleNameToFull[s]=n,this.fullRuleNameToShort[n]=s,a=this.outputCst===!0?function(...o){try{this.ruleInvocationStateUpdate(s,n,this.subruleIdx),e.apply(this,o);const c=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(c),c}catch(c){return this.invokeRuleCatch(c,r,i)}finally{this.ruleFinallyStateUpdate()}}:function(...o){try{return this.ruleInvocationStateUpdate(s,n,this.subruleIdx),e.apply(this,o)}catch(c){return this.invokeRuleCatch(c,r,i)}finally{this.ruleFinallyStateUpdate()}},Object.assign(a,{ruleName:n,originalGrammarAction:e})}invokeRuleCatch(n,e,t){const r=this.RULE_STACK.length===1,i=e&&!this.isBackTracking()&&this.recoveryEnabled;if(kn(n)){const s=n;if(i){const a=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(a)){if(s.resyncedTokens=this.reSyncTo(a),this.outputCst){const o=this.CST_STACK[this.CST_STACK.length-1];return o.recoveredNode=!0,o}return t(n)}if(this.outputCst){const o=this.CST_STACK[this.CST_STACK.length-1];o.recoveredNode=!0,s.partialCstResult=o}throw s}if(r)return this.moveToTerminatedState(),t(n);throw s}throw n}optionInternal(n,e){const t=this.getKeyForAutomaticLookahead(512,e);return this.optionInternalLogic(n,e,t)}optionInternalLogic(n,e,t){let r,i=this.getLaFuncFromCache(t);if(typeof n!="function"){r=n.DEF;const s=n.GATE;if(s!==void 0){const a=i;i=()=>s.call(this)&&a.call(this)}}else r=n;if(i.call(this)===!0)return r.call(this)}atLeastOneInternal(n,e){const t=this.getKeyForAutomaticLookahead(Cr,n);return this.atLeastOneInternalLogic(n,e,t)}atLeastOneInternalLogic(n,e,t){let r,i=this.getLaFuncFromCache(t);if(typeof e!="function"){r=e.DEF;const s=e.GATE;if(s!==void 0){const a=i;i=()=>s.call(this)&&a.call(this)}}else r=e;if(i.call(this)!==!0)throw this.raiseEarlyExitException(n,K.REPETITION_MANDATORY,e.ERR_MSG);{let s=this.doSingleRepetition(r);for(;i.call(this)===!0&&s===!0;)s=this.doSingleRepetition(r)}this.attemptInRepetitionRecovery(this.atLeastOneInternal,[n,e],i,Cr,n,zu)}atLeastOneSepFirstInternal(n,e){const t=this.getKeyForAutomaticLookahead(xn,n);this.atLeastOneSepFirstInternalLogic(n,e,t)}atLeastOneSepFirstInternalLogic(n,e,t){const r=e.DEF,i=e.SEP;if(this.getLaFuncFromCache(t).call(this)!==!0)throw this.raiseEarlyExitException(n,K.REPETITION_MANDATORY_WITH_SEPARATOR,e.ERR_MSG);{r.call(this);const s=()=>this.tokenMatcher(this.LA(1),i);for(;this.tokenMatcher(this.LA(1),i)===!0;)this.CONSUME(i),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[n,i,s,r,Ns],s,xn,n,Ns)}}manyInternal(n,e){const t=this.getKeyForAutomaticLookahead(768,n);return this.manyInternalLogic(n,e,t)}manyInternalLogic(n,e,t){let r,i=this.getLaFuncFromCache(t);if(typeof e!="function"){r=e.DEF;const a=e.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else r=e;let s=!0;for(;i.call(this)===!0&&s===!0;)s=this.doSingleRepetition(r);this.attemptInRepetitionRecovery(this.manyInternal,[n,e],i,768,n,Wu,s)}manySepFirstInternal(n,e){const t=this.getKeyForAutomaticLookahead($r,n);this.manySepFirstInternalLogic(n,e,t)}manySepFirstInternalLogic(n,e,t){const r=e.DEF,i=e.SEP;if(this.getLaFuncFromCache(t).call(this)===!0){r.call(this);const s=()=>this.tokenMatcher(this.LA(1),i);for(;this.tokenMatcher(this.LA(1),i)===!0;)this.CONSUME(i),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[n,i,s,r,Ss],s,$r,n,Ss)}}repetitionSepSecondInternal(n,e,t,r,i){for(;t();)this.CONSUME(e),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[n,e,t,r,i],t,xn,n,i)}doSingleRepetition(n){const e=this.getLexerPosition();return n.call(this),this.getLexerPosition()>e}orInternal(n,e){const t=this.getKeyForAutomaticLookahead(256,e),r=re(n)?n:n.DEF,i=this.getLaFuncFromCache(t).call(this,r);if(i!==void 0)return r[i].ALT.call(this);this.raiseNoAltException(e,n.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const n=this.LA(1),e=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:n,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new ad(e,n))}}subruleInternal(n,e,t){let r;try{const i=t!==void 0?t.ARGS:void 0;return this.subruleIdx=e,r=n.apply(this,i),this.cstPostNonTerminal(r,t!==void 0&&t.LABEL!==void 0?t.LABEL:n.ruleName),r}catch(i){throw this.subruleInternalError(i,t,n.ruleName)}}subruleInternalError(n,e,t){throw kn(n)&&n.partialCstResult!==void 0&&(this.cstPostNonTerminal(n.partialCstResult,e!==void 0&&e.LABEL!==void 0?e.LABEL:t),delete n.partialCstResult),n}consumeInternal(n,e,t){let r;try{const i=this.LA(1);this.tokenMatcher(i,n)===!0?(this.consumeToken(),r=i):this.consumeInternalError(n,i,t)}catch(i){r=this.consumeInternalRecovery(n,e,i)}return this.cstPostTerminal(t!==void 0&&t.LABEL!==void 0?t.LABEL:n.name,r),r}consumeInternalError(n,e,t){let r;const i=this.LA(0);throw r=t!==void 0&&t.ERR_MSG?t.ERR_MSG:this.errorMessageProvider.buildMismatchTokenMessage({expected:n,actual:e,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Ls(r,e,i))}consumeInternalRecovery(n,e,t){if(!this.recoveryEnabled||t.name!=="MismatchedTokenException"||this.isBackTracking())throw t;{const r=this.getFollowsForInRuleRecovery(n,e);try{return this.tryInRuleRecovery(n,r)}catch(i){throw i.name===Kc?t:i}}}saveRecogState(){const n=this.errors,e=te(this.RULE_STACK);return{errors:n,lexerState:this.exportLexerState(),RULE_STACK:e,CST_STACK:this.CST_STACK}}reloadRecogState(n){this.errors=n.errors,this.importLexerState(n.lexerState),this.RULE_STACK=n.RULE_STACK}ruleInvocationStateUpdate(n,e,t){this.RULE_OCCURRENCE_STACK.push(t),this.RULE_STACK.push(n),this.cstInvocationStateUpdate(e)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const n=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[n]}shortRuleNameToFullName(n){return this.shortRuleNameToFull[n]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),nt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}},class{ACTION(n){return n.call(this)}consume(n,e,t){return this.consumeInternal(e,n,t)}subrule(n,e,t){return this.subruleInternal(e,n,t)}option(n,e){return this.optionInternal(e,n)}or(n,e){return this.orInternal(e,n)}many(n,e){return this.manyInternal(n,e)}atLeastOne(n,e){return this.atLeastOneInternal(n,e)}CONSUME(n,e){return this.consumeInternal(n,0,e)}CONSUME1(n,e){return this.consumeInternal(n,1,e)}CONSUME2(n,e){return this.consumeInternal(n,2,e)}CONSUME3(n,e){return this.consumeInternal(n,3,e)}CONSUME4(n,e){return this.consumeInternal(n,4,e)}CONSUME5(n,e){return this.consumeInternal(n,5,e)}CONSUME6(n,e){return this.consumeInternal(n,6,e)}CONSUME7(n,e){return this.consumeInternal(n,7,e)}CONSUME8(n,e){return this.consumeInternal(n,8,e)}CONSUME9(n,e){return this.consumeInternal(n,9,e)}SUBRULE(n,e){return this.subruleInternal(n,0,e)}SUBRULE1(n,e){return this.subruleInternal(n,1,e)}SUBRULE2(n,e){return this.subruleInternal(n,2,e)}SUBRULE3(n,e){return this.subruleInternal(n,3,e)}SUBRULE4(n,e){return this.subruleInternal(n,4,e)}SUBRULE5(n,e){return this.subruleInternal(n,5,e)}SUBRULE6(n,e){return this.subruleInternal(n,6,e)}SUBRULE7(n,e){return this.subruleInternal(n,7,e)}SUBRULE8(n,e){return this.subruleInternal(n,8,e)}SUBRULE9(n,e){return this.subruleInternal(n,9,e)}OPTION(n){return this.optionInternal(n,0)}OPTION1(n){return this.optionInternal(n,1)}OPTION2(n){return this.optionInternal(n,2)}OPTION3(n){return this.optionInternal(n,3)}OPTION4(n){return this.optionInternal(n,4)}OPTION5(n){return this.optionInternal(n,5)}OPTION6(n){return this.optionInternal(n,6)}OPTION7(n){return this.optionInternal(n,7)}OPTION8(n){return this.optionInternal(n,8)}OPTION9(n){return this.optionInternal(n,9)}OR(n){return this.orInternal(n,0)}OR1(n){return this.orInternal(n,1)}OR2(n){return this.orInternal(n,2)}OR3(n){return this.orInternal(n,3)}OR4(n){return this.orInternal(n,4)}OR5(n){return this.orInternal(n,5)}OR6(n){return this.orInternal(n,6)}OR7(n){return this.orInternal(n,7)}OR8(n){return this.orInternal(n,8)}OR9(n){return this.orInternal(n,9)}MANY(n){this.manyInternal(0,n)}MANY1(n){this.manyInternal(1,n)}MANY2(n){this.manyInternal(2,n)}MANY3(n){this.manyInternal(3,n)}MANY4(n){this.manyInternal(4,n)}MANY5(n){this.manyInternal(5,n)}MANY6(n){this.manyInternal(6,n)}MANY7(n){this.manyInternal(7,n)}MANY8(n){this.manyInternal(8,n)}MANY9(n){this.manyInternal(9,n)}MANY_SEP(n){this.manySepFirstInternal(0,n)}MANY_SEP1(n){this.manySepFirstInternal(1,n)}MANY_SEP2(n){this.manySepFirstInternal(2,n)}MANY_SEP3(n){this.manySepFirstInternal(3,n)}MANY_SEP4(n){this.manySepFirstInternal(4,n)}MANY_SEP5(n){this.manySepFirstInternal(5,n)}MANY_SEP6(n){this.manySepFirstInternal(6,n)}MANY_SEP7(n){this.manySepFirstInternal(7,n)}MANY_SEP8(n){this.manySepFirstInternal(8,n)}MANY_SEP9(n){this.manySepFirstInternal(9,n)}AT_LEAST_ONE(n){this.atLeastOneInternal(0,n)}AT_LEAST_ONE1(n){return this.atLeastOneInternal(1,n)}AT_LEAST_ONE2(n){this.atLeastOneInternal(2,n)}AT_LEAST_ONE3(n){this.atLeastOneInternal(3,n)}AT_LEAST_ONE4(n){this.atLeastOneInternal(4,n)}AT_LEAST_ONE5(n){this.atLeastOneInternal(5,n)}AT_LEAST_ONE6(n){this.atLeastOneInternal(6,n)}AT_LEAST_ONE7(n){this.atLeastOneInternal(7,n)}AT_LEAST_ONE8(n){this.atLeastOneInternal(8,n)}AT_LEAST_ONE9(n){this.atLeastOneInternal(9,n)}AT_LEAST_ONE_SEP(n){this.atLeastOneSepFirstInternal(0,n)}AT_LEAST_ONE_SEP1(n){this.atLeastOneSepFirstInternal(1,n)}AT_LEAST_ONE_SEP2(n){this.atLeastOneSepFirstInternal(2,n)}AT_LEAST_ONE_SEP3(n){this.atLeastOneSepFirstInternal(3,n)}AT_LEAST_ONE_SEP4(n){this.atLeastOneSepFirstInternal(4,n)}AT_LEAST_ONE_SEP5(n){this.atLeastOneSepFirstInternal(5,n)}AT_LEAST_ONE_SEP6(n){this.atLeastOneSepFirstInternal(6,n)}AT_LEAST_ONE_SEP7(n){this.atLeastOneSepFirstInternal(7,n)}AT_LEAST_ONE_SEP8(n){this.atLeastOneSepFirstInternal(8,n)}AT_LEAST_ONE_SEP9(n){this.atLeastOneSepFirstInternal(9,n)}RULE(n,e,t=Sn){if(fe(this.definedRulesNames,n)){const i={message:ht.buildDuplicateRuleNameError({topLevelRule:n,grammarName:this.className}),type:he.DUPLICATE_RULE_NAME,ruleName:n};this.definitionErrors.push(i)}this.definedRulesNames.push(n);const r=this.defineRule(n,e,t);return this[n]=r,r}OVERRIDE_RULE(n,e,t=Sn){const r=function(s,a,o){const c=[];let l;return fe(a,s)||(l=`Invalid rule override, rule: ->${s}<- cannot be overridden in the grammar: ->${o}<-as it is not defined in any of the super grammars `,c.push({message:l,type:he.INVALID_RULE_OVERRIDE,ruleName:s})),c}(n,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(r);const i=this.defineRule(n,e,t);return this[n]=i,i}BACKTRACK(n,e){return function(){this.isBackTrackingStack.push(1);const t=this.saveRecogState();try{return n.apply(this,e),!0}catch(r){if(kn(r))return!1;throw r}finally{this.reloadRecogState(t),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return n=q(this.gastProductionsCache),I(n,_n);var n}},class{initErrorHandler(n){this._errors=[],this.errorMessageProvider=$(n,"errorMessageProvider")?n.errorMessageProvider:ze.errorMessageProvider}SAVE_ERROR(n){if(kn(n))return n.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:te(this.RULE_OCCURRENCE_STACK)},this._errors.push(n),n;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return te(this._errors)}set errors(n){this._errors=n}raiseEarlyExitException(n,e,t){const r=this.getCurrRuleFullName(),i=qn(n,this.getGAstProductions()[r],e,this.maxLookahead)[0],s=[];for(let o=1;o<=this.maxLookahead;o++)s.push(this.LA(o));const a=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:i,actual:s,previous:this.LA(0),customUserDescription:t,ruleName:r});throw this.SAVE_ERROR(new od(a,this.LA(1),this.LA(0)))}raiseNoAltException(n,e){const t=this.getCurrRuleFullName(),r=pr(n,this.getGAstProductions()[t],this.maxLookahead),i=[];for(let o=1;o<=this.maxLookahead;o++)i.push(this.LA(o));const s=this.LA(0),a=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:r,actual:i,previous:s,customUserDescription:e,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new sd(a,this.LA(1),s))}},class{initContentAssist(){}computeContentAssist(n,e){const t=this.gastProductionsCache[n];if(Xe(t))throw Error(`Rule ->${n}<- does not exist in this grammar.`);return Oc([t],e,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(n){const e=Me(n.ruleStack),t=this.getGAstProductions()[e];return new Hu(t,n).startWalking()}},class{initGastRecorder(n){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let n=0;n<10;n++){const e=n>0?n:"";this[`CONSUME${e}`]=function(t,r){return this.consumeInternalRecord(t,n,r)},this[`SUBRULE${e}`]=function(t,r){return this.subruleInternalRecord(t,n,r)},this[`OPTION${e}`]=function(t){return this.optionInternalRecord(t,n)},this[`OR${e}`]=function(t){return this.orInternalRecord(t,n)},this[`MANY${e}`]=function(t){this.manyInternalRecord(n,t)},this[`MANY_SEP${e}`]=function(t){this.manySepFirstInternalRecord(n,t)},this[`AT_LEAST_ONE${e}`]=function(t){this.atLeastOneInternalRecord(n,t)},this[`AT_LEAST_ONE_SEP${e}`]=function(t){this.atLeastOneSepFirstInternalRecord(n,t)}}this.consume=function(n,e,t){return this.consumeInternalRecord(e,n,t)},this.subrule=function(n,e,t){return this.subruleInternalRecord(e,n,t)},this.option=function(n,e){return this.optionInternalRecord(e,n)},this.or=function(n,e){return this.orInternalRecord(e,n)},this.many=function(n,e){this.manyInternalRecord(n,e)},this.atLeastOne=function(n,e){this.atLeastOneInternalRecord(n,e)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const n=this;for(let e=0;e<10;e++){const t=e>0?e:"";delete n[`CONSUME${t}`],delete n[`SUBRULE${t}`],delete n[`OPTION${t}`],delete n[`OR${t}`],delete n[`MANY${t}`],delete n[`MANY_SEP${t}`],delete n[`AT_LEAST_ONE${t}`],delete n[`AT_LEAST_ONE_SEP${t}`]}delete n.consume,delete n.subrule,delete n.option,delete n.or,delete n.many,delete n.atLeastOne,delete n.ACTION,delete n.BACKTRACK,delete n.LA})}ACTION_RECORD(n){}BACKTRACK_RECORD(n,e){return()=>!0}LA_RECORD(n){return Un}topLevelRuleRecord(n,e){try{const t=new Yt({definition:[],name:n});return t.name=n,this.recordingProdStack.push(t),e.call(this),this.recordingProdStack.pop(),t}catch(t){if(t.KNOWN_RECORDER_ERROR!==!0)try{t.message=t.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw t}throw t}}optionInternalRecord(n,e){return rn.call(this,ie,n,e)}atLeastOneInternalRecord(n,e){rn.call(this,Se,e,n)}atLeastOneSepFirstInternalRecord(n,e){rn.call(this,Ne,e,n,_s)}manyInternalRecord(n,e){rn.call(this,W,e,n)}manySepFirstInternalRecord(n,e){rn.call(this,Ee,e,n,_s)}orInternalRecord(n,e){return pd.call(this,n,e)}subruleInternalRecord(n,e,t){if(Xn(e),!n||$(n,"ruleName")===!1){const a=new Error(`<SUBRULE${Ms(e)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(n)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}const r=Kt(this.recordingProdStack),i=n.ruleName,s=new me({idx:e,nonTerminalName:i,label:t==null?void 0:t.LABEL,referencedRule:void 0});return r.definition.push(s),this.outputCst?fd:gr}consumeInternalRecord(n,e,t){if(Xn(e),!$c(n)){const s=new Error(`<CONSUME${Ms(e)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(n)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw s.KNOWN_RECORDER_ERROR=!0,s}const r=Kt(this.recordingProdStack),i=new G({idx:e,terminalType:n,label:t==null?void 0:t.LABEL});return r.definition.push(i),Wc}},class{initPerformanceTracer(n){if($(n,"traceInitPerf")){const e=n.traceInitPerf,t=typeof e=="number";this.traceInitMaxIdent=t?e:1/0,this.traceInitPerf=t?e>0:e}else this.traceInitMaxIdent=0,this.traceInitPerf=ze.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(n,e){if(this.traceInitPerf===!0){this.traceInitIndent++;const t=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${t}--> <${n}>`);const{time:r,value:i}=Ec(e),s=r>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&s(`${t}<-- <${n}> time: ${r}ms`),this.traceInitIndent--,i}return e()}}],Ds.forEach(n=>{const e=n.prototype;Object.getOwnPropertyNames(e).forEach(t=>{if(t==="constructor")return;const r=Object.getOwnPropertyDescriptor(e,t);r&&(r.get||r.set)?Object.defineProperty(Lr.prototype,t,r):Lr.prototype[t]=n.prototype[t]})});class md extends fn{constructor(e,t=ze){const r=te(t);r.outputCst=!1,super(e,r)}}function Ht(n,e,t){return`${n.name}_${e}_${t}`}const rt=1,gd=2,Fs=4,Gs=5,dn=7,yd=8,Td=9,vd=10,Ed=11,zc=12;class Pi{constructor(e){this.target=e}isEpsilon(){return!1}}class Mi extends Pi{constructor(e,t){super(e),this.tokenType=t}}class Yc extends Pi{constructor(e){super(e)}isEpsilon(){return!0}}class Di extends Pi{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function Rd(n){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};(function(r,i){const s=i.length;for(let a=0;a<s;a++){const o=i[a],c=Q(r,o,void 0,{type:gd}),l=Q(r,o,void 0,{type:dn});c.stop=l,r.ruleToStartState.set(o,c),r.ruleToStopState.set(o,l)}})(e,n);const t=n.length;for(let r=0;r<t;r++){const i=n[r],s=ct(e,i,i);s!==void 0&&Ad(e,i,s)}return e}function qc(n,e,t){return t instanceof G?br(n,e,t.terminalType,t):t instanceof me?function(r,i,s){const a=s.referencedRule,o=r.ruleToStartState.get(a),c=Q(r,i,s,{type:rt}),l=Q(r,i,s,{type:rt}),u=new Di(o,a,l);return Ui(c,u),{left:c,right:l}}(n,e,t):t instanceof Re?function(r,i,s){const a=Q(r,i,s,{type:rt});et(r,a);const o=I(s.definition,l=>qc(r,i,l));return vt(r,i,a,s,...o)}(n,e,t):t instanceof ie?function(r,i,s){const a=Q(r,i,s,{type:rt});et(r,a);const o=vt(r,i,a,s,ct(r,i,s));return function(c,l,u,h){const d=h.left,f=h.right;return z(d,f),c.decisionMap[Ht(l,"Option",u.idx)]=d,h}(r,i,s,o)}(n,e,t):t instanceof W?function(r,i,s){const a=Q(r,i,s,{type:Gs});et(r,a);const o=vt(r,i,a,s,ct(r,i,s));return js(r,i,s,o)}(n,e,t):t instanceof Ee?function(r,i,s){const a=Q(r,i,s,{type:Gs});et(r,a);const o=vt(r,i,a,s,ct(r,i,s)),c=br(r,i,s.separator,s);return js(r,i,s,o,c)}(n,e,t):t instanceof Se?function(r,i,s){const a=Q(r,i,s,{type:Fs});et(r,a);const o=vt(r,i,a,s,ct(r,i,s));return Bs(r,i,s,o)}(n,e,t):t instanceof Ne?function(r,i,s){const a=Q(r,i,s,{type:Fs});et(r,a);const o=vt(r,i,a,s,ct(r,i,s)),c=br(r,i,s.separator,s);return Bs(r,i,s,o,c)}(n,e,t):ct(n,e,t)}function ct(n,e,t){const r=ke(I(t.definition,i=>qc(n,e,i)),i=>i!==void 0);return r.length===1?r[0]:r.length===0?void 0:function(i,s){const a=s.length;for(let l=0;l<a-1;l++){const u=s[l];let h;u.left.transitions.length===1&&(h=u.left.transitions[0]);const d=h instanceof Di,f=h,p=s[l+1].left;u.left.type===rt&&u.right.type===rt&&h!==void 0&&(d&&f.followState===u.right||h.target===u.right)?(d?f.followState=p:h.target=p,kd(i,u.right)):z(u.right,p)}const o=s[0],c=s[a-1];return{left:o.left,right:c.right}}(n,r)}function Bs(n,e,t,r,i){const s=r.left,a=r.right,o=Q(n,e,t,{type:Ed});et(n,o);const c=Q(n,e,t,{type:zc});return s.loopback=o,c.loopback=o,n.decisionMap[Ht(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=o,z(a,o),i===void 0?(z(o,s),z(o,c)):(z(o,c),z(o,i.left),z(i.right,s)),{left:s,right:c}}function js(n,e,t,r,i){const s=r.left,a=r.right,o=Q(n,e,t,{type:vd});et(n,o);const c=Q(n,e,t,{type:zc}),l=Q(n,e,t,{type:Td});return o.loopback=l,c.loopback=l,z(o,s),z(o,c),z(a,l),i!==void 0?(z(l,c),z(l,i.left),z(i.right,s)):z(l,o),n.decisionMap[Ht(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=o,{left:o,right:c}}function et(n,e){return n.decisionStates.push(e),e.decision=n.decisionStates.length-1,e.decision}function vt(n,e,t,r,...i){const s=Q(n,e,r,{type:yd,start:t});t.end=s;for(const o of i)o!==void 0?(z(t,o.left),z(o.right,s)):z(t,s);const a={left:t,right:s};return n.decisionMap[Ht(e,function(o){if(o instanceof Re)return"Alternation";if(o instanceof ie)return"Option";if(o instanceof W)return"Repetition";if(o instanceof Ee)return"RepetitionWithSeparator";if(o instanceof Se)return"RepetitionMandatory";if(o instanceof Ne)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}(r),r.idx)]=t,a}function br(n,e,t,r){const i=Q(n,e,r,{type:rt}),s=Q(n,e,r,{type:rt});return Ui(i,new Mi(s,t)),{left:i,right:s}}function Ad(n,e,t){const r=n.ruleToStartState.get(e);z(r,t.left);const i=n.ruleToStopState.get(e);return z(t.right,i),{left:r,right:i}}function z(n,e){Ui(n,new Yc(e))}function Q(n,e,t,r){const i=Object.assign({atn:n,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:n.states.length},r);return n.states.push(i),i}function Ui(n,e){n.transitions.length===0&&(n.epsilonOnlyTransitions=e.isEpsilon()),n.transitions.push(e)}function kd(n,e){n.states.splice(n.states.indexOf(e),1)}const Qn={};class di{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=Xc(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return I(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function Xc(n,e=!0){return`${e?`a${n.alt}`:""}s${n.state.stateNumber}:${n.stack.map(t=>t.stateNumber.toString()).join("_")}`}function xd(n,e){const t={};return r=>{const i=r.toString();let s=t[i];return s!==void 0||(s={atnStartState:n,decision:e,states:{}},t[i]=s),s}}class Qc{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let r=0;r<t;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const Ks=new Qc;class Id extends _i{constructor(e){var t;super(),this.logging=(t=e==null?void 0:e.logging)!==null&&t!==void 0?t:r=>console.log(r)}initialize(e){this.atn=Rd(e.rules),this.dfas=function(t){const r=t.decisionStates.length,i=Array(r);for(let s=0;s<r;s++)i[s]=xd(t.decisionStates[s],s);return i}(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:r,hasPredicates:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,c=Ht(r,"Alternation",t),l=this.atn.decisionMap[c].decision,u=I(Cs({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),h=>I(h,d=>d[0]));if(Vs(u,!1)&&!s){const h=pe(u,(d,f,p)=>(C(f,m=>{m&&(d[m.tokenTypeIdx]=p,C(m.categoryMatches,y=>{d[y]=p}))}),d),{});return i?function(d){var f;const p=this.LA(1),m=h[p.tokenTypeIdx];if(d!==void 0&&m!==void 0){const y=(f=d[m])===null||f===void 0?void 0:f.GATE;if(y!==void 0&&y.call(this)===!1)return}return m}:function(){const d=this.LA(1);return h[d.tokenTypeIdx]}}return i?function(h){const d=new Qc,f=h===void 0?0:h.length;for(let m=0;m<f;m++){const y=h==null?void 0:h[m].GATE;d.set(m,y===void 0||y.call(this))}const p=Or.call(this,a,l,d,o);return typeof p=="number"?p:void 0}:function(){const h=Or.call(this,a,l,Ks,o);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:r,prodType:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,c=Ht(r,i,t),l=this.atn.decisionMap[c].decision,u=I(Cs({maxLookahead:1,occurrence:t,prodType:i,rule:r}),h=>I(h,d=>d[0]));if(Vs(u)&&u[0][0]&&!s){const h=u[0],d=$e(h);if(d.length===1&&F(d[0].categoryMatches)){const f=d[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===f}}{const f=pe(d,(p,m)=>(m!==void 0&&(p[m.tokenTypeIdx]=!0,C(m.categoryMatches,y=>{p[y]=!0})),p),{});return function(){const p=this.LA(1);return f[p.tokenTypeIdx]===!0}}}return function(){const h=Or.call(this,a,l,Ks,o);return typeof h!="object"&&h===0}}}function Vs(n,e=!0){const t=new Set;for(const r of n){const i=new Set;for(const s of r){if(s===void 0){if(e)break;return!1}const a=[s.tokenTypeIdx].concat(s.categoryMatches);for(const o of a)if(t.has(o)){if(!i.has(o))return!1}else t.add(o),i.add(o)}}return!0}function Or(n,e,t,r){const i=n[e](t);let s=i.start;return s===void 0&&(s=Zc(i,Jc(Ld(i.atnStartState))),i.start=s),Sd.apply(this,[i,s,t,r])}function Sd(n,e,t,r){let i=e,s=1;const a=[];let o=this.LA(s++);for(;;){let l=(c=o,i.edges[c.tokenTypeIdx]);if(l===void 0&&(l=Nd.apply(this,[n,i,o,s,t,r])),l===Qn)return $d(a,i,o);if(l.isAcceptState===!0)return l.prediction;i=l,a.push(o),o=this.LA(s++)}var c}function Nd(n,e,t,r,i,s){const a=function(l,u,h){const d=new di,f=[];for(const m of l.elements){if(h.is(m.alt)===!1)continue;if(m.state.type===dn){f.push(m);continue}const y=m.state.transitions.length;for(let E=0;E<y;E++){const T=wd(m.state.transitions[E],u);T!==void 0&&d.add({state:T,alt:m.alt,stack:m.stack})}}let p;if(f.length===0&&d.size===1&&(p=d),p===void 0){p=new di;for(const m of d.elements)Jn(m,p)}if(f.length>0&&!function(m){for(const y of m.elements)if(y.state.type===dn)return!0;return!1}(p))for(const m of f)p.add(m);return p}(e.configs,t,i);if(a.size===0)return Hs(n,e,t,Qn),Qn;let o=Jc(a);const c=function(l,u){let h;for(const d of l.elements)if(u.is(d.alt)===!0){if(h===void 0)h=d.alt;else if(h!==d.alt)return}return h}(a,i);if(c!==void 0)o.isAcceptState=!0,o.prediction=c,o.configs.uniqueAlt=c;else if(function(l){if(function(h){for(const d of h.elements)if(d.state.type!==dn)return!1;return!0}(l))return!0;const u=function(h){const d=new Map;for(const f of h){const p=Xc(f,!1);let m=d.get(p);m===void 0&&(m={},d.set(p,m)),m[f.alt]=!0}return d}(l.elements);return function(h){for(const d of Array.from(h.values()))if(Object.keys(d).length>1)return!0;return!1}(u)&&!function(h){for(const d of Array.from(h.values()))if(Object.keys(d).length===1)return!0;return!1}(u)}(a)){const l=nu(a.alts);o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l,Cd.apply(this,[n,r,a.alts,s])}return o=Hs(n,e,t,o),o}function Cd(n,e,t,r){const i=[];for(let a=1;a<=e;a++)i.push(this.LA(a).tokenType);const s=n.atnStartState;r(function(a){const o=I(a.prefixPath,u=>St(u)).join(", "),c=a.production.idx===0?"":a.production.idx;let l=`Ambiguous Alternatives Detected: <${a.ambiguityIndices.join(", ")}> in <${function(u){if(u instanceof me)return"SUBRULE";if(u instanceof ie)return"OPTION";if(u instanceof Re)return"OR";if(u instanceof Se)return"AT_LEAST_ONE";if(u instanceof Ne)return"AT_LEAST_ONE_SEP";if(u instanceof Ee)return"MANY_SEP";if(u instanceof W)return"MANY";if(u instanceof G)return"CONSUME";throw Error("non exhaustive match")}(a.production)}${c}> inside <${a.topLevelRule.name}> Rule,
<${o}> may appears as a prefix path in all these alternatives.
`;return l+=`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,l}({topLevelRule:s.rule,ambiguityIndices:t,production:s.production,prefixPath:i}))}function $d(n,e,t){const r=function(i,s){return i&&i.length?ko(i,zt(s)):[]}(xe(e.configs.elements,i=>i.state.transitions).filter(i=>i instanceof Mi).map(i=>i.tokenType),i=>i.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:r,tokenPath:n}}function wd(n,e){if(n instanceof Mi&&bc(e,n.tokenType))return n.target}function Jc(n){return{configs:n,edges:{},isAcceptState:!1,prediction:-1}}function Hs(n,e,t,r){return r=Zc(n,r),e.edges[t.tokenTypeIdx]=r,r}function Zc(n,e){if(e===Qn)return e;const t=e.configs.key,r=n.states[t];return r!==void 0?r:(e.configs.finalize(),n.states[t]=e,e)}function Ld(n){const e=new di,t=n.transitions.length;for(let r=0;r<t;r++)Jn({state:n.transitions[r].target,alt:r,stack:[]},e);return e}function Jn(n,e){const t=n.state;if(t.type===dn){if(n.stack.length>0){const i=[...n.stack];Jn({state:i.pop(),alt:n.alt,stack:i},e)}else e.add(n);return}t.epsilonOnlyTransitions||e.add(n);const r=t.transitions.length;for(let i=0;i<r;i++){const s=bd(n,t.transitions[i]);s!==void 0&&Jn(s,e)}}function bd(n,e){if(e instanceof Yc)return{state:e.target,alt:n.alt,stack:n.stack};if(e instanceof Di){const t=[...n.stack,e.followState];return{state:e.target,alt:n.alt,stack:t}}}var Ws,_r,zs,Nn,M,_,Cn,Ys,Pr,qs,Xs,Qs,Js,Mr,Zs,ea,ta,$n,Et,Rt,Dr,At,na,Ur,Fr,Gr,Br,jr,ra,ia,Kr,sa,Vr,sn,aa,oa,ca,la,ua,da,ha,fa,wn,pa,ma,ga,ya,Ta,va,Ea,Ra,Aa,ka,xa,Ln,Ia,Sa,Na,Ca,$a,wa,La,ba,Oa,_a,Pa,Ma,Da,Hr,Wr,Ua,Fa,Ga,Ba,ja,Ka,Va,Ha,Wa;(function(n){n.is=function(e){return typeof e=="string"}})(Ws||(Ws={})),(_r||(_r={})).is=function(n){return typeof n=="string"},function(n){n.MIN_VALUE=-2147483648,n.MAX_VALUE=2147483647,n.is=function(e){return typeof e=="number"&&n.MIN_VALUE<=e&&e<=n.MAX_VALUE}}(zs||(zs={})),function(n){n.MIN_VALUE=0,n.MAX_VALUE=2147483647,n.is=function(e){return typeof e=="number"&&n.MIN_VALUE<=e&&e<=n.MAX_VALUE}}(Nn||(Nn={})),function(n){n.create=function(e,t){return e===Number.MAX_VALUE&&(e=Nn.MAX_VALUE),t===Number.MAX_VALUE&&(t=Nn.MAX_VALUE),{line:e,character:t}},n.is=function(e){let t=e;return g.objectLiteral(t)&&g.uinteger(t.line)&&g.uinteger(t.character)}}(M||(M={})),function(n){n.create=function(e,t,r,i){if(g.uinteger(e)&&g.uinteger(t)&&g.uinteger(r)&&g.uinteger(i))return{start:M.create(e,t),end:M.create(r,i)};if(M.is(e)&&M.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${i}]`)},n.is=function(e){let t=e;return g.objectLiteral(t)&&M.is(t.start)&&M.is(t.end)}}(_||(_={})),function(n){n.create=function(e,t){return{uri:e,range:t}},n.is=function(e){let t=e;return g.objectLiteral(t)&&_.is(t.range)&&(g.string(t.uri)||g.undefined(t.uri))}}(Cn||(Cn={})),function(n){n.create=function(e,t,r,i){return{targetUri:e,targetRange:t,targetSelectionRange:r,originSelectionRange:i}},n.is=function(e){let t=e;return g.objectLiteral(t)&&_.is(t.targetRange)&&g.string(t.targetUri)&&_.is(t.targetSelectionRange)&&(_.is(t.originSelectionRange)||g.undefined(t.originSelectionRange))}}(Ys||(Ys={})),function(n){n.create=function(e,t,r,i){return{red:e,green:t,blue:r,alpha:i}},n.is=function(e){const t=e;return g.objectLiteral(t)&&g.numberRange(t.red,0,1)&&g.numberRange(t.green,0,1)&&g.numberRange(t.blue,0,1)&&g.numberRange(t.alpha,0,1)}}(Pr||(Pr={})),function(n){n.create=function(e,t){return{range:e,color:t}},n.is=function(e){const t=e;return g.objectLiteral(t)&&_.is(t.range)&&Pr.is(t.color)}}(qs||(qs={})),function(n){n.create=function(e,t,r){return{label:e,textEdit:t,additionalTextEdits:r}},n.is=function(e){const t=e;return g.objectLiteral(t)&&g.string(t.label)&&(g.undefined(t.textEdit)||Rt.is(t))&&(g.undefined(t.additionalTextEdits)||g.typedArray(t.additionalTextEdits,Rt.is))}}(Xs||(Xs={})),function(n){n.Comment="comment",n.Imports="imports",n.Region="region"}(Qs||(Qs={})),function(n){n.create=function(e,t,r,i,s,a){const o={startLine:e,endLine:t};return g.defined(r)&&(o.startCharacter=r),g.defined(i)&&(o.endCharacter=i),g.defined(s)&&(o.kind=s),g.defined(a)&&(o.collapsedText=a),o},n.is=function(e){const t=e;return g.objectLiteral(t)&&g.uinteger(t.startLine)&&g.uinteger(t.startLine)&&(g.undefined(t.startCharacter)||g.uinteger(t.startCharacter))&&(g.undefined(t.endCharacter)||g.uinteger(t.endCharacter))&&(g.undefined(t.kind)||g.string(t.kind))}}(Js||(Js={})),function(n){n.create=function(e,t){return{location:e,message:t}},n.is=function(e){let t=e;return g.defined(t)&&Cn.is(t.location)&&g.string(t.message)}}(Mr||(Mr={})),function(n){n.Error=1,n.Warning=2,n.Information=3,n.Hint=4}(Zs||(Zs={})),function(n){n.Unnecessary=1,n.Deprecated=2}(ea||(ea={})),function(n){n.is=function(e){const t=e;return g.objectLiteral(t)&&g.string(t.href)}}(ta||(ta={})),function(n){n.create=function(e,t,r,i,s,a){let o={range:e,message:t};return g.defined(r)&&(o.severity=r),g.defined(i)&&(o.code=i),g.defined(s)&&(o.source=s),g.defined(a)&&(o.relatedInformation=a),o},n.is=function(e){var t;let r=e;return g.defined(r)&&_.is(r.range)&&g.string(r.message)&&(g.number(r.severity)||g.undefined(r.severity))&&(g.integer(r.code)||g.string(r.code)||g.undefined(r.code))&&(g.undefined(r.codeDescription)||g.string((t=r.codeDescription)===null||t===void 0?void 0:t.href))&&(g.string(r.source)||g.undefined(r.source))&&(g.undefined(r.relatedInformation)||g.typedArray(r.relatedInformation,Mr.is))}}($n||($n={})),function(n){n.create=function(e,t,...r){let i={title:e,command:t};return g.defined(r)&&r.length>0&&(i.arguments=r),i},n.is=function(e){let t=e;return g.defined(t)&&g.string(t.title)&&g.string(t.command)}}(Et||(Et={})),function(n){n.replace=function(e,t){return{range:e,newText:t}},n.insert=function(e,t){return{range:{start:e,end:e},newText:t}},n.del=function(e){return{range:e,newText:""}},n.is=function(e){const t=e;return g.objectLiteral(t)&&g.string(t.newText)&&_.is(t.range)}}(Rt||(Rt={})),function(n){n.create=function(e,t,r){const i={label:e};return t!==void 0&&(i.needsConfirmation=t),r!==void 0&&(i.description=r),i},n.is=function(e){const t=e;return g.objectLiteral(t)&&g.string(t.label)&&(g.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(g.string(t.description)||t.description===void 0)}}(Dr||(Dr={})),function(n){n.is=function(e){const t=e;return g.string(t)}}(At||(At={})),function(n){n.replace=function(e,t,r){return{range:e,newText:t,annotationId:r}},n.insert=function(e,t,r){return{range:{start:e,end:e},newText:t,annotationId:r}},n.del=function(e,t){return{range:e,newText:"",annotationId:t}},n.is=function(e){const t=e;return Rt.is(t)&&(Dr.is(t.annotationId)||At.is(t.annotationId))}}(na||(na={})),function(n){n.create=function(e,t){return{textDocument:e,edits:t}},n.is=function(e){let t=e;return g.defined(t)&&Kr.is(t.textDocument)&&Array.isArray(t.edits)}}(Ur||(Ur={})),function(n){n.create=function(e,t,r){let i={kind:"create",uri:e};return t===void 0||t.overwrite===void 0&&t.ignoreIfExists===void 0||(i.options=t),r!==void 0&&(i.annotationId=r),i},n.is=function(e){let t=e;return t&&t.kind==="create"&&g.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||g.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||g.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||At.is(t.annotationId))}}(Fr||(Fr={})),function(n){n.create=function(e,t,r,i){let s={kind:"rename",oldUri:e,newUri:t};return r===void 0||r.overwrite===void 0&&r.ignoreIfExists===void 0||(s.options=r),i!==void 0&&(s.annotationId=i),s},n.is=function(e){let t=e;return t&&t.kind==="rename"&&g.string(t.oldUri)&&g.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||g.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||g.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||At.is(t.annotationId))}}(Gr||(Gr={})),function(n){n.create=function(e,t,r){let i={kind:"delete",uri:e};return t===void 0||t.recursive===void 0&&t.ignoreIfNotExists===void 0||(i.options=t),r!==void 0&&(i.annotationId=r),i},n.is=function(e){let t=e;return t&&t.kind==="delete"&&g.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||g.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||g.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||At.is(t.annotationId))}}(Br||(Br={})),function(n){n.is=function(e){let t=e;return t&&(t.changes!==void 0||t.documentChanges!==void 0)&&(t.documentChanges===void 0||t.documentChanges.every(r=>g.string(r.kind)?Fr.is(r)||Gr.is(r)||Br.is(r):Ur.is(r)))}}(jr||(jr={})),function(n){n.create=function(e){return{uri:e}},n.is=function(e){let t=e;return g.defined(t)&&g.string(t.uri)}}(ra||(ra={})),function(n){n.create=function(e,t){return{uri:e,version:t}},n.is=function(e){let t=e;return g.defined(t)&&g.string(t.uri)&&g.integer(t.version)}}(ia||(ia={})),function(n){n.create=function(e,t){return{uri:e,version:t}},n.is=function(e){let t=e;return g.defined(t)&&g.string(t.uri)&&(t.version===null||g.integer(t.version))}}(Kr||(Kr={})),function(n){n.create=function(e,t,r,i){return{uri:e,languageId:t,version:r,text:i}},n.is=function(e){let t=e;return g.defined(t)&&g.string(t.uri)&&g.string(t.languageId)&&g.integer(t.version)&&g.string(t.text)}}(sa||(sa={})),function(n){n.PlainText="plaintext",n.Markdown="markdown",n.is=function(e){const t=e;return t===n.PlainText||t===n.Markdown}}(Vr||(Vr={})),function(n){n.is=function(e){const t=e;return g.objectLiteral(e)&&Vr.is(t.kind)&&g.string(t.value)}}(sn||(sn={})),function(n){n.Text=1,n.Method=2,n.Function=3,n.Constructor=4,n.Field=5,n.Variable=6,n.Class=7,n.Interface=8,n.Module=9,n.Property=10,n.Unit=11,n.Value=12,n.Enum=13,n.Keyword=14,n.Snippet=15,n.Color=16,n.File=17,n.Reference=18,n.Folder=19,n.EnumMember=20,n.Constant=21,n.Struct=22,n.Event=23,n.Operator=24,n.TypeParameter=25}(aa||(aa={})),function(n){n.PlainText=1,n.Snippet=2}(oa||(oa={})),function(n){n.Deprecated=1}(ca||(ca={})),function(n){n.create=function(e,t,r){return{newText:e,insert:t,replace:r}},n.is=function(e){const t=e;return t&&g.string(t.newText)&&_.is(t.insert)&&_.is(t.replace)}}(la||(la={})),function(n){n.asIs=1,n.adjustIndentation=2}(ua||(ua={})),function(n){n.is=function(e){const t=e;return t&&(g.string(t.detail)||t.detail===void 0)&&(g.string(t.description)||t.description===void 0)}}(da||(da={})),function(n){n.create=function(e){return{label:e}}}(ha||(ha={})),function(n){n.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(fa||(fa={})),function(n){n.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},n.is=function(e){const t=e;return g.string(t)||g.objectLiteral(t)&&g.string(t.language)&&g.string(t.value)}}(wn||(wn={})),function(n){n.is=function(e){let t=e;return!!t&&g.objectLiteral(t)&&(sn.is(t.contents)||wn.is(t.contents)||g.typedArray(t.contents,wn.is))&&(e.range===void 0||_.is(e.range))}}(pa||(pa={})),function(n){n.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(ma||(ma={})),function(n){n.create=function(e,t,...r){let i={label:e};return g.defined(t)&&(i.documentation=t),g.defined(r)?i.parameters=r:i.parameters=[],i}}(ga||(ga={})),function(n){n.Text=1,n.Read=2,n.Write=3}(ya||(ya={})),function(n){n.create=function(e,t){let r={range:e};return g.number(t)&&(r.kind=t),r}}(Ta||(Ta={})),function(n){n.File=1,n.Module=2,n.Namespace=3,n.Package=4,n.Class=5,n.Method=6,n.Property=7,n.Field=8,n.Constructor=9,n.Enum=10,n.Interface=11,n.Function=12,n.Variable=13,n.Constant=14,n.String=15,n.Number=16,n.Boolean=17,n.Array=18,n.Object=19,n.Key=20,n.Null=21,n.EnumMember=22,n.Struct=23,n.Event=24,n.Operator=25,n.TypeParameter=26}(va||(va={})),function(n){n.Deprecated=1}(Ea||(Ea={})),function(n){n.create=function(e,t,r,i,s){let a={name:e,kind:t,location:{uri:i,range:r}};return s&&(a.containerName=s),a}}(Ra||(Ra={})),function(n){n.create=function(e,t,r,i){return i!==void 0?{name:e,kind:t,location:{uri:r,range:i}}:{name:e,kind:t,location:{uri:r}}}}(Aa||(Aa={})),function(n){n.create=function(e,t,r,i,s,a){let o={name:e,detail:t,kind:r,range:i,selectionRange:s};return a!==void 0&&(o.children=a),o},n.is=function(e){let t=e;return t&&g.string(t.name)&&g.number(t.kind)&&_.is(t.range)&&_.is(t.selectionRange)&&(t.detail===void 0||g.string(t.detail))&&(t.deprecated===void 0||g.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}}(ka||(ka={})),function(n){n.Empty="",n.QuickFix="quickfix",n.Refactor="refactor",n.RefactorExtract="refactor.extract",n.RefactorInline="refactor.inline",n.RefactorRewrite="refactor.rewrite",n.Source="source",n.SourceOrganizeImports="source.organizeImports",n.SourceFixAll="source.fixAll"}(xa||(xa={})),function(n){n.Invoked=1,n.Automatic=2}(Ln||(Ln={})),function(n){n.create=function(e,t,r){let i={diagnostics:e};return t!=null&&(i.only=t),r!=null&&(i.triggerKind=r),i},n.is=function(e){let t=e;return g.defined(t)&&g.typedArray(t.diagnostics,$n.is)&&(t.only===void 0||g.typedArray(t.only,g.string))&&(t.triggerKind===void 0||t.triggerKind===Ln.Invoked||t.triggerKind===Ln.Automatic)}}(Ia||(Ia={})),function(n){n.create=function(e,t,r){let i={title:e},s=!0;return typeof t=="string"?(s=!1,i.kind=t):Et.is(t)?i.command=t:i.edit=t,s&&r!==void 0&&(i.kind=r),i},n.is=function(e){let t=e;return t&&g.string(t.title)&&(t.diagnostics===void 0||g.typedArray(t.diagnostics,$n.is))&&(t.kind===void 0||g.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||Et.is(t.command))&&(t.isPreferred===void 0||g.boolean(t.isPreferred))&&(t.edit===void 0||jr.is(t.edit))}}(Sa||(Sa={})),function(n){n.create=function(e,t){let r={range:e};return g.defined(t)&&(r.data=t),r},n.is=function(e){let t=e;return g.defined(t)&&_.is(t.range)&&(g.undefined(t.command)||Et.is(t.command))}}(Na||(Na={})),function(n){n.create=function(e,t){return{tabSize:e,insertSpaces:t}},n.is=function(e){let t=e;return g.defined(t)&&g.uinteger(t.tabSize)&&g.boolean(t.insertSpaces)}}(Ca||(Ca={})),function(n){n.create=function(e,t,r){return{range:e,target:t,data:r}},n.is=function(e){let t=e;return g.defined(t)&&_.is(t.range)&&(g.undefined(t.target)||g.string(t.target))}}($a||($a={})),function(n){n.create=function(e,t){return{range:e,parent:t}},n.is=function(e){let t=e;return g.objectLiteral(t)&&_.is(t.range)&&(t.parent===void 0||n.is(t.parent))}}(wa||(wa={})),function(n){n.namespace="namespace",n.type="type",n.class="class",n.enum="enum",n.interface="interface",n.struct="struct",n.typeParameter="typeParameter",n.parameter="parameter",n.variable="variable",n.property="property",n.enumMember="enumMember",n.event="event",n.function="function",n.method="method",n.macro="macro",n.keyword="keyword",n.modifier="modifier",n.comment="comment",n.string="string",n.number="number",n.regexp="regexp",n.operator="operator",n.decorator="decorator"}(La||(La={})),function(n){n.declaration="declaration",n.definition="definition",n.readonly="readonly",n.static="static",n.deprecated="deprecated",n.abstract="abstract",n.async="async",n.modification="modification",n.documentation="documentation",n.defaultLibrary="defaultLibrary"}(ba||(ba={})),function(n){n.is=function(e){const t=e;return g.objectLiteral(t)&&(t.resultId===void 0||typeof t.resultId=="string")&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]=="number")}}(Oa||(Oa={})),function(n){n.create=function(e,t){return{range:e,text:t}},n.is=function(e){const t=e;return t!=null&&_.is(t.range)&&g.string(t.text)}}(_a||(_a={})),function(n){n.create=function(e,t,r){return{range:e,variableName:t,caseSensitiveLookup:r}},n.is=function(e){const t=e;return t!=null&&_.is(t.range)&&g.boolean(t.caseSensitiveLookup)&&(g.string(t.variableName)||t.variableName===void 0)}}(Pa||(Pa={})),function(n){n.create=function(e,t){return{range:e,expression:t}},n.is=function(e){const t=e;return t!=null&&_.is(t.range)&&(g.string(t.expression)||t.expression===void 0)}}(Ma||(Ma={})),function(n){n.create=function(e,t){return{frameId:e,stoppedLocation:t}},n.is=function(e){const t=e;return g.defined(t)&&_.is(e.stoppedLocation)}}(Da||(Da={})),function(n){n.Type=1,n.Parameter=2,n.is=function(e){return e===1||e===2}}(Hr||(Hr={})),function(n){n.create=function(e){return{value:e}},n.is=function(e){const t=e;return g.objectLiteral(t)&&(t.tooltip===void 0||g.string(t.tooltip)||sn.is(t.tooltip))&&(t.location===void 0||Cn.is(t.location))&&(t.command===void 0||Et.is(t.command))}}(Wr||(Wr={})),function(n){n.create=function(e,t,r){const i={position:e,label:t};return r!==void 0&&(i.kind=r),i},n.is=function(e){const t=e;return g.objectLiteral(t)&&M.is(t.position)&&(g.string(t.label)||g.typedArray(t.label,Wr.is))&&(t.kind===void 0||Hr.is(t.kind))&&t.textEdits===void 0||g.typedArray(t.textEdits,Rt.is)&&(t.tooltip===void 0||g.string(t.tooltip)||sn.is(t.tooltip))&&(t.paddingLeft===void 0||g.boolean(t.paddingLeft))&&(t.paddingRight===void 0||g.boolean(t.paddingRight))}}(Ua||(Ua={})),function(n){n.createSnippet=function(e){return{kind:"snippet",value:e}}}(Fa||(Fa={})),function(n){n.create=function(e,t,r,i){return{insertText:e,filterText:t,range:r,command:i}}}(Ga||(Ga={})),function(n){n.create=function(e){return{items:e}}}(Ba||(Ba={})),function(n){n.Invoked=0,n.Automatic=1}(ja||(ja={})),function(n){n.create=function(e,t){return{range:e,text:t}}}(Ka||(Ka={})),function(n){n.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(Va||(Va={})),function(n){n.is=function(e){const t=e;return g.objectLiteral(t)&&_r.is(t.uri)&&g.string(t.name)}}(Ha||(Ha={})),function(n){function e(t,r){if(t.length<=1)return t;const i=t.length/2|0,s=t.slice(0,i),a=t.slice(i);e(s,r),e(a,r);let o=0,c=0,l=0;for(;o<s.length&&c<a.length;){let u=r(s[o],a[c]);t[l++]=u<=0?s[o++]:a[c++]}for(;o<s.length;)t[l++]=s[o++];for(;c<a.length;)t[l++]=a[c++];return t}n.create=function(t,r,i,s){return new Od(t,r,i,s)},n.is=function(t){let r=t;return!!(g.defined(r)&&g.string(r.uri)&&(g.undefined(r.languageId)||g.string(r.languageId))&&g.uinteger(r.lineCount)&&g.func(r.getText)&&g.func(r.positionAt)&&g.func(r.offsetAt))},n.applyEdits=function(t,r){let i=t.getText(),s=e(r,(o,c)=>{let l=o.range.start.line-c.range.start.line;return l===0?o.range.start.character-c.range.start.character:l}),a=i.length;for(let o=s.length-1;o>=0;o--){let c=s[o],l=t.offsetAt(c.range.start),u=t.offsetAt(c.range.end);if(!(u<=a))throw new Error("Overlapping edit");i=i.substring(0,l)+c.newText+i.substring(u,i.length),a=l}return i}}(Wa||(Wa={}));let Od=class{constructor(n,e,t,r){this._uri=n,this._languageId=e,this._version=t,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(n){if(n){let e=this.offsetAt(n.start),t=this.offsetAt(n.end);return this._content.substring(e,t)}return this._content}update(n,e){this._content=n.text,this._version=e,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let n=[],e=this._content,t=!0;for(let r=0;r<e.length;r++){t&&(n.push(r),t=!1);let i=e.charAt(r);t=i==="\r"||i===`
`,i==="\r"&&r+1<e.length&&e.charAt(r+1)===`
`&&r++}t&&e.length>0&&n.push(e.length),this._lineOffsets=n}return this._lineOffsets}positionAt(n){n=Math.max(Math.min(n,this._content.length),0);let e=this.getLineOffsets(),t=0,r=e.length;if(r===0)return M.create(0,n);for(;t<r;){let s=Math.floor((t+r)/2);e[s]>n?r=s:t=s+1}let i=t-1;return M.create(i,n-e[i])}offsetAt(n){let e=this.getLineOffsets();if(n.line>=e.length)return this._content.length;if(n.line<0)return 0;let t=e[n.line],r=n.line+1<e.length?e[n.line+1]:this._content.length;return Math.max(Math.min(t+n.character,r),t)}get lineCount(){return this.getLineOffsets().length}};var g;(function(n){const e=Object.prototype.toString;n.defined=function(t){return t!==void 0},n.undefined=function(t){return t===void 0},n.boolean=function(t){return t===!0||t===!1},n.string=function(t){return e.call(t)==="[object String]"},n.number=function(t){return e.call(t)==="[object Number]"},n.numberRange=function(t,r,i){return e.call(t)==="[object Number]"&&r<=t&&t<=i},n.integer=function(t){return e.call(t)==="[object Number]"&&-2147483648<=t&&t<=2147483647},n.uinteger=function(t){return e.call(t)==="[object Number]"&&0<=t&&t<=2147483647},n.func=function(t){return e.call(t)==="[object Function]"},n.objectLiteral=function(t){return t!==null&&typeof t=="object"},n.typedArray=function(t,r){return Array.isArray(t)&&t.every(r)}})(g||(g={}));class _d{constructor(){this.nodeStack=[]}get current(){return this.nodeStack[this.nodeStack.length-1]}buildRootNode(e){return this.rootNode=new tl(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new Fi;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const r=new hi(e.startOffset,e.image.length,ti(e),e.tokenType,!1);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const t=e.container;if(t){const r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const r=this.nodeStack.pop();(r==null?void 0:r.content.length)===0&&this.removeNode(r)}addHiddenTokens(e){for(const t of e){const r=new hi(t.startOffset,t.image.length,ti(t),t.tokenType,!0);r.root=this.rootNode,this.addHiddenToken(this.rootNode,r)}}addHiddenToken(e,t){const{offset:r,end:i}=t;for(let s=0;s<e.content.length;s++){const a=e.content[s],{offset:o,end:c}=a;if(Vt(a)&&r>o&&i<c)return void this.addHiddenToken(a,t);if(i<=o)return void e.content.splice(s,0,t)}e.content.push(t)}}class el{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class hi extends el{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=r}}class Fi extends el{constructor(){super(...arguments),this.content=new Gi(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:r}=e,{range:i}=t;this._rangeCache={start:r.start,end:i.end.line<r.start.line?r.start:i.end}}return this._rangeCache}return{start:M.create(0,0),end:M.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class Gi extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,Gi.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(const t of e)t.container=this.parent}}class tl extends Fi{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}}const fi=Symbol("Datatype");function zr(n){return n.$type===fi}const nl=n=>n.endsWith("​")?n:n+"​";class rl{constructor(e){this._unorderedGroups=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition;this.wrapper=new Fd(t,Object.assign(Object.assign({},e.parser.ParserConfig),{errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class Pd extends rl{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new _d,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const r=e.fragment?void 0:wi(e)?fi:dr(e),i=this.wrapper.DEFINE_RULE(nl(e.name),this.startImplementation(r,t).bind(this));return e.entry&&(this.mainRule=i),i}parse(e){this.nodeBuilder.buildRootNode(e);const t=this.lexer.tokenize(e);this.wrapper.input=t.tokens;const r=this.mainRule.call(this.wrapper,{});return this.nodeBuilder.addHiddenTokens(t.hidden),this.unorderedGroups.clear(),{value:r,lexerErrors:t.errors,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{if(!this.isRecording()){const s={$type:e};this.stack.push(s),e===fi&&(s.value="")}let i;try{i=t(r)}catch{i=void 0}return this.isRecording()||i!==void 0||(i=this.construct()),i}}consume(e,t,r){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.nodeBuilder.buildLeafNode(i,r),{assignment:a,isCrossRef:o}=this.getAssignment(r),c=this.current;if(a){const l=gt(r)?i.image:this.converter.convert(i.image,s);this.assign(a.operator,a.feature,l,s,o)}else if(zr(c)){let l=i.image;gt(r)||(l=this.converter.convert(l,s).toString()),c.value+=l}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,r,i){let s;this.isRecording()||(s=this.nodeBuilder.buildCompositeNode(r));const a=this.wrapper.wrapSubrule(e,t,i);!this.isRecording()&&s&&s.length>0&&this.performSubruleAssignment(a,r,s)}performSubruleAssignment(e,t,r){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,r,s);else if(!i){const a=this.current;if(zr(a))a.value+=e.toString();else if(typeof e=="object"&&e){const o=e.$type,c=this.assignWithoutOverride(e,a);o&&(c.$type=o);const l=c;this.stack.pop(),this.stack.push(l)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(!r.$cstNode&&t.feature&&t.operator){r=this.construct(!1);const s=r.$cstNode.feature;this.nodeBuilder.buildCompositeNode(s)}const i={$type:e};this.stack.pop(),this.stack.push(i),t.feature&&t.operator&&this.assign(t.operator,t.feature,r,r.$cstNode,!1)}}construct(e=!0){if(this.isRecording())return;const t=this.current;return function(r){for(const[i,s]of Object.entries(r))i.startsWith("$")||(Array.isArray(s)?s.forEach((a,o)=>{le(a)&&(a.$container=r,a.$containerProperty=i,a.$containerIndex=o)}):le(s)&&(s.$container=r,s.$containerProperty=i))}(t),this.nodeBuilder.construct(t),e&&this.stack.pop(),zr(t)?this.converter.convert(t.value,t.$cstNode):(function(r,i){const s=r.getTypeMetaData(i.$type),a=i;for(const o of s.properties)o.defaultValue!==void 0&&a[o.name]===void 0&&(a[o.name]=uc(o.defaultValue))}(this.astReflection,t),t)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=lr(e,mt);this.assignmentMap.set(e,{assignment:t,isCrossRef:!!t&&Si(t.terminal)})}return this.assignmentMap.get(e)}assign(e,t,r,i,s){const a=this.current;let o;switch(o=s&&typeof r=="string"?this.linker.buildReference(a,t,i,r):r,e){case"=":a[t]=o;break;case"?=":a[t]=!0;break;case"+=":Array.isArray(a[t])||(a[t]=[]),a[t].push(o)}}assignWithoutOverride(e,t){for(const[r,i]of Object.entries(t)){const s=e[r];s===void 0?e[r]=i:Array.isArray(s)&&Array.isArray(i)&&(i.push(...s),e[r]=i)}return e}get definitionErrors(){return this.wrapper.definitionErrors}}class Md{buildMismatchTokenMessage(e){return xt.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return xt.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return xt.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return xt.buildEarlyExitMessage(e)}}class il extends Md{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class Dd extends rl{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e);return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const r=this.wrapper.DEFINE_RULE(nl(e.name),this.startImplementation(t).bind(this));return e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,i){this.before(r),this.wrapper.wrapSubrule(e,t,i),this.after(r)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const Ud={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new il};class Fd extends md{constructor(e,t){const r=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},Ud),{lookaheadStrategy:r?new _i({maxLookahead:t.maxLookahead}):new Id}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function sl(n,e,t){return function(r,i){const s=hc(i,!1),a=ne(i.rules).filter(we).filter(o=>s.has(o));for(const o of a){const c=Object.assign(Object.assign({},r),{consume:1,optional:1,subrule:1,many:1,or:1});c.rules.set(o.name,r.parser.rule(o,lt(c,o.definition)))}}({parser:e,tokens:t,rules:new Map,ruleNames:new Map},n),e}function lt(n,e,t=!1){let r;if(gt(e))r=function(s,a){const o=s.consume++,c=s.tokens[a.value];if(!c)throw new Error("Could not find token for keyword: "+a.value);return()=>s.parser.consume(o,c,a)}(n,e);else if(cr(e))r=function(s,a){const o=dr(a);return()=>s.parser.action(o,a)}(n,e);else if(mt(e))r=lt(n,e.terminal);else if(Si(e))r=al(n,e);else if(yt(e))r=function(s,a){const o=a.rule.ref;if(we(o)){const c=s.subrule++,l=a.arguments.length>0?function(u,h){const d=h.map(f=>Ye(f.value));return f=>{const p={};for(let m=0;m<d.length;m++){const y=u.parameters[m],E=d[m];p[y.name]=E(f)}return p}}(o,a.arguments):()=>({});return u=>s.parser.subrule(c,ol(s,o),a,l(u))}if(pt(o)){const c=s.consume++,l=pi(s,o.name);return()=>s.parser.consume(c,l,a)}if(!o)throw new is(a.$cstNode,`Undefined rule type: ${a.$type}`);or()}(n,e);else if(Ho(e))r=function(s,a){if(a.elements.length===1)return lt(s,a.elements[0]);{const o=[];for(const l of a.elements){const u={ALT:lt(s,l,!0)},h=bn(l);h&&(u.GATE=Ye(h)),o.push(u)}const c=s.or++;return l=>s.parser.alternatives(c,o.map(u=>{const h={ALT:()=>u.ALT(l)},d=u.GATE;return d&&(h.GATE=()=>d(l)),h}))}}(n,e);else if(sc(e))r=function(s,a){if(a.elements.length===1)return lt(s,a.elements[0]);const o=[];for(const d of a.elements){const f={ALT:lt(s,d,!0)},p=bn(d);p&&(f.GATE=Ye(p)),o.push(f)}const c=s.or++,l=(d,f)=>`uGroup_${d}_${f.getRuleStack().join("-")}`,u=d=>s.parser.alternatives(c,o.map((f,p)=>{const m={ALT:()=>!0},y=s.parser;m.ALT=()=>{if(f.ALT(d),!y.isRecording()){const T=l(c,y);y.unorderedGroups.get(T)||y.unorderedGroups.set(T,[]);const v=y.unorderedGroups.get(T);(v==null?void 0:v[p])===void 0&&(v[p]=!0)}};const E=f.GATE;return m.GATE=E?()=>E(d):()=>{const T=y.unorderedGroups.get(l(c,y));return!(T!=null&&T[p])},m})),h=za(s,bn(a),u,"*");return d=>{h(d),s.parser.isRecording()||s.parser.unorderedGroups.delete(l(c,s.parser))}}(n,e);else if(Ni(e))r=function(s,a){const o=a.elements.map(c=>lt(s,c));return c=>o.forEach(l=>l(c))}(n,e);else{if(i=e,!D.isInstance(i,qo))throw new is(e.$cstNode,`Unexpected element type: ${e.$type}`);{const s=n.consume++;r=()=>n.parser.consume(s,nt,e)}}var i;return za(n,t?void 0:bn(e),r,e.cardinality)}function Ye(n){if(e=n,D.isInstance(e,bo)){const t=Ye(n.left),r=Ye(n.right);return i=>t(i)||r(i)}if(function(t){return D.isInstance(t,Lo)}(n)){const t=Ye(n.left),r=Ye(n.right);return i=>t(i)&&r(i)}if(function(t){return D.isInstance(t,Do)}(n)){const t=Ye(n.value);return r=>!t(r)}if(function(t){return D.isInstance(t,Uo)}(n)){const t=n.parameter.ref.name;return r=>r!==void 0&&r[t]===!0}if(function(t){return D.isInstance(t,wo)}(n)){const t=!!n.true;return()=>t}var e;or()}function bn(n){if(Ni(n))return n.guardCondition}function al(n,e,t=e.terminal){if(t){if(yt(t)&&we(t.rule.ref)){const r=n.subrule++;return i=>n.parser.subrule(r,ol(n,t.rule.ref),e,i)}if(yt(t)&&pt(t.rule.ref)){const r=n.consume++,i=pi(n,t.rule.ref.name);return()=>n.parser.consume(r,i,e)}if(gt(t)){const r=n.consume++,i=pi(n,t.value);return()=>n.parser.consume(r,i,e)}throw new Error("Could not build cross reference parser")}{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=mc(e.type.ref),i=r==null?void 0:r.terminal;if(!i)throw new Error("Could not find name assignment for type: "+dr(e.type.ref));return al(n,e,i)}}function za(n,e,t,r){const i=e&&Ye(e);if(!r){if(i){const s=n.or++;return a=>n.parser.alternatives(s,[{ALT:()=>t(a),GATE:()=>i(a)},{ALT:Us(),GATE:()=>!i(a)}])}return t}if(r==="*"){const s=n.many++;return a=>n.parser.many(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}if(r==="+"){const s=n.many++;if(i){const a=n.or++;return o=>n.parser.alternatives(a,[{ALT:()=>n.parser.atLeastOne(s,{DEF:()=>t(o)}),GATE:()=>i(o)},{ALT:Us(),GATE:()=>!i(o)}])}return a=>n.parser.atLeastOne(s,{DEF:()=>t(a)})}if(r==="?"){const s=n.optional++;return a=>n.parser.optional(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}or()}function ol(n,e){const t=function(i,s){if(we(s))return s.name;if(i.ruleNames.has(s))return i.ruleNames.get(s);{let a=s,o=a.$container,c=s.$type;for(;!we(o);)(Ni(o)||Ho(o)||sc(o))&&(c=o.elements.indexOf(a).toString()+":"+c),a=o,o=o.$container;return c=o.name+":"+c,i.ruleNames.set(s,c),c}}(n,e),r=n.rules.get(t);if(!r)throw new Error(`Rule "${t}" not found."`);return r}function pi(n,e){const t=n.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}function Gd(n){const e=function(t){const r=t.Grammar,i=t.parser.Lexer,s=new Pd(t);return sl(r,s,i.definition)}(n);return e.finalize(),e}class cl{buildTokens(e,t){const r=ne(hc(e,!1)),i=this.buildTerminalTokens(r),s=this.buildKeywordTokens(r,i,t);return i.forEach(a=>{const o=a.PATTERN;typeof o=="object"&&o&&"test"in o&&us(o)?s.unshift(a):s.push(a)}),s}buildTerminalTokens(e){return e.filter(pt).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=ri(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r,LINE_BREAKS:!0};return e.hidden&&(i.GROUP=us(t)?ye.SKIPPED:"hidden"),i}requiresCustomPattern(e){return!!e.flags.includes("u")||!(!e.source.includes("?<=")&&!e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(r,i)=>(t.lastIndex=i,t.exec(r))}buildKeywordTokens(e,t,r){return e.filter(we).flatMap(i=>yn(i).filter(gt)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!(r!=null&&r.caseInsensitive)))}buildKeywordToken(e,t,r){return{name:e.value,PATTERN:this.buildKeywordPattern(e,r),LONGER_ALT:this.findLongerAlt(e,t)}}buildKeywordPattern(e,t){return t?new RegExp(function(r){return Array.prototype.map.call(r,i=>/\w/.test(i)?`[${i.toLowerCase()}${i.toUpperCase()}]`:ur(i)).join("")}(e.value)):e.value}findLongerAlt(e,t){return t.reduce((r,i)=>{const s=i==null?void 0:i.PATTERN;return s!=null&&s.source&&Iu("^"+s.source+"$",e.value)&&r.push(i),r},[])}}class ll{convert(e,t){let r=t.grammarSource;if(Si(r)&&(r=function(i){if(i.terminal)return i.terminal;if(i.type.ref){const s=mc(i.type.ref);return s==null?void 0:s.terminal}}(r)),yt(r)){const i=r.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,r){var i;switch(e.name.toUpperCase()){case"INT":return We.convertInt(t);case"STRING":return We.convertString(t);case"ID":return We.convertID(t)}switch((i=function(s){var a,o,c;return pt(s)?(o=(a=s.type)===null||a===void 0?void 0:a.name)!==null&&o!==void 0?o:"string":wi(s)?s.name:(c=Tc(s))!==null&&c!==void 0?c:s.name}(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return We.convertNumber(t);case"boolean":return We.convertBoolean(t);case"bigint":return We.convertBigint(t);case"date":return We.convertDate(t);default:return t}}}var We;(function(n){function e(t){switch(t){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return t}}n.convertString=function(t){let r="";for(let i=1;i<t.length-1;i++){const s=t.charAt(i);s==="\\"?r+=e(t.charAt(++i)):r+=s}return r},n.convertID=function(t){return t.charAt(0)==="^"?t.substring(1):t},n.convertInt=function(t){return parseInt(t)},n.convertBigint=function(t){return BigInt(t)},n.convertDate=function(t){return new Date(t)},n.convertNumber=function(t){return Number(t)},n.convertBoolean=function(t){return t.toLowerCase()==="true"}})(We||(We={}));var pn={},Zn={};let mi;function Yr(){if(mi===void 0)throw new Error("No runtime abstraction layer installed");return mi}Object.defineProperty(Zn,"__esModule",{value:!0}),function(n){n.install=function(e){if(e===void 0)throw new Error("No runtime abstraction layer provided");mi=e}}(Yr||(Yr={})),Zn.default=Yr;var oe={};function Ya(n){return typeof n=="string"||n instanceof String}function qa(n){return Array.isArray(n)}Object.defineProperty(oe,"__esModule",{value:!0}),oe.stringArray=oe.array=oe.func=oe.error=oe.number=oe.string=oe.boolean=void 0,oe.boolean=function(n){return n===!0||n===!1},oe.string=Ya,oe.number=function(n){return typeof n=="number"||n instanceof Number},oe.error=function(n){return n instanceof Error},oe.func=function(n){return typeof n=="function"},oe.array=qa,oe.stringArray=function(n){return qa(n)&&n.every(e=>Ya(e))};var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.Emitter=Nt.Event=void 0;const Bd=Zn;var Xa,J;(function(n){const e={dispose(){}};n.None=function(){return e}})(Xa||(Nt.Event=Xa={}));class jd{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e){if(this._contexts[i]===t)return this._callbacks.splice(i,1),void this._contexts.splice(i,1);r=!0}if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,a=r.length;s<a;s++)try{t.push(r[s].apply(i[s],e))}catch(o){(0,Bd.default)().console.error(o)}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class er{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new jd),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=er._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}Nt.Emitter=er,er._noop=function(){},Object.defineProperty(pn,"__esModule",{value:!0});var gi=pn.CancellationTokenSource=J=pn.CancellationToken=void 0;const Kd=Zn,Vd=oe,yi=Nt;var tr;(function(n){n.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:yi.Event.None}),n.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:yi.Event.None}),n.is=function(e){const t=e;return t&&(t===n.None||t===n.Cancelled||Vd.boolean(t.isCancellationRequested)&&!!t.onCancellationRequested)}})(tr||(J=pn.CancellationToken=tr={}));const Hd=Object.freeze(function(n,e){const t=(0,Kd.default)().timer.setTimeout(n.bind(e),0);return{dispose(){t.dispose()}}});class Qa{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Hd:(this._emitter||(this._emitter=new yi.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}gi=pn.CancellationTokenSource=class{get token(){return this._token||(this._token=new Qa),this._token}cancel(){this._token?this._token.cancel():this._token=tr.Cancelled}dispose(){this._token?this._token instanceof Qa&&this._token.dispose():this._token=tr.None}};let Ja=0;const nr=Symbol("OperationCancelled");function Bi(n){return n===nr}async function Pe(n){if(n===J.None)return;const e=Date.now();if(e-Ja>=10&&(Ja=e,await new Promise(t=>{typeof setImmediate>"u"?setTimeout(t,0):setImmediate(t)})),n.isCancellationRequested)throw nr}class ji{constructor(){this.promise=new Promise((e,t)=>{this.resolve=r=>(e(r),this),this.reject=r=>(t(r),this)})}}class mn{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(const r of e)if(mn.isIncremental(r)){const i=hl(r.range),s=this.offsetAt(i.start),a=this.offsetAt(i.end);this._content=this._content.substring(0,s)+r.text+this._content.substring(a,this._content.length);const o=Math.max(i.start.line,0),c=Math.max(i.end.line,0);let l=this._lineOffsets;const u=Za(r.text,!1,s);if(c-o===u.length)for(let d=0,f=u.length;d<f;d++)l[d+o+1]=u[d];else u.length<1e4?l.splice(o+1,c-o,...u):this._lineOffsets=l=l.slice(0,o+1).concat(u,l.slice(c+1));const h=r.text.length-(a-s);if(h!==0)for(let d=o+1+u.length,f=l.length;d<f;d++)l[d]=l[d]+h}else{if(!mn.isFull(r))throw new Error("Unknown change event received");this._content=r.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Za(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let r=0,i=t.length;if(i===0)return{line:0,character:e};for(;r<i;){const a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}const s=r-1;return{line:s,character:(e=this.ensureBeforeEOL(e,t[s]))-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const r=t[e.line];if(e.character<=0)return r;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(r+e.character,i);return this.ensureBeforeEOL(s,r)}ensureBeforeEOL(e,t){for(;e>t&&dl(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}}var Ti,ul;function vi(n,e){if(n.length<=1)return n;const t=n.length/2|0,r=n.slice(0,t),i=n.slice(t);vi(r,e),vi(i,e);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;){const c=e(r[s],i[a]);n[o++]=c<=0?r[s++]:i[a++]}for(;s<r.length;)n[o++]=r[s++];for(;a<i.length;)n[o++]=i[a++];return n}function Za(n,e,t=0){const r=e?[t]:[];for(let i=0;i<n.length;i++){const s=n.charCodeAt(i);dl(s)&&(s===13&&i+1<n.length&&n.charCodeAt(i+1)===10&&i++,r.push(t+i+1))}return r}function dl(n){return n===13||n===10}function hl(n){const e=n.start,t=n.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:n}function Wd(n){const e=hl(n.range);return e!==n.range?{newText:n.newText,range:e}:n}(function(n){n.create=function(e,t,r,i){return new mn(e,t,r,i)},n.update=function(e,t,r){if(e instanceof mn)return e.update(t,r),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},n.applyEdits=function(e,t){const r=e.getText(),i=vi(t.map(Wd),(o,c)=>{const l=o.range.start.line-c.range.start.line;return l===0?o.range.start.character-c.range.start.character:l});let s=0;const a=[];for(const o of i){const c=e.offsetAt(o.range.start);if(c<s)throw new Error("Overlapping edit");c>s&&a.push(r.substring(s,c)),o.newText.length&&a.push(o.newText),s=e.offsetAt(o.range.end)}return a.push(r.substr(s)),a.join("")}})(Ti||(Ti={})),(()=>{var n={470:i=>{function s(c){if(typeof c!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(c))}function a(c,l){for(var u,h="",d=0,f=-1,p=0,m=0;m<=c.length;++m){if(m<c.length)u=c.charCodeAt(m);else{if(u===47)break;u=47}if(u===47){if(!(f===m-1||p===1))if(f!==m-1&&p===2){if(h.length<2||d!==2||h.charCodeAt(h.length-1)!==46||h.charCodeAt(h.length-2)!==46){if(h.length>2){var y=h.lastIndexOf("/");if(y!==h.length-1){y===-1?(h="",d=0):d=(h=h.slice(0,y)).length-1-h.lastIndexOf("/"),f=m,p=0;continue}}else if(h.length===2||h.length===1){h="",d=0,f=m,p=0;continue}}l&&(h.length>0?h+="/..":h="..",d=2)}else h.length>0?h+="/"+c.slice(f+1,m):h=c.slice(f+1,m),d=m-f-1;f=m,p=0}else u===46&&p!==-1?++p:p=-1}return h}var o={resolve:function(){for(var c,l="",u=!1,h=arguments.length-1;h>=-1&&!u;h--){var d;h>=0?d=arguments[h]:(c===void 0&&(c=process.cwd()),d=c),s(d),d.length!==0&&(l=d+"/"+l,u=d.charCodeAt(0)===47)}return l=a(l,!u),u?l.length>0?"/"+l:"/":l.length>0?l:"."},normalize:function(c){if(s(c),c.length===0)return".";var l=c.charCodeAt(0)===47,u=c.charCodeAt(c.length-1)===47;return(c=a(c,!l)).length!==0||l||(c="."),c.length>0&&u&&(c+="/"),l?"/"+c:c},isAbsolute:function(c){return s(c),c.length>0&&c.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var c,l=0;l<arguments.length;++l){var u=arguments[l];s(u),u.length>0&&(c===void 0?c=u:c+="/"+u)}return c===void 0?".":o.normalize(c)},relative:function(c,l){if(s(c),s(l),c===l||(c=o.resolve(c))===(l=o.resolve(l)))return"";for(var u=1;u<c.length&&c.charCodeAt(u)===47;++u);for(var h=c.length,d=h-u,f=1;f<l.length&&l.charCodeAt(f)===47;++f);for(var p=l.length-f,m=d<p?d:p,y=-1,E=0;E<=m;++E){if(E===m){if(p>m){if(l.charCodeAt(f+E)===47)return l.slice(f+E+1);if(E===0)return l.slice(f+E)}else d>m&&(c.charCodeAt(u+E)===47?y=E:E===0&&(y=0));break}var T=c.charCodeAt(u+E);if(T!==l.charCodeAt(f+E))break;T===47&&(y=E)}var v="";for(E=u+y+1;E<=h;++E)E!==h&&c.charCodeAt(E)!==47||(v.length===0?v+="..":v+="/..");return v.length>0?v+l.slice(f+y):(f+=y,l.charCodeAt(f)===47&&++f,l.slice(f))},_makeLong:function(c){return c},dirname:function(c){if(s(c),c.length===0)return".";for(var l=c.charCodeAt(0),u=l===47,h=-1,d=!0,f=c.length-1;f>=1;--f)if((l=c.charCodeAt(f))===47){if(!d){h=f;break}}else d=!1;return h===-1?u?"/":".":u&&h===1?"//":c.slice(0,h)},basename:function(c,l){if(l!==void 0&&typeof l!="string")throw new TypeError('"ext" argument must be a string');s(c);var u,h=0,d=-1,f=!0;if(l!==void 0&&l.length>0&&l.length<=c.length){if(l.length===c.length&&l===c)return"";var p=l.length-1,m=-1;for(u=c.length-1;u>=0;--u){var y=c.charCodeAt(u);if(y===47){if(!f){h=u+1;break}}else m===-1&&(f=!1,m=u+1),p>=0&&(y===l.charCodeAt(p)?--p==-1&&(d=u):(p=-1,d=m))}return h===d?d=m:d===-1&&(d=c.length),c.slice(h,d)}for(u=c.length-1;u>=0;--u)if(c.charCodeAt(u)===47){if(!f){h=u+1;break}}else d===-1&&(f=!1,d=u+1);return d===-1?"":c.slice(h,d)},extname:function(c){s(c);for(var l=-1,u=0,h=-1,d=!0,f=0,p=c.length-1;p>=0;--p){var m=c.charCodeAt(p);if(m!==47)h===-1&&(d=!1,h=p+1),m===46?l===-1?l=p:f!==1&&(f=1):l!==-1&&(f=-1);else if(!d){u=p+1;break}}return l===-1||h===-1||f===0||f===1&&l===h-1&&l===u+1?"":c.slice(l,h)},format:function(c){if(c===null||typeof c!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof c);return function(l,u){var h=u.dir||u.root,d=u.base||(u.name||"")+(u.ext||"");return h?h===u.root?h+d:h+"/"+d:d}(0,c)},parse:function(c){s(c);var l={root:"",dir:"",base:"",ext:"",name:""};if(c.length===0)return l;var u,h=c.charCodeAt(0),d=h===47;d?(l.root="/",u=1):u=0;for(var f=-1,p=0,m=-1,y=!0,E=c.length-1,T=0;E>=u;--E)if((h=c.charCodeAt(E))!==47)m===-1&&(y=!1,m=E+1),h===46?f===-1?f=E:T!==1&&(T=1):f!==-1&&(T=-1);else if(!y){p=E+1;break}return f===-1||m===-1||T===0||T===1&&f===m-1&&f===p+1?m!==-1&&(l.base=l.name=p===0&&d?c.slice(1,m):c.slice(p,m)):(p===0&&d?(l.name=c.slice(1,f),l.base=c.slice(1,m)):(l.name=c.slice(p,f),l.base=c.slice(p,m)),l.ext=c.slice(f,m)),p>0?l.dir=c.slice(0,p-1):d&&(l.dir="/"),l},sep:"/",delimiter:":",win32:null,posix:null};o.posix=o,i.exports=o}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var a=e[i]={exports:{}};return n[i](a,a.exports,t),a.exports}t.d=(i,s)=>{for(var a in s)t.o(s,a)&&!t.o(i,a)&&Object.defineProperty(i,a,{enumerable:!0,get:s[a]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;t.r(r),t.d(r,{URI:()=>d,Utils:()=>ge}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,a=/^\//,o=/^\/\//;function c(A,R){if(!A.scheme&&R)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${A.authority}", path: "${A.path}", query: "${A.query}", fragment: "${A.fragment}"}`);if(A.scheme&&!s.test(A.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(A.path){if(A.authority){if(!a.test(A.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(A.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const l="",u="/",h=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{constructor(R,k,S,O,b,L=!1){Je(this,"scheme");Je(this,"authority");Je(this,"path");Je(this,"query");Je(this,"fragment");typeof R=="object"?(this.scheme=R.scheme||l,this.authority=R.authority||l,this.path=R.path||l,this.query=R.query||l,this.fragment=R.fragment||l):(this.scheme=function(Ae,ae){return Ae||ae?Ae:"file"}(R,L),this.authority=k||l,this.path=function(Ae,ae){switch(Ae){case"https":case"http":case"file":ae?ae[0]!==u&&(ae=u+ae):ae=u}return ae}(this.scheme,S||l),this.query=O||l,this.fragment=b||l,c(this,L))}static isUri(R){return R instanceof d||!!R&&typeof R.authority=="string"&&typeof R.fragment=="string"&&typeof R.path=="string"&&typeof R.query=="string"&&typeof R.scheme=="string"&&typeof R.fsPath=="string"&&typeof R.with=="function"&&typeof R.toString=="function"}get fsPath(){return T(this)}with(R){if(!R)return this;let{scheme:k,authority:S,path:O,query:b,fragment:L}=R;return k===void 0?k=this.scheme:k===null&&(k=l),S===void 0?S=this.authority:S===null&&(S=l),O===void 0?O=this.path:O===null&&(O=l),b===void 0?b=this.query:b===null&&(b=l),L===void 0?L=this.fragment:L===null&&(L=l),k===this.scheme&&S===this.authority&&O===this.path&&b===this.query&&L===this.fragment?this:new p(k,S,O,b,L)}static parse(R,k=!1){const S=h.exec(R);return S?new p(S[2]||l,U(S[4]||l),U(S[5]||l),U(S[7]||l),U(S[9]||l),k):new p(l,l,l,l,l)}static file(R){let k=l;if(i&&(R=R.replace(/\\/g,u)),R[0]===u&&R[1]===u){const S=R.indexOf(u,2);S===-1?(k=R.substring(2),R=u):(k=R.substring(2,S),R=R.substring(S)||u)}return new p("file",k,R,l,l)}static from(R){const k=new p(R.scheme,R.authority,R.path,R.query,R.fragment);return c(k,!0),k}toString(R=!1){return v(this,R)}toJSON(){return this}static revive(R){if(R){if(R instanceof d)return R;{const k=new p(R);return k._formatted=R.external,k._fsPath=R._sep===f?R.fsPath:null,k}}return R}}const f=i?1:void 0;class p extends d{constructor(){super(...arguments);Je(this,"_formatted",null);Je(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=T(this)),this._fsPath}toString(k=!1){return k?v(this,!0):(this._formatted||(this._formatted=v(this,!1)),this._formatted)}toJSON(){const k={$mid:1};return this._fsPath&&(k.fsPath=this._fsPath,k._sep=f),this._formatted&&(k.external=this._formatted),this.path&&(k.path=this.path),this.scheme&&(k.scheme=this.scheme),this.authority&&(k.authority=this.authority),this.query&&(k.query=this.query),this.fragment&&(k.fragment=this.fragment),k}}const m={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function y(A,R,k){let S,O=-1;for(let b=0;b<A.length;b++){const L=A.charCodeAt(b);if(L>=97&&L<=122||L>=65&&L<=90||L>=48&&L<=57||L===45||L===46||L===95||L===126||R&&L===47||k&&L===91||k&&L===93||k&&L===58)O!==-1&&(S+=encodeURIComponent(A.substring(O,b)),O=-1),S!==void 0&&(S+=A.charAt(b));else{S===void 0&&(S=A.substr(0,b));const Ae=m[L];Ae!==void 0?(O!==-1&&(S+=encodeURIComponent(A.substring(O,b)),O=-1),S+=Ae):O===-1&&(O=b)}}return O!==-1&&(S+=encodeURIComponent(A.substring(O))),S!==void 0?S:A}function E(A){let R;for(let k=0;k<A.length;k++){const S=A.charCodeAt(k);S===35||S===63?(R===void 0&&(R=A.substr(0,k)),R+=m[S]):R!==void 0&&(R+=A[k])}return R!==void 0?R:A}function T(A,R){let k;return k=A.authority&&A.path.length>1&&A.scheme==="file"?`//${A.authority}${A.path}`:A.path.charCodeAt(0)===47&&(A.path.charCodeAt(1)>=65&&A.path.charCodeAt(1)<=90||A.path.charCodeAt(1)>=97&&A.path.charCodeAt(1)<=122)&&A.path.charCodeAt(2)===58?A.path[1].toLowerCase()+A.path.substr(2):A.path,i&&(k=k.replace(/\//g,"\\")),k}function v(A,R){const k=R?E:y;let S="",{scheme:O,authority:b,path:L,query:Ae,fragment:ae}=A;if(O&&(S+=O,S+=":"),(b||O==="file")&&(S+=u,S+=u),b){let V=b.indexOf("@");if(V!==-1){const at=b.substr(0,V);b=b.substr(V+1),V=at.lastIndexOf(":"),V===-1?S+=k(at,!1,!1):(S+=k(at.substr(0,V),!1,!1),S+=":",S+=k(at.substr(V+1),!1,!0)),S+="@"}b=b.toLowerCase(),V=b.lastIndexOf(":"),V===-1?S+=k(b,!1,!0):(S+=k(b.substr(0,V),!1,!0),S+=b.substr(V))}if(L){if(L.length>=3&&L.charCodeAt(0)===47&&L.charCodeAt(2)===58){const V=L.charCodeAt(1);V>=65&&V<=90&&(L=`/${String.fromCharCode(V+32)}:${L.substr(3)}`)}else if(L.length>=2&&L.charCodeAt(1)===58){const V=L.charCodeAt(0);V>=65&&V<=90&&(L=`${String.fromCharCode(V+32)}:${L.substr(2)}`)}S+=k(L,!0,!1)}return Ae&&(S+="?",S+=k(Ae,!1,!1)),ae&&(S+="#",S+=R?ae:y(ae,!1,!1)),S}function x(A){try{return decodeURIComponent(A)}catch{return A.length>3?A.substr(0,3)+x(A.substr(3)):A}}const P=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function U(A){return A.match(P)?A.replace(P,R=>x(R)):A}var B=t(470);const Z=B.posix||B,se="/";var ge;(function(A){A.joinPath=function(R,...k){return R.with({path:Z.join(R.path,...k)})},A.resolvePath=function(R,...k){let S=R.path,O=!1;S[0]!==se&&(S=se+S,O=!0);let b=Z.resolve(S,...k);return O&&b[0]===se&&!R.authority&&(b=b.substring(1)),R.with({path:b})},A.dirname=function(R){if(R.path.length===0||R.path===se)return R;let k=Z.dirname(R.path);return k.length===1&&k.charCodeAt(0)===46&&(k=""),R.with({path:k})},A.basename=function(R){return Z.basename(R.path)},A.extname=function(R){return Z.extname(R.path)}})(ge||(ge={}))})(),ul=r})();const{URI:Wt,Utils:an}=ul;var st,j,rr,tt,ir;(function(n){n.basename=an.basename,n.dirname=an.dirname,n.extname=an.extname,n.joinPath=an.joinPath,n.resolvePath=an.resolvePath,n.equals=function(e,t){return(e==null?void 0:e.toString())===(t==null?void 0:t.toString())},n.relative=function(e,t){const r=typeof e=="string"?e:e.path,i=typeof t=="string"?t:t.path,s=r.split("/").filter(c=>c.length>0),a=i.split("/").filter(c=>c.length>0);let o=0;for(;o<s.length&&s[o]===a[o];o++);return"../".repeat(s.length-o)+a.slice(o).join("/")}})(st||(st={})),function(n){n[n.Changed=0]="Changed",n[n.Parsed=1]="Parsed",n[n.IndexedContent=2]="IndexedContent",n[n.ComputedScopes=3]="ComputedScopes",n[n.Linked=4]="Linked",n[n.IndexedReferences=5]="IndexedReferences",n[n.Validated=6]="Validated"}(j||(j={}));class zd{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=J.None){const r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return t=t??Wt.parse(e.uri),r?this.createAsync(t,e,r):this.create(t,e)}fromString(e,t,r){return r?this.createAsync(t,e,r):this.create(t,e)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t){if(typeof t=="string"){const r=this.parse(e,t);return this.createLangiumDocument(r,e,void 0,t)}if("$model"in t){const r={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(r,e)}{const r=this.parse(e,t.getText());return this.createLangiumDocument(r,e,t)}}async createAsync(e,t,r){if(typeof t=="string"){const i=await this.parseAsync(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}{const i=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,r,i){let s;if(r)s={parseResult:e,uri:t,state:j.Parsed,references:[],textDocument:r};else{const a=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:j.Parsed,references:[],get textDocument(){return a()}}}return e.value.$document=s,s}async update(e,t){var r,i;const s=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,a=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),o=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{const c=this.createTextDocumentGetter(e.uri,o);Object.defineProperty(e,"textDocument",{get:c})}return s!==o&&(e.parseResult=await this.parseAsync(e.uri,o,t),e.parseResult.value.$document=e),e.state=j.Parsed,e}parse(e,t){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){const r=this.serviceRegistry;let i;return()=>i??(i=Ti.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,t??""))}}class Yd{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory}get all(){return ne(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r),r)}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=j.Changed,r.precomputedScopes=void 0,r.references=[],r.diagnostics=void 0),r}deleteDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=j.Changed,this.documentMap.delete(t)),r}}class qd{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=J.None){for(const r of It(e.parseResult.value))await Pe(t),lc(r).forEach(i=>this.doLink(i,e))}doLink(e,t){const r=e.reference;if(r._ref===void 0)try{const i=this.getCandidate(e);if(On(i))r._ref=i;else if(r._nodeDescription=i,this.langiumDocuments().hasDocument(i.documentUri)){const s=this.loadAstNode(i);r._ref=s??this.createLinkingError(e,i)}}catch(i){r._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${r.$refText}': ${i}`})}t.references.push(r)}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const t=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return t??this.createLinkingError(e)}buildReference(e,t,r,i){const s=this,a={$refNode:r,$refText:i,get ref(){var o,c;if(le(this._ref))return this._ref;if(typeof(c=this._nodeDescription)=="object"&&c!==null&&typeof c.name=="string"&&typeof c.type=="string"&&typeof c.path=="string"){const l=s.loadAstNode(this._nodeDescription);this._ref=l??s.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){const l=s.getLinkedNode({reference:a,container:e,property:t});if(l.error&&Be(e).state<j.ComputedScopes)return;this._ref=(o=l.node)!==null&&o!==void 0?o:l.error,this._nodeDescription=l.descr}return le(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return On(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){try{const t=this.getCandidate(e);if(On(t))return{error:t};const r=this.loadAstNode(t);return r?{node:r,descr:t}:{descr:t,error:this.createLinkingError(e,t)}}catch(t){return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${t}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);return t?this.astNodeLocator.getAstNode(t.parseResult.value,e.path):void 0}createLinkingError(e,t){const r=Be(e.container);r.state<j.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}}class Xd{getName(e){if(function(t){return typeof t.name=="string"}(e))return e.name}getNameNode(e){return pc(e.$cstNode,"name")}}class Qd{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=function(i){var s;const a=i.astNode;for(;a===((s=i.container)===null||s===void 0?void 0:s.astNode);){const o=lr(i.grammarSource,mt);if(o)return o;i=i.container}}(e),r=e.astNode;if(t&&r){const i=r[t.feature];if(Ge(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(Ge(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(r){const i=this.nameProvider.getNameNode(r);if(i&&(i===e||function(s,a){for(;s.container;)if((s=s.container)===a)return!0;return!1}(e,i)))return r}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t!=null&&t.$cstNode){const r=this.nameProvider.getNameNode(t);return r??t.$cstNode}}findReferences(e,t){const r=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&r.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>st.equals(s.sourceUri,t.documentUri))),r.push(...i),ne(r)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const r=Be(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:i,targetUri:r.uri,targetPath:i,segment:Bn(t),local:!0}}}}class Ki{constructor(e){if(this.map=new Map,e)for(const[t,r]of e)this.add(t,r)}get size(){return Zr.sum(ne(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const i=r.indexOf(t);if(i>=0)return r.length===1?this.map.delete(e):r.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const r=this.map.get(e);return!!r&&r.indexOf(t)>=0}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(i=>e(i,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return ne(this.map.entries()).flatMap(([e,t])=>t.map(r=>[e,r]))}keys(){return ne(this.map.keys())}values(){return ne(this.map.values()).flat()}entriesGroupedByKey(){return ne(this.map.entries())}}class eo{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0&&(this.map.delete(e),this.inverse.delete(t),!0)}}class Jd{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=J.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=Ci,i=J.None){const s=[];this.exportNode(e,s,t);for(const a of r(e))await Pe(i),this.exportNode(a,s,t);return s}exportNode(e,t,r){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,r))}async computeLocalScopes(e,t=J.None){const r=e.parseResult.value,i=new Ki;for(const s of yn(r))await Pe(t),this.processNode(s,e,i);return i}processNode(e,t,r){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&r.add(i,this.descriptions.createDescription(e,s,t))}}}class to{constructor(e,t,r){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0&&i}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){return(this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e))||(this.outerScope?this.outerScope.getElement(e):void 0)}}class Zd{constructor(e,t,r){var i;this.elements=new Map,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0&&i;for(const s of e){const a=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(a,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e;return this.elements.get(t)||(this.outerScope?this.outerScope.getElement(e):void 0)}getAllElements(){let e=ne(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class fl{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class eh extends fl{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const r=t();return this.cache.set(e,r),r}}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class th extends fl{constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(r){const s=r();return i.set(t,s),s}}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class nh extends eh{constructor(e){super(),this.onDispose(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class rh{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new nh(e.shared)}getScope(e){const t=[],r=this.reflection.getReferenceType(e),i=Be(e.container).precomputedScopes;if(i){let a=e.container;do{const o=i.get(a);o.length>0&&t.push(ne(o).filter(c=>this.reflection.isSubtype(c.type,r))),a=a.$container}while(a)}let s=this.getGlobalScope(r,e);for(let a=t.length-1;a>=0;a--)s=this.createScope(t[a],s);return s}createScope(e,t,r){return new to(ne(e),t,r)}createScopeForNodes(e,t,r){const i=ne(e).map(s=>{const a=this.nameProvider.getName(s);if(a)return this.descriptions.createDescription(s,a)}).nonNullable();return new to(i,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new Zd(this.indexManager.allElements(e)))}}function no(n){return typeof n=="object"&&!!n&&("$ref"in n||"$error"in n)}class ih{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t={}){const r=t==null?void 0:t.replacer,i=(a,o)=>this.replacer(a,o,t),s=r?(a,o)=>r(a,o,i):i;try{return this.currentDocument=Be(e),JSON.stringify(e,s,t==null?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t={}){const r=JSON.parse(e);return this.linkNode(r,r,t),r}replacer(e,t,{refText:r,sourceText:i,textRegions:s,comments:a,uriConverter:o}){var c,l,u,h;if(!this.ignoreProperties.has(e)){if(Ge(t)){const d=t.ref,f=r?t.$refText:void 0;if(d){const p=Be(d);let m="";return this.currentDocument&&this.currentDocument!==p&&(m=o?o(p.uri,t):p.uri.toString()),{$ref:`${m}#${this.astNodeLocator.getAstNodePath(d)}`,$refText:f}}return{$error:(l=(c=t.error)===null||c===void 0?void 0:c.message)!==null&&l!==void 0?l:"Could not resolve reference",$refText:f}}if(le(t)){let d;if(s&&(d=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),e&&!t.$document||!(d!=null&&d.$textRegion)||(d.$textRegion.documentURI=(u=this.currentDocument)===null||u===void 0?void 0:u.uri.toString())),i&&!e&&(d!=null||(d=Object.assign({},t)),d.$sourceText=(h=t.$cstNode)===null||h===void 0?void 0:h.text),a){d!=null||(d=Object.assign({},t));const f=this.commentProvider.getComment(t);f&&(d.$comment=f.replace(/\r/g,""))}return d??t}return t}}addAstNodeRegionWithAssignmentsTo(e){const t=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=(e.$textRegion=t(e.$cstNode)).assignments={};return Object.keys(e).filter(i=>!i.startsWith("$")).forEach(i=>{const s=function(a,o){return a&&o?$i(a,o,a.astNode,!0):[]}(e.$cstNode,i).map(t);s.length!==0&&(r[i]=s)}),e}}linkNode(e,t,r,i,s,a){for(const[c,l]of Object.entries(e))if(Array.isArray(l))for(let u=0;u<l.length;u++){const h=l[u];no(h)?l[u]=this.reviveReference(e,c,t,h,r):le(h)&&this.linkNode(h,t,r,e,c,u)}else no(l)?e[c]=this.reviveReference(e,c,t,l,r):le(l)&&this.linkNode(l,t,r,e,c);const o=e;o.$container=i,o.$containerProperty=s,o.$containerIndex=a}reviveReference(e,t,r,i,s){let a=i.$refText,o=i.$error;if(i.$ref){const c=this.getRefNode(r,i.$ref,s.uriConverter);if(le(c))return a||(a=this.nameProvider.getName(c)),{$refText:a??"",ref:c};o=c}if(o){const c={$refText:a??""};return c.error={container:e,property:t,message:o,reference:c},c}}getRefNode(e,t,r){try{const i=t.indexOf("#");if(i===0)return this.astNodeLocator.getAstNode(e,t.substring(1))||"Could not resolve path: "+t;if(i<0){const c=r?r(t):Wt.parse(t),l=this.langiumDocuments.getDocument(c);return l?l.parseResult.value:"Could not find document for URI: "+t}const s=r?r(t.substring(0,i)):Wt.parse(t.substring(0,i)),a=this.langiumDocuments.getDocument(s);return a?i===t.length-1?a.parseResult.value:this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(i+1))||"Could not resolve URI: "+t:"Could not find document for URI: "+t}catch(i){return String(i)}}}class sh{register(e){if(this.singleton||this.map){if(!this.map&&(this.map={},this.singleton)){for(const t of this.singleton.LanguageMetaData.fileExtensions)this.map[t]=this.singleton;this.singleton=void 0}for(const t of e.LanguageMetaData.fileExtensions)this.map[t]!==void 0&&this.map[t]!==e&&console.warn(`The file extension ${t} is used by multiple languages. It is now assigned to '${e.LanguageMetaData.languageId}'.`),this.map[t]=e}else this.singleton=e}getServices(e){if(this.singleton!==void 0)return this.singleton;if(this.map===void 0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const t=st.extname(e),r=this.map[t];if(!r)throw new Error(`The service registry contains no services for the extension '${t}'.`);return r}get all(){return this.singleton!==void 0?[this.singleton]:this.map!==void 0?Object.values(this.map):[]}}function ro(n){return{code:n}}(function(n){n.all=["fast","slow","built-in"]})(rr||(rr={}));class ah{constructor(e){this.entries=new Ki,this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const a=s;if(Array.isArray(a))for(const o of a){const c={check:this.wrapValidationException(o,t),category:r};this.addEntry(i,c)}else if(typeof a=="function"){const o={check:this.wrapValidationException(a,t),category:r};this.addEntry(i,o)}}}wrapValidationException(e,t){return async(r,i,s)=>{try{await e.call(t,r,i,s)}catch(a){if(Bi(a))throw a;console.error("An error occurred during validation:",a);const o=a instanceof Error?a.message:String(a);a instanceof Error&&a.stack&&console.error(a.stack),i("error","An error occurred during validation: "+o,{node:r})}}}addEntry(e,t){if(e!=="AstNode")for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,t);else this.entries.add("AstNode",t)}getChecks(e,t){let r=ne(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(i=>t.includes(i.category))),r.map(i=>i.check)}}class oh{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=J.None){const i=e.parseResult,s=[];if(await Pe(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===tt.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===tt.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===tt.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,r))}catch(a){if(Bi(a))throw a;console.error("An error occurred during validation:",a)}return await Pe(r),s}processLexingErrors(e,t,r){for(const i of e.lexerErrors){const s={severity:qr("error"),range:{start:{line:i.line-1,character:i.column-1},end:{line:i.line-1,character:i.column+i.length-1}},message:i.message,data:ro(tt.LexingError),source:this.getSource()};t.push(s)}}processParsingErrors(e,t,r){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const a=i.previousToken;if(isNaN(a.startOffset)){const o={line:0,character:0};s={start:o,end:o}}else{const o={line:a.endLine-1,character:a.endColumn};s={start:o,end:o}}}}else s=ti(i.token);if(s){const a={severity:qr("error"),range:s,message:i.message,data:ro(tt.ParsingError),source:this.getSource()};t.push(a)}}}processLinkingErrors(e,t,r){for(const i of e.references){const s=i.error;if(s){const a={node:s.container,property:s.property,index:s.index,data:{code:tt.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,a))}}}async validateAst(e,t,r=J.None){const i=[],s=(a,o,c)=>{i.push(this.toDiagnostic(a,o,c))};return await Promise.all(It(e).map(async a=>{await Pe(r);const o=this.validationRegistry.getChecks(a.$type,t.categories);for(const c of o)await c(a,s,r)})),i}toDiagnostic(e,t,r){return{message:t,range:ch(r),severity:qr(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function ch(n){if(n.range)return n.range;let e;return typeof n.property=="string"?e=pc(n.node.$cstNode,n.property,n.index):typeof n.keyword=="string"&&(e=Su(n.node.$cstNode,n.keyword,n.index)),e!=null||(e=n.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function qr(n){switch(n){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+n)}}(function(n){n.LexingError="lexing-error",n.ParsingError="parsing-error",n.LinkingError="linking-error"})(tt||(tt={}));class lh{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r=Be(e)){t!=null||(t=this.nameProvider.getName(e));const i=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${i} has no name.`);let s;const a=()=>{var o;return s??(s=Bn((o=this.nameProvider.getNameNode(e))!==null&&o!==void 0?o:e.$cstNode))};return{node:e,name:t,get nameSegment(){return a()},selectionSegment:Bn(e.$cstNode),type:e.$type,documentUri:r.uri,path:i}}}class uh{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=J.None){const r=[],i=e.parseResult.value;for(const s of It(i))await Pe(t),lc(s).filter(a=>!On(a)).forEach(a=>{const o=this.createDescription(a);o&&r.push(o)});return r}createDescription(e){const t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;const i=Be(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:Bn(r),local:st.equals(t.documentUri,i)}}}class dh{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((r,i)=>{if(!r||i.length===0)return r;const s=i.indexOf(this.indexSeparator);if(s>0){const a=i.substring(0,s),o=parseInt(i.substring(s+1)),c=r[a];return c==null?void 0:c[o]}return r[i]},e)}}class hh{constructor(e){this._ready=new ji,this.settings={},this.workspaceConfig=!1,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=(r=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&r!==void 0&&r}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,r[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{this.updateSectionConfiguration(t,e.settings[t])})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}}(function(n){n.create=function(e){return{dispose:async()=>await e()}}})(ir||(ir={}));class fh{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new Ki,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=j.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=J.None){var i,s;for(const a of e){const o=a.uri.toString();if(a.state===j.Validated){if(typeof t.validation=="boolean"&&t.validation)a.state=j.IndexedReferences,a.diagnostics=void 0,this.buildState.delete(o);else if(typeof t.validation=="object"){const c=this.buildState.get(o),l=(i=c==null?void 0:c.result)===null||i===void 0?void 0:i.validationChecks;if(l){const u=((s=t.validation.categories)!==null&&s!==void 0?s:rr.all).filter(h=>!l.includes(h));u.length>0&&(this.buildState.set(o,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:u})},result:c.result}),a.state=j.IndexedReferences)}}}else this.buildState.delete(o)}this.currentState=j.Changed,await this.emitUpdate(e.map(a=>a.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=J.None){this.currentState=j.Changed;for(const a of t)this.langiumDocuments.deleteDocument(a),this.buildState.delete(a.toString()),this.indexManager.remove(a);for(const a of e){if(!this.langiumDocuments.invalidateDocument(a)){const o=this.langiumDocumentFactory.fromModel({$type:"INVALID"},a);o.state=j.Changed,this.langiumDocuments.addDocument(o)}this.buildState.delete(a.toString())}const i=ne(e).concat(t).map(a=>a.toString()).toSet();this.langiumDocuments.all.filter(a=>!i.has(a.uri.toString())&&this.shouldRelink(a,i)).forEach(a=>{this.serviceRegistry.getServices(a.uri).references.Linker.unlink(a),a.state=Math.min(a.state,j.ComputedScopes),a.diagnostics=void 0}),await this.emitUpdate(e,t),await Pe(r);const s=this.langiumDocuments.all.filter(a=>{var o;return a.state<j.Linked||!(!((o=this.buildState.get(a.uri.toString()))===null||o===void 0)&&o.completed)}).toArray();await this.buildDocuments(s,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}shouldRelink(e,t){return!!e.references.some(r=>r.error!==void 0)||this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),ir.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,j.Parsed,r,s=>this.langiumDocumentFactory.update(s,r)),await this.runCancelable(e,j.IndexedContent,r,s=>this.indexManager.updateContent(s,r)),await this.runCancelable(e,j.ComputedScopes,r,async s=>{const a=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await a.computeLocalScopes(s,r)}),await this.runCancelable(e,j.Linked,r,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,r)),await this.runCancelable(e,j.IndexedReferences,r,s=>this.indexManager.updateReferences(s,r));const i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,j.Validated,r,s=>this.validate(s,r));for(const s of e){const a=this.buildState.get(s.uri.toString());a&&(a.completed=!0)}}prepareBuild(e,t){for(const r of e){const i=r.uri.toString(),s=this.buildState.get(i);s&&!s.completed||this.buildState.set(i,{completed:!1,options:t,result:s==null?void 0:s.result})}}async runCancelable(e,t,r,i){const s=e.filter(a=>a.state<t);for(const a of s)await Pe(r),await i(a),a.state=t;await this.notifyBuildPhase(s,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),ir.create(()=>{this.buildPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let i;if(t&&"path"in t?i=t:r=t,r!=null||(r=J.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(nr):new Promise((s,a)=>{const o=this.onBuildPhase(e,()=>{if(o.dispose(),c.dispose(),i){const l=this.langiumDocuments.getDocument(i);s(l==null?void 0:l.uri)}else s(void 0)}),c=r.onCancellationRequested(()=>{o.dispose(),c.dispose(),a(nr)})})}async notifyBuildPhase(e,t,r){if(e.length===0)return;const i=this.buildPhaseListeners.get(t);for(const s of i)await Pe(r),await s(e,r)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var r,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,o=typeof a=="object"?a:void 0,c=await s.validateDocument(e,o,t);e.diagnostics?e.diagnostics.push(...c):e.diagnostics=c;const l=this.buildState.get(e.uri.toString());if(l){(r=l.result)!==null&&r!==void 0||(l.result={});const u=(i=o==null?void 0:o.categories)!==null&&i!==void 0?i:rr.all;l.result.validationChecks?l.result.validationChecks.push(...u):l.result.validationChecks=[...u]}}getBuildOptions(e){var t,r;return(r=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&r!==void 0?r:{}}}class ph{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new th,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const r=Be(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(a=>{st.equals(a.targetUri,r)&&a.targetPath===t&&i.push(a)})}),ne(i)}allElements(e,t){let r=ne(this.symbolIndex.keys());return t&&(r=r.filter(i=>!t||t.has(i))),r.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(a=>this.astReflection.isSubtype(a.type,t))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=J.None){const r=this.serviceRegistry.getServices(e.uri),i=await r.references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=J.None){const r=this.serviceRegistry.getServices(e.uri),i=await r.workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){const r=this.referenceIndex.get(e.uri.toString());return!!r&&r.some(i=>!i.local&&t.has(i.targetUri.toString()))}}class mh{constructor(e){this.initialBuildOptions={},this._ready=new ji,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],t)})}async initializeWorkspace(e,t=J.None){const r=await this.performStartup(e);await Pe(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),r=[],i=s=>{r.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)};return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return Wt.parse(e.uri)}async traverseFolder(e,t,r,i){const s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async a=>{if(this.includeEntry(e,a,r)){if(a.isDirectory)await this.traverseFolder(e,a.uri,r,i);else if(a.isFile){const o=await this.langiumDocuments.getOrCreateDocument(a.uri);i(o)}}}))}includeEntry(e,t,r){const i=st.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=st.extname(t.uri);return r.includes(s)}return!1}}class gh{constructor(e){const t=e.parser.TokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const r=io(t)?Object.values(t):t;this.chevrotainLexer=new ye(r,{positionTracking:"full"})}get definition(){return this.tokenTypes}tokenize(e){var t;const r=this.chevrotainLexer.tokenize(e);return{tokens:r.tokens,errors:r.errors,hidden:(t=r.groups.hidden)!==null&&t!==void 0?t:[]}}toTokenTypeDictionary(e){if(io(e))return e;const t=pl(e)?Object.values(e.modes).flat():e,r={};return t.forEach(i=>r[i.name]=i),r}}function pl(n){return n&&"modes"in n&&"defaultMode"in n}function io(n){return!function(e){return Array.isArray(e)&&(e.length===0||"name"in e[0])}(n)&&!pl(n)}function yh(n,e,t){let r,i;typeof n=="string"?(i=e,r=t):(i=n.range.start,r=e),i||(i=M.create(0,0));const s=function(a){var o,c,l;const u=[];let h=a.position.line,d=a.position.character;for(let f=0;f<a.lines.length;f++){const p=f===0,m=f===a.lines.length-1;let y=a.lines[f],E=0;if(p&&a.options.start){const T=(o=a.options.start)===null||o===void 0?void 0:o.exec(y);T&&(E=T.index+T[0].length)}else{const T=(c=a.options.line)===null||c===void 0?void 0:c.exec(y);T&&(E=T.index+T[0].length)}if(m){const T=(l=a.options.end)===null||l===void 0?void 0:l.exec(y);T&&(y=y.substring(0,T.index))}if(y=y.substring(0,Ah(y)),Ei(y,E)>=y.length){if(u.length>0){const T=M.create(h,d);u.push({type:"break",content:"",range:_.create(T,T)})}}else{so.lastIndex=E;const T=so.exec(y);if(T){const v=T[0],x=T[1],P=M.create(h,d+E),U=M.create(h,d+E+v.length);u.push({type:"tag",content:x,range:_.create(P,U)}),E+=v.length,E=Ei(y,E)}if(E<y.length){const v=y.substring(E),x=Array.from(v.matchAll(Th));u.push(...vh(x,v,h,d+E))}}h++,d=0}return u.length>0&&u[u.length-1].type==="break"?u.slice(0,-1):u}({lines:ml(n),position:i,options:Vi(r)});return function(a){var o,c,l,u;const h=M.create(a.position.line,a.position.character);if(a.tokens.length===0)return new ao([],_.create(h,h));const d=[];for(;a.index<a.tokens.length;){const m=kh(a,d[d.length-1]);m&&d.push(m)}const f=(c=(o=d[0])===null||o===void 0?void 0:o.range.start)!==null&&c!==void 0?c:h,p=(u=(l=d[d.length-1])===null||l===void 0?void 0:l.range.end)!==null&&u!==void 0?u:h;return new ao(d,_.create(f,p))}({index:0,tokens:s,position:i})}function ml(n){let e="";return e=typeof n=="string"?n:n.text,e.split(Au)}const so=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,Th=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function vh(n,e,t,r){const i=[];if(n.length===0){const s=M.create(t,r),a=M.create(t,r+e.length);i.push({type:"text",content:e,range:_.create(s,a)})}else{let s=0;for(const o of n){const c=o.index,l=e.substring(s,c);l.length>0&&i.push({type:"text",content:e.substring(s,c),range:_.create(M.create(t,s+r),M.create(t,c+r))});let u=l.length+1;const h=o[1];if(i.push({type:"inline-tag",content:h,range:_.create(M.create(t,s+u+r),M.create(t,s+u+h.length+r))}),u+=h.length,o.length===4){u+=o[2].length;const d=o[3];i.push({type:"text",content:d,range:_.create(M.create(t,s+u+r),M.create(t,s+u+d.length+r))})}else i.push({type:"text",content:"",range:_.create(M.create(t,s+u+r),M.create(t,s+u+r))});s=c+o[0].length}const a=e.substring(s);a.length>0&&i.push({type:"text",content:a,range:_.create(M.create(t,s+r),M.create(t,s+r+a.length))})}return i}const Eh=/\S/,Rh=/\s*$/;function Ei(n,e){const t=n.substring(e).match(Eh);return t?e+t.index:n.length}function Ah(n){const e=n.match(Rh);if(e&&typeof e.index=="number")return e.index}function kh(n,e){const t=n.tokens[n.index];return t.type==="tag"?yl(n,!1):t.type==="text"||t.type==="inline-tag"?gl(n):(function(r,i){if(i){const s=new vl("",r.range);"inlines"in i?i.inlines.push(s):i.content.inlines.push(s)}}(t,e),void n.index++)}function gl(n){let e=n.tokens[n.index];const t=e;let r=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(xh(n)),r=e,e=n.tokens[n.index];return new Ri(i,_.create(t.range.start,r.range.end))}function xh(n){return n.tokens[n.index].type==="inline-tag"?yl(n,!0):Tl(n)}function yl(n,e){const t=n.tokens[n.index++],r=t.content.substring(1),i=n.tokens[n.index];if((i==null?void 0:i.type)==="text"){if(e){const s=Tl(n);return new Qr(r,new Ri([s],s.range),e,_.create(t.range.start,s.range.end))}{const s=gl(n);return new Qr(r,s,e,_.create(t.range.start,s.range.end))}}{const s=t.range;return new Qr(r,new Ri([],s),e,s)}}function Tl(n){const e=n.tokens[n.index++];return new vl(e.content,e.range)}function Vi(n){if(!n)return Vi({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:r}=n;return{start:Xr(e,!0),end:Xr(t,!1),line:Xr(r,!0)}}function Xr(n,e){if(typeof n=="string"||typeof n=="object"){const t=typeof n=="string"?ur(n):n.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}return n}class ao{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const r=t.toString();e+=oo(e)+r}return e.trim()}toMarkdown(e){let t="";for(const r of this.elements)if(t.length===0)t=r.toMarkdown(e);else{const i=r.toMarkdown(e);t+=oo(t)+i}return t.trim()}}class Qr{constructor(e,t,r,i){this.name=e,this.content=t,this.inline=r,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,r;return(r=(t=e==null?void 0:e.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=function(a,o,c){var l,u;if(a==="linkplain"||a==="linkcode"||a==="link"){const h=o.indexOf(" ");let d=o;if(h>0){const p=Ei(o,h);d=o.substring(p),o=o.substring(0,h)}return(a==="linkcode"||a==="link"&&c.link==="code")&&(d=`\`${d}\``),(u=(l=c.renderLink)===null||l===void 0?void 0:l.call(c,o,d))!==null&&u!==void 0?u:function(p,m){try{return Wt.parse(p,!0),`[${m}](${p})`}catch{return p}}(o,d)}}(this.name,t,e??{});if(typeof s=="string")return s}let r="";(e==null?void 0:e.tag)==="italic"||(e==null?void 0:e.tag)===void 0?r="*":(e==null?void 0:e.tag)==="bold"?r="**":(e==null?void 0:e.tag)==="bold-italic"&&(r="***");let i=`${r}@${this.name}${r}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}}class Ri{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const r=this.inlines[t],i=this.inlines[t+1];e+=r.toString(),i&&i.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){const i=this.inlines[r],s=this.inlines[r+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}}class vl{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function oo(n){return n.endsWith(`
`)?`
`:`

`}class Ih{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&function(r,i){const s=Vi(i),a=ml(r);if(a.length===0)return!1;const o=a[0],c=a[a.length-1],l=s.start,u=s.end;return!!(l!=null&&l.exec(o))&&!!(u!=null&&u.exec(c))}(t))return yh(t).toMarkdown({renderLink:(r,i)=>this.documentationLinkRenderer(e,r,i),renderTag:r=>this.documentationTagRenderer(e,r)})}documentationLinkRenderer(e,t,r){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const a=s.nameSegment.range.start.line+1,o=s.nameSegment.range.start.character+1;return`[${r}](${s.documentUri.with({fragment:`L${a},${o}`}).toString()})`}}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const r=Be(e).precomputedScopes;if(!r)return;let i=e;do{const s=r.get(i).find(a=>a.name===t);if(s)return s;i=i.$container}while(i)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(r=>r.name===t)}}class Sh{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return function(r){return typeof r.$comment=="string"}(e)?e.$comment:(t=Tu(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class Nh{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e){return Promise.resolve(this.syncParser.parse(e))}}class Ch{constructor(){this.previousTokenSource=new gi,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=new gi;return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r){const i=new ji,s={action:t,deferred:i,cancellationToken:r??J.None};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else{if(!(this.readQueue.length>0))return;e.push(...this.readQueue.splice(0,this.readQueue.length))}this.done=!1,await Promise.all(e.map(async({action:t,deferred:r,cancellationToken:i})=>{try{const s=await Promise.resolve().then(()=>t(i));r.resolve(s)}catch(s){Bi(s)?r.resolve(void 0):r.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class $h{constructor(e){this.grammarElementIdMap=new eo,this.tokenTypeIdMap=new eo,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors.map(t=>Object.assign({},t)),parserErrors:e.parserErrors.map(t=>Object.assign({},t)),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}createDehyrationContext(e){const t=new Map,r=new Map;for(const i of It(e))t.set(i,{});if(e.$cstNode)for(const i of ei(e.$cstNode))r.set(i,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)le(o)?a.push(this.dehydrateAstNode(o,t)):Ge(o)?a.push(this.dehydrateReference(o,t)):a.push(o)}else le(s)?r[i]=this.dehydrateAstNode(s,t):Ge(s)?r[i]=this.dehydrateReference(s,t):s!==void 0&&(r[i]=s);return r}dehydrateReference(e,t){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){const r=t.cstNodes.get(e);return Co(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),Vt(e)?r.content=e.content.map(i=>this.dehydrateCstNode(i,t)):No(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){const t=new Map,r=new Map;for(const s of It(e))t.set(s,{});let i;if(e.$cstNode)for(const s of ei(e.$cstNode)){let a;"fullText"in s?(a=new tl(s.fullText),i=a):"content"in s?a=new Fi:"tokenType"in s&&(a=this.hydrateCstLeafNode(s)),a&&(r.set(s,a),a.root=i)}return{astNodes:t,cstNodes:r}}hydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)le(o)?a.push(this.setParent(this.hydrateAstNode(o,t),r)):Ge(o)?a.push(this.hydrateReference(o,r,i,t)):a.push(o)}else le(s)?r[i]=this.setParent(this.hydrateAstNode(s,t),r):Ge(s)?r[i]=this.hydrateReference(s,r,i,t):s!==void 0&&(r[i]=s);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,i){return this.linker.buildReference(t,r,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),Vt(i))for(const s of e.content){const a=this.hydrateCstNode(s,t,r++);i.content.push(a)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),r=e.offset,i=e.length,s=e.startLine,a=e.startColumn,o=e.endLine,c=e.endColumn,l=e.hidden;return new hi(r,i,{start:{line:s,character:a},end:{line:o,character:c}},t,l)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap();const t=this.grammarElementIdMap.getKey(e);if(t)return t;throw new Error("Invalid grammar element id: "+e)}createGrammarElementIdMap(){let e=0;for(const r of It(this.grammar))t=r,D.isInstance(t,$o)&&this.grammarElementIdMap.set(r,e++);var t}}function Xt(n){return{documentation:{CommentProvider:e=>new Sh(e),DocumentationProvider:e=>new Ih(e)},parser:{AsyncParser:e=>new Nh(e),GrammarConfig:e=>function(t){const r=[],i=t.Grammar;for(const a of i.rules)pt(a)&&(s=a).hidden&&!ri(s).test(" ")&&xu(ri(a))&&r.push(a.name);var s;return{multilineCommentRules:r,nameRegexp:yu}}(e),LangiumParser:e=>Gd(e),CompletionParser:e=>function(t){const r=t.Grammar,i=t.parser.Lexer,s=new Dd(t);return sl(r,s,i.definition),s.finalize(),s}(e),ValueConverter:()=>new ll,TokenBuilder:()=>new cl,Lexer:e=>new gh(e),ParserErrorMessageProvider:()=>new il},workspace:{AstNodeLocator:()=>new dh,AstNodeDescriptionProvider:e=>new lh(e),ReferenceDescriptionProvider:e=>new uh(e)},references:{Linker:e=>new qd(e),NameProvider:()=>new Xd,ScopeProvider:e=>new rh(e),ScopeComputation:e=>new Jd(e),References:e=>new Qd(e)},serializer:{Hydrator:e=>new $h(e),JsonSerializer:e=>new ih(e)},validation:{DocumentValidator:e=>new oh(e),ValidationRegistry:e=>new ah(e)},shared:()=>n.shared}}function Qt(n){return{ServiceRegistry:()=>new sh,workspace:{LangiumDocuments:e=>new Yd(e),LangiumDocumentFactory:e=>new zd(e),DocumentBuilder:e=>new fh(e),IndexManager:e=>new ph(e),WorkspaceManager:e=>new mh(e),FileSystemProvider:e=>n.fileSystemProvider(e),WorkspaceLock:()=>new Ch,ConfigurationProvider:e=>new hh(e)}}}var co;function Le(n,e,t,r,i,s,a,o,c){return El([n,e,t,r,i,s,a,o,c].reduce(sr,{}))}(function(n){n.merge=(e,t)=>sr(sr({},e),t)})(co||(co={}));const lo=Symbol("isProxy");function El(n,e){const t=new Proxy({},{deleteProperty:()=>!1,get:(r,i)=>ho(r,i,n,e||t),getOwnPropertyDescriptor:(r,i)=>(ho(r,i,n,e||t),Object.getOwnPropertyDescriptor(r,i)),has:(r,i)=>i in n,ownKeys:()=>[...Reflect.ownKeys(n),lo]});return t[lo]=!0,t}const uo=Symbol();function ho(n,e,t,r){if(e in n){if(n[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:n[e]});if(n[e]===uo)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. See https://langium.org/docs/configuration-services/#resolving-cyclic-dependencies');return n[e]}if(e in t){const i=t[e];n[e]=uo;try{n[e]=typeof i=="function"?i(r):El(i,r)}catch(s){throw n[e]=s instanceof Error?s:void 0,s}return n[e]}}function sr(n,e){if(e){for(const[t,r]of Object.entries(e))if(r!==void 0){const i=n[t];n[t]=i!==null&&r!==null&&typeof i=="object"&&typeof r=="object"?sr(i,r):r}}return n}class wh{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const Jt={fileSystemProvider:()=>new wh},Lh={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},bh={AstReflection:()=>new cc};function vn(n){var e;const t=function(){const i=Le(Qt(Jt),bh),s=Le(Xt({shared:i}),Lh);return i.ServiceRegistry.register(s),s}(),r=t.serializer.JsonSerializer.deserialize(n);return t.shared.workspace.LangiumDocumentFactory.fromModel(r,Wt.parse(`memory://${(e=r.name)!==null&&e!==void 0?e:"grammar"}.langium`)),r}var Oh=Object.defineProperty,N=(n,e)=>Oh(n,"name",{value:e,configurable:!0});N(function(n){return Ue.isInstance(n,"Architecture")},"isArchitecture");var Rl="Branch";N(function(n){return Ue.isInstance(n,Rl)},"isBranch");var Al="Commit";N(function(n){return Ue.isInstance(n,Al)},"isCommit");N(function(n){return Ue.isInstance(n,"Common")},"isCommon");var kl="GitGraph";N(function(n){return Ue.isInstance(n,kl)},"isGitGraph");N(function(n){return Ue.isInstance(n,"Info")},"isInfo");var xl="Merge";N(function(n){return Ue.isInstance(n,xl)},"isMerge");N(function(n){return Ue.isInstance(n,"Packet")},"isPacket");N(function(n){return Ue.isInstance(n,"PacketBlock")},"isPacketBlock");N(function(n){return Ue.isInstance(n,"Pie")},"isPie");N(function(n){return Ue.isInstance(n,"PieSection")},"isPieSection");var fo,po,mo,go,yo,Ct,Il=(Ct=class extends So{getAllTypes(){return["Architecture","Branch","Checkout","CherryPicking","Commit","Common","Direction","Edge","GitGraph","Group","Info","Junction","Merge","Packet","PacketBlock","Pie","PieSection","Service","Statement"]}computeIsSubtype(e,t){switch(e){case Rl:case"Checkout":case"CherryPicking":case Al:case xl:return this.isSubtype("Statement",t);case"Direction":return this.isSubtype(kl,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;throw new Error(`${t} is not a valid reference id.`)}getTypeMetaData(e){switch(e){case"Architecture":return{name:"Architecture",properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case"Branch":return{name:"Branch",properties:[{name:"name"},{name:"order"}]};case"Checkout":return{name:"Checkout",properties:[{name:"branch"}]};case"CherryPicking":return{name:"CherryPicking",properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case"Commit":return{name:"Commit",properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Common":return{name:"Common",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Edge":return{name:"Edge",properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case"GitGraph":return{name:"GitGraph",properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case"Group":return{name:"Group",properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case"Info":return{name:"Info",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Junction":return{name:"Junction",properties:[{name:"id"},{name:"in"}]};case"Merge":return{name:"Merge",properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Packet":return{name:"Packet",properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case"PacketBlock":return{name:"PacketBlock",properties:[{name:"end"},{name:"label"},{name:"start"}]};case"Pie":return{name:"Pie",properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case"PieSection":return{name:"PieSection",properties:[{name:"label"},{name:"value"}]};case"Service":return{name:"Service",properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case"Direction":return{name:"Direction",properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},N(Ct,"MermaidAstReflection"),Ct),Ue=new Il,_h=N(()=>fo??(fo=vn('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","name":"Info","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),Ph=N(()=>po??(po=vn(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","name":"Packet","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Mh=N(()=>mo??(mo=vn('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","name":"Pie","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),Dh=N(()=>go??(go=vn('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","name":"Architecture","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","fragment":true,"definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"LeftPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"RightPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Arrow","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),Uh=N(()=>yo??(yo=vn(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","name":"GitGraph","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),Fh={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},Gh={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},Bh={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},jh={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},Kh={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},En={AstReflection:N(()=>new Il,"AstReflection")},Vh={Grammar:N(()=>_h(),"Grammar"),LanguageMetaData:N(()=>Fh,"LanguageMetaData"),parser:{}},Hh={Grammar:N(()=>Ph(),"Grammar"),LanguageMetaData:N(()=>Gh,"LanguageMetaData"),parser:{}},Wh={Grammar:N(()=>Mh(),"Grammar"),LanguageMetaData:N(()=>Bh,"LanguageMetaData"),parser:{}},zh={Grammar:N(()=>Dh(),"Grammar"),LanguageMetaData:N(()=>jh,"LanguageMetaData"),parser:{}},Yh={Grammar:N(()=>Uh(),"Grammar"),LanguageMetaData:N(()=>Kh,"LanguageMetaData"),parser:{}},qh={ACC_DESCR:/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ACC_TITLE:/accTitle[\t ]*:([^\n\r]*)/,TITLE:/title([\t ][^\n\r]*|)/},$t,Hi=($t=class extends ll{runConverter(e,t,r){let i=this.runCommonConverter(e,t,r);return i===void 0&&(i=this.runCustomConverter(e,t,r)),i===void 0?super.runConverter(e,t,r):i}runCommonConverter(e,t,r){const i=qh[e.name];if(i===void 0)return;const s=i.exec(t);return s!==null?s[1]!==void 0?s[1].trim().replace(/[\t ]{2,}/gm," "):s[2]!==void 0?s[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`):void 0:void 0}},N($t,"AbstractMermaidValueConverter"),$t),wt,Wi=(wt=class extends Hi{runCustomConverter(e,t,r){}},N(wt,"CommonValueConverter"),wt),Lt,Zt=(Lt=class extends cl{constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){const i=super.buildKeywordTokens(e,t,r);return i.forEach(s=>{this.keywords.has(s.name)&&s.PATTERN!==void 0&&(s.PATTERN=new RegExp(s.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},N(Lt,"AbstractMermaidTokenBuilder"),Lt),bt;bt=class extends Zt{},N(bt,"CommonTokenBuilder");var Ot,Xh=(Ot=class extends Zt{constructor(){super(["gitGraph"])}},N(Ot,"GitGraphTokenBuilder"),Ot),Sl={parser:{TokenBuilder:N(()=>new Xh,"TokenBuilder"),ValueConverter:N(()=>new Wi,"ValueConverter")}};function Nl(n=Jt){const e=Le(Qt(n),En),t=Le(Xt({shared:e}),Yh,Sl);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}N(Nl,"createGitGraphServices");var _t,Qh=(_t=class extends Zt{constructor(){super(["info","showInfo"])}},N(_t,"InfoTokenBuilder"),_t),Cl={parser:{TokenBuilder:N(()=>new Qh,"TokenBuilder"),ValueConverter:N(()=>new Wi,"ValueConverter")}};function $l(n=Jt){const e=Le(Qt(n),En),t=Le(Xt({shared:e}),Vh,Cl);return e.ServiceRegistry.register(t),{shared:e,Info:t}}N($l,"createInfoServices");var Pt,Jh=(Pt=class extends Zt{constructor(){super(["packet-beta"])}},N(Pt,"PacketTokenBuilder"),Pt),wl={parser:{TokenBuilder:N(()=>new Jh,"TokenBuilder"),ValueConverter:N(()=>new Wi,"ValueConverter")}};function Ll(n=Jt){const e=Le(Qt(n),En),t=Le(Xt({shared:e}),Hh,wl);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}N(Ll,"createPacketServices");var Mt,Zh=(Mt=class extends Zt{constructor(){super(["pie","showData"])}},N(Mt,"PieTokenBuilder"),Mt),Dt,ef=(Dt=class extends Hi{runCustomConverter(e,t,r){if(e.name==="PIE_SECTION_LABEL")return t.replace(/"/g,"").trim()}},N(Dt,"PieValueConverter"),Dt),bl={parser:{TokenBuilder:N(()=>new Zh,"TokenBuilder"),ValueConverter:N(()=>new ef,"ValueConverter")}};function Ol(n=Jt){const e=Le(Qt(n),En),t=Le(Xt({shared:e}),Wh,bl);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}N(Ol,"createPieServices");var Ut,tf=(Ut=class extends Zt{constructor(){super(["architecture"])}},N(Ut,"ArchitectureTokenBuilder"),Ut),Ft,nf=(Ft=class extends Hi{runCustomConverter(e,t,r){return e.name==="ARCH_ICON"?t.replace(/[()]/g,"").trim():e.name==="ARCH_TEXT_ICON"?t.replace(/["()]/g,""):e.name==="ARCH_TITLE"?t.replace(/[[\]]/g,"").trim():void 0}},N(Ft,"ArchitectureValueConverter"),Ft),_l={parser:{TokenBuilder:N(()=>new tf,"TokenBuilder"),ValueConverter:N(()=>new nf,"ValueConverter")}};function Pl(n=Jt){const e=Le(Qt(n),En),t=Le(Xt({shared:e}),zh,_l);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}N(Pl,"createArchitectureServices");var ut={},rf={info:N(async()=>{const{createInfoServices:n}=await en(async()=>{const{createInfoServices:t}=await Promise.resolve().then(()=>of);return{createInfoServices:t}},void 0,import.meta.url),e=n().Info.parser.LangiumParser;ut.info=e},"info"),packet:N(async()=>{const{createPacketServices:n}=await en(async()=>{const{createPacketServices:t}=await Promise.resolve().then(()=>cf);return{createPacketServices:t}},void 0,import.meta.url),e=n().Packet.parser.LangiumParser;ut.packet=e},"packet"),pie:N(async()=>{const{createPieServices:n}=await en(async()=>{const{createPieServices:t}=await Promise.resolve().then(()=>lf);return{createPieServices:t}},void 0,import.meta.url),e=n().Pie.parser.LangiumParser;ut.pie=e},"pie"),architecture:N(async()=>{const{createArchitectureServices:n}=await en(async()=>{const{createArchitectureServices:t}=await Promise.resolve().then(()=>uf);return{createArchitectureServices:t}},void 0,import.meta.url),e=n().Architecture.parser.LangiumParser;ut.architecture=e},"architecture"),gitGraph:N(async()=>{const{createGitGraphServices:n}=await en(async()=>{const{createGitGraphServices:t}=await Promise.resolve().then(()=>df);return{createGitGraphServices:t}},void 0,import.meta.url),e=n().GitGraph.parser.LangiumParser;ut.gitGraph=e},"gitGraph")};async function sf(n,e){const t=rf[n];if(!t)throw new Error(`Unknown diagram type: ${n}`);ut[n]||await t();const r=ut[n].parse(e);if(r.lexerErrors.length>0||r.parserErrors.length>0)throw new af(r);return r.value}N(sf,"parse");var Gt,af=(Gt=class extends Error{constructor(e){super(`Parsing failed: ${e.lexerErrors.map(t=>t.message).join(`
`)} ${e.parserErrors.map(t=>t.message).join(`
`)}`),this.result=e}},N(Gt,"MermaidParseError"),Gt);const of=Object.freeze(Object.defineProperty({__proto__:null,InfoModule:Cl,createInfoServices:$l},Symbol.toStringTag,{value:"Module"})),cf=Object.freeze(Object.defineProperty({__proto__:null,PacketModule:wl,createPacketServices:Ll},Symbol.toStringTag,{value:"Module"})),lf=Object.freeze(Object.defineProperty({__proto__:null,PieModule:bl,createPieServices:Ol},Symbol.toStringTag,{value:"Module"})),uf=Object.freeze(Object.defineProperty({__proto__:null,ArchitectureModule:_l,createArchitectureServices:Pl},Symbol.toStringTag,{value:"Module"})),df=Object.freeze(Object.defineProperty({__proto__:null,GitGraphModule:Sl,createGitGraphServices:Nl},Symbol.toStringTag,{value:"Module"}));export{sf as p};
