var wa=Object.defineProperty;var Ca=(r,e,t)=>e in r?wa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var Oe=(r,e,t)=>Ca(r,typeof e!="symbol"?e+"":e,t);import{f as bt,b as d,w as ct,aD as Rr,aA as mr,A as Ye,C as w,D as Xe,J as m,H as Y,_ as n,L as g,t as h,M as p,a3 as xe,Q as Ce,ab as Ct,W as ze,aF as ls,T as ue,V as L,P as mt,I as Qe,X as $e,Y as v,m as G,K as y,$ as lt,R as Qt,S as ba,F as nt,G as Se,a4 as st,a2 as Nt,z as $a,l as us,Z as Sa,a as js,a1 as ka,N as wt,al as ar,a7 as xa,b4 as Er,ay as gr,v as _s,u as ds,b5 as Ir,aE as lr,a5 as Ws,B as xr,ak as Kr,a$ as zr,a6 as Yr,b6 as Ma,az as Aa}from"./SpinnerAugment-AffdR7--.js";import"./design-system-init-BpU1o6o4.js";import{W as Ve,d as _t,e as ot,I as ts,b as Ta,c as at,i as qt,a as Na,h as nr,f as Lr,g as Ra,H as dr}from"./IconButtonAugment-DVt24OaC.js";import{M as Qr,R as Or}from"./message-broker-Bv_1VsFe.js";import{G as Ea,S as Ia,b as za,N as La,L as Oa,c as ut,M as ns,D as Za,F as Pa,f as Xr,R as Fa,d as Zr,T as ea,e as ja,C as Da,P as cr,g as Ua,h as Va,i as qa,A as Ha,j as Ba,k as Ga,U as Pr}from"./user-BlpcIo5U.js";import{Q as ur,a2 as Zt,O as Ue,i as hr,t as fr,T as Xt,D as We,a3 as Ja,C as ta,E as sa,a4 as qs,f as pr,A as Fr,g as Wa,h as Ka,R as Ya}from"./index-CnLsnTY6.js";import{G as Qa,D as Hs,C as Xa,P as hs,B as Mr,a as en,T as yr,b as ra,S as _r,c as tn}from"./download-BWD0PqSl.js";import{o as Gs}from"./keypress-DD1aQVr0.js";import{V as aa}from"./VSCodeCodicon-CM9n-Tfg.js";import{A as sn}from"./async-messaging-DXXiLgz5.js";import{c as wr}from"./svelte-component-BzMfvILK.js";import{k as rn,C as na,a as an,T as Js}from"./CollapseButtonAugment-DgGSnbBS.js";import{D as nn}from"./Drawer-jmAZQpgu.js";import{b as ia,T as Ft,a as ps,p as on}from"./CardAugment-CB88N7dm.js";import{B as Ke}from"./ButtonAugment-K-zrKZyw.js";import{C as ks}from"./CalloutAugment-4ajbBCm_.js";import{E as ln}from"./ellipsis-BHLqUIzX.js";import{P as dn}from"./pen-to-square-ChHviosp.js";import{T as oa,S as cn}from"./TextAreaAugment-BXNDUf24.js";import{C as Ar}from"./chevron-down-BBAM6A1q.js";import{M as un}from"./index-ZbZe59K6.js";import{M as hn}from"./MarkdownEditor-eRXt2w4J.js";import{R as pn}from"./RulesModeSelector-DVF13ARD.js";import{M as la}from"./ModalAugment-C7SE8sNA.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CB_5BS9R.js";import"./isObjectLike-D6uePTe3.js";import"./BaseTextInput-IcL3sG2L.js";import"./index-oHUUsc-1.js";import"./index-C5DcjNTh.js";const ws={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class vn{constructor(e,t=ws){Oe(this,"timerId",null);Oe(this,"currentMS");Oe(this,"step",0);Oe(this,"params");this.callback=e;const s={...t};s.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),s.maxMS=ws.maxMS),s.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),s.initialMS=ws.initialMS),s.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),s.mult=ws.mult),s.maxSteps!==void 0&&s.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),s.maxSteps=ws.maxSteps),this.params=s,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}var mn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function gn(r){var e=mn();d(r,e)}var fn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function yn(r){var e=fn();d(r,e)}var _n=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function Ut(r){var e=_n();d(r,e)}var Pe,Cr,wn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function Cn(r){var e=wn();d(r,e)}class xs{constructor(e){Oe(this,"configs",ct([]));Oe(this,"pollingManager");Oe(this,"_enableDebugFeatures",ct(!1));Oe(this,"_settingsComponentSupported",ct({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Oe(this,"_enableAgentMode",ct(!1));Oe(this,"_enableAgentSwarmMode",ct(!1));Oe(this,"_enableNativeRemoteMcp",ct(!0));Oe(this,"_hasEverUsedRemoteAgent",ct(!1));Oe(this,"_enableInitialOrientation",ct(!1));Oe(this,"_userTier",ct("unknown"));Oe(this,"_guidelines",ct({}));this._host=e,this.pollingManager=new vn(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,s=e.oauthUrl;if(e.identifier.hostName===ur.remoteToolHost){let a=e.identifier.toolId;switch(typeof a=="string"&&/^\d+$/.test(a)&&(a=Number(a)),a){case Zt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Qa,requiresAuthentication:t,authUrl:s};case Zt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Oa,requiresAuthentication:t,authUrl:s};case Zt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:yn,requiresAuthentication:t,authUrl:s};case Zt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:La,requiresAuthentication:t,authUrl:s};case Zt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:gn,requiresAuthentication:t,authUrl:s};case Zt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:za,requiresAuthentication:t,authUrl:s};case Zt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Ia,requiresAuthentication:t,authUrl:s};case Zt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Ea,requiresAuthentication:t,authUrl:s};case Zt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled RemoteToolId: ${a}`)}}else if(e.identifier.hostName===ur.localToolHost){const a=e.identifier.toolId;switch(a){case Ue.readFile:case Ue.editFile:case Ue.saveFile:case Ue.launchProcess:case Ue.killProcess:case Ue.readProcess:case Ue.writeProcess:case Ue.listProcesses:case Ue.waitProcess:case Ue.openBrowser:case Ue.clarify:case Ue.onboardingSubAgent:case Ue.strReplaceEditor:case Ue.remember:case Ue.diagnostics:case Ue.setupScript:case Ue.readTerminal:case Ue.gitCommitRetrieval:case Ue.memoryRetrieval:case Ue.startWorkerAgent:case Ue.readWorkerState:case Ue.waitForWorkerAgent:case Ue.sendInstructionToWorkerAgent:case Ue.stopWorkerAgent:case Ue.deleteWorkerAgent:case Ue.readWorkerAgentEdits:case Ue.applyWorkerAgentEdits:case Ue.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Ut,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled LocalToolType: ${a}`)}}else if(e.identifier.hostName===ur.sidecarToolHost){const a=e.identifier.toolId;switch(a){case ut.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:ns,requiresAuthentication:t,authUrl:s};case ut.shell:return{displayName:"Shell",description:"Shell",icon:ns,requiresAuthentication:t,authUrl:s};case ut.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:ns,requiresAuthentication:t,authUrl:s};case ut.view:return{displayName:"File View",description:"File Viewer",icon:ns,requiresAuthentication:t,authUrl:s};case ut.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:ns,requiresAuthentication:t,authUrl:s};case ut.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Cn,requiresAuthentication:t,authUrl:s};case ut.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Pa,requiresAuthentication:t,authUrl:s};case ut.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:Ut,requiresAuthentication:t,authUrl:s};case ut.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Za,requiresAuthentication:t,authUrl:s};case ut.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:ns,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled SidecarToolType: ${a}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:s}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case Ve.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case Ve.toolConfigDefinitionsResponse:return this.configs.update(s=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(a=>{const l=s.find(i=>i.name===a.name);return l?{...l,displayName:a.displayName,description:a.description,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,isConfigured:a.isConfigured,toolApprovalConfig:a.toolApprovalConfig}:a})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(s=>{const a=this.transformToolDisplay(s),l=t.find(o=>o.name===s.definition.name),i=(l==null?void 0:l.isConfigured)??!a.requiresAuthentication;return{config:(l==null?void 0:l.config)??{},configString:JSON.stringify((l==null?void 0:l.config)??{},null,2),isConfigured:i,name:s.definition.name.toString(),displayName:a.displayName,description:a.description,identifier:s.identifier,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:s.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Rr(this.configs,e=>{const t=e.filter(a=>this.isDisplayableTool(a)),s=new Map;for(const a of t)s.set(a.displayName,a);return Array.from(s.values()).sort((a,l)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[a.displayName]||o,u=i[l.displayName]||o;return c<o&&u<o||c===o&&u===o?c!==u?c-u:a.displayName.localeCompare(l.displayName):c-u})})}getPretendNativeToolDefs(){return Rr(this.configs,e=>this.getEnableNativeRemoteMcp()?Xr(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:Ve.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:Ve.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(s=>s.map(a=>a.identifier.toolId===e.toolId&&a.identifier.hostName===e.hostName?{...a,toolApprovalConfig:t}:a))}getSettingsComponentSupported(){return this._settingsComponentSupported}}Oe(xs,"key","toolConfigModel");(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(a=>typeof e[e[a]]!="number"),s={};for(const a of t)s[a]=e[a];return r.objectValues(s)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},r.find=(e,t)=>{for(const s of e)if(t(s))return s},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(s=>typeof s=="string"?`'${s}'`:s).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(Pe||(Pe={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(Cr||(Cr={}));const K=Pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Pt=r=>{switch(typeof r){case"undefined":return K.undefined;case"string":return K.string;case"number":return isNaN(r)?K.nan:K.number;case"boolean":return K.boolean;case"function":return K.function;case"bigint":return K.bigint;case"symbol":return K.symbol;case"object":return Array.isArray(r)?K.array:r===null?K.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?K.promise:typeof Map<"u"&&r instanceof Map?K.map:typeof Set<"u"&&r instanceof Set?K.set:typeof Date<"u"&&r instanceof Date?K.date:K.object;default:return K.unknown}},b=Pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class yt extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(l){return l.message},s={_errors:[]},a=l=>{for(const i of l.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return a(this),s}static assert(e){if(!(e instanceof yt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}yt.create=r=>new yt(r);const vs=(r,e)=>{let t;switch(r.code){case b.invalid_type:t=r.received===K.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case b.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,Pe.jsonStringifyReplacer)}`;break;case b.unrecognized_keys:t=`Unrecognized key(s) in object: ${Pe.joinValues(r.keys,", ")}`;break;case b.invalid_union:t="Invalid input";break;case b.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${Pe.joinValues(r.options)}`;break;case b.invalid_enum_value:t=`Invalid enum value. Expected ${Pe.joinValues(r.options)}, received '${r.received}'`;break;case b.invalid_arguments:t="Invalid function arguments";break;case b.invalid_return_type:t="Invalid function return type";break;case b.invalid_date:t="Invalid date";break;case b.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:Pe.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case b.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case b.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case b.custom:t="Invalid input";break;case b.invalid_intersection_types:t="Intersection results could not be merged";break;case b.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case b.not_finite:t="Number must be finite";break;default:t=e.defaultError,Pe.assertNever(r)}return{message:t}};let da=vs;function Ks(){return da}const Ys=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,l=[...t,...a.path||[]],i={...a,path:l};if(a.message!==void 0)return{...a,path:l,message:a.message};let o="";const c=s.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return{...a,path:l,message:o}};function D(r,e){const t=Ks(),s=Ys({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===vs?void 0:vs].filter(a=>!!a)});r.common.issues.push(s)}class pt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return be;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const l=await a.key,i=await a.value;s.push({key:l,value:i})}return pt.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:l,value:i}=a;if(l.status==="aborted"||i.status==="aborted")return be;l.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),l.value==="__proto__"||i.value===void 0&&!a.alwaysSet||(s[l.value]=i.value)}return{status:e.value,value:s}}}const be=Object.freeze({status:"aborted"}),Qs=r=>({status:"dirty",value:r}),gt=r=>({status:"valid",value:r}),br=r=>r.status==="aborted",$r=r=>r.status==="dirty",ss=r=>r.status==="valid",Ms=r=>typeof Promise<"u"&&r instanceof Promise;function Xs(r,e,t,s){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function ca(r,e,t,s,a){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var re,bs,$s;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(re||(re={}));class It{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const jr=(r,e)=>{if(ss(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new yt(r.common.issues);return this._error=t,this._error}}};function Te(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(l,i)=>{var o,c;const{message:u}=r;return l.code==="invalid_enum_value"?{message:u??i.defaultError}:i.data===void 0?{message:(o=u??s)!==null&&o!==void 0?o:i.defaultError}:l.code!=="invalid_type"?{message:i.defaultError}:{message:(c=u??t)!==null&&c!==void 0?c:i.defaultError}},description:a}}class Ie{get description(){return this._def.description}_getType(e){return Pt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new pt,ctx:{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Ms(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const a={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0&&s,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},l=this._parseSync({data:e,path:a.path,parent:a});return jr(a,l)}"~validate"(e){var t,s;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)};if(!this["~standard"].async)try{const l=this._parseSync({data:e,path:[],parent:a});return ss(l)?{value:l.value}:{issues:a.common.issues}}catch(l){!((s=(t=l==null?void 0:l.message)===null||t===void 0?void 0:t.toLowerCase())===null||s===void 0)&&s.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(l=>ss(l)?{value:l.value}:{issues:a.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},a=this._parse({data:e,path:s.path,parent:s}),l=await(Ms(a)?a:Promise.resolve(a));return jr(s,l)}refine(e,t){const s=a=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,l)=>{const i=e(a),o=()=>l.addIssue({code:b.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((s,a)=>!!e(s)||(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new xt({schema:this,typeName:_e.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Rt.create(this,this._def)}nullable(){return Wt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Mt.create(this)}promise(){return gs.create(this,this._def)}or(e){return Rs.create([this,e],this._def)}and(e){return Es.create(this,e,this._def)}transform(e){return new xt({...Te(this._def),schema:this,typeName:_e.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Os({...Te(this._def),innerType:this,defaultValue:t,typeName:_e.ZodDefault})}brand(){return new Tr({typeName:_e.ZodBranded,type:this,...Te(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Zs({...Te(this._def),innerType:this,catchValue:t,typeName:_e.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Ds.create(this,e)}readonly(){return Ps.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const bn=/^c[^\s-]{8,}$/i,$n=/^[0-9a-z]+$/,Sn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,kn=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,xn=/^[a-z0-9_-]{21}$/i,Mn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,An=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Tn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let vr;const Nn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Rn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,En=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,In=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,zn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ln=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ua="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",On=new RegExp(`^${ua}$`);function ha(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function pa(r){let e=`${ua}T${ha(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Zn(r,e){if(!Mn.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return typeof a=="object"&&a!==null&&!(!a.typ||!a.alg)&&(!e||a.alg===e)}catch{return!1}}function Pn(r,e){return!(e!=="v4"&&e||!Rn.test(r))||!(e!=="v6"&&e||!In.test(r))}class kt extends Ie{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==K.string){const i=this._getOrReturnCtx(e);return D(i,{code:b.invalid_type,expected:K.string,received:i.parsedType}),be}const t=new pt;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:b.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:b.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(s=this._getOrReturnCtx(e,s),o?D(s,{code:b.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&D(s,{code:b.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Tn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"email",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")vr||(vr=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),vr.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"emoji",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")kn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"uuid",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")xn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"nanoid",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")bn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cuid",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")$n.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cuid2",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Sn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"ulid",code:b.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),D(s,{validation:"url",code:b.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"regex",code:b.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?pa(i).test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?On.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${ha(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:b.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?An.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"duration",code:b.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(a=e.data,((l=i.version)!=="v4"&&l||!Nn.test(a))&&(l!=="v6"&&l||!En.test(a))&&(s=this._getOrReturnCtx(e,s),D(s,{validation:"ip",code:b.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Zn(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"jwt",code:b.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Pn(e.data,i.version)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cidr",code:b.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?zn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"base64",code:b.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Ln.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"base64url",code:b.invalid_string,message:i.message}),t.dirty()):Pe.assertNever(i);var a,l;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:b.invalid_string,...re.errToObj(s)})}_addCheck(e){return new kt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...re.errToObj(e)})}url(e){return this._addCheck({kind:"url",...re.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...re.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...re.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...re.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...re.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...re.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...re.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...re.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...re.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...re.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...re.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...re.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(s=e==null?void 0:e.local)!==null&&s!==void 0&&s,...re.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...re.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...re.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...re.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...re.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...re.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...re.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...re.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...re.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...re.errToObj(t)})}nonempty(e){return this.min(1,re.errToObj(e))}trim(){return new kt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new kt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new kt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Fn(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s;return parseInt(r.toFixed(a).replace(".",""))%parseInt(e.toFixed(a).replace(".",""))/Math.pow(10,a)}kt.create=r=>{var e;return new kt({checks:[],typeName:_e.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Te(r)})};class Bt extends Ie{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==K.number){const a=this._getOrReturnCtx(e);return D(a,{code:b.invalid_type,expected:K.number,received:a.parsedType}),be}let t;const s=new pt;for(const a of this._def.checks)a.kind==="int"?Pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:b.invalid_type,expected:"integer",received:"float",message:a.message}),s.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="multipleOf"?Fn(e.data,a.value)!==0&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:b.not_finite,message:a.message}),s.dirty()):Pe.assertNever(a);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,re.toString(t))}gt(e,t){return this.setLimit("min",e,!1,re.toString(t))}lte(e,t){return this.setLimit("max",e,!0,re.toString(t))}lt(e,t){return this.setLimit("max",e,!1,re.toString(t))}setLimit(e,t,s,a){return new Bt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:re.toString(a)}]})}_addCheck(e){return new Bt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:re.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:re.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:re.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:re.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:re.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:re.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:re.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:re.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:re.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&Pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Bt.create=r=>new Bt({checks:[],typeName:_e.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...Te(r)});class Gt extends Ie{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==K.bigint)return this._getInvalidInput(e);let t;const s=new pt;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),D(t,{code:b.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):Pe.assertNever(a);return{status:s.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.bigint,received:t.parsedType}),be}gte(e,t){return this.setLimit("min",e,!0,re.toString(t))}gt(e,t){return this.setLimit("min",e,!1,re.toString(t))}lte(e,t){return this.setLimit("max",e,!0,re.toString(t))}lt(e,t){return this.setLimit("max",e,!1,re.toString(t))}setLimit(e,t,s,a){return new Gt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:re.toString(a)}]})}_addCheck(e){return new Gt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:re.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:re.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:re.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:re.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:re.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Gt.create=r=>{var e;return new Gt({checks:[],typeName:_e.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Te(r)})};class As extends Ie{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==K.boolean){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.boolean,received:t.parsedType}),be}return gt(e.data)}}As.create=r=>new As({typeName:_e.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...Te(r)});class rs extends Ie{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==K.date){const a=this._getOrReturnCtx(e);return D(a,{code:b.invalid_type,expected:K.date,received:a.parsedType}),be}if(isNaN(e.data.getTime()))return D(this._getOrReturnCtx(e),{code:b.invalid_date}),be;const t=new pt;let s;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:b.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:b.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):Pe.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new rs({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:re.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:re.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}rs.create=r=>new rs({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:_e.ZodDate,...Te(r)});class er extends Ie{_parse(e){if(this._getType(e)!==K.symbol){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.symbol,received:t.parsedType}),be}return gt(e.data)}}er.create=r=>new er({typeName:_e.ZodSymbol,...Te(r)});class Ts extends Ie{_parse(e){if(this._getType(e)!==K.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.undefined,received:t.parsedType}),be}return gt(e.data)}}Ts.create=r=>new Ts({typeName:_e.ZodUndefined,...Te(r)});class Ns extends Ie{_parse(e){if(this._getType(e)!==K.null){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.null,received:t.parsedType}),be}return gt(e.data)}}Ns.create=r=>new Ns({typeName:_e.ZodNull,...Te(r)});class ms extends Ie{constructor(){super(...arguments),this._any=!0}_parse(e){return gt(e.data)}}ms.create=r=>new ms({typeName:_e.ZodAny,...Te(r)});class es extends Ie{constructor(){super(...arguments),this._unknown=!0}_parse(e){return gt(e.data)}}es.create=r=>new es({typeName:_e.ZodUnknown,...Te(r)});class Dt extends Ie{_parse(e){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.never,received:t.parsedType}),be}}Dt.create=r=>new Dt({typeName:_e.ZodNever,...Te(r)});class tr extends Ie{_parse(e){if(this._getType(e)!==K.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.void,received:t.parsedType}),be}return gt(e.data)}}tr.create=r=>new tr({typeName:_e.ZodVoid,...Te(r)});class Mt extends Ie{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==K.array)return D(t,{code:b.invalid_type,expected:K.array,received:t.parsedType}),be;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,o=t.data.length<a.exactLength.value;(i||o)&&(D(t,{code:i?b.too_big:b.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(D(t,{code:b.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(D(t,{code:b.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>a.type._parseAsync(new It(t,i,t.path,o)))).then(i=>pt.mergeArray(s,i));const l=[...t.data].map((i,o)=>a.type._parseSync(new It(t,i,t.path,o)));return pt.mergeArray(s,l)}get element(){return this._def.type}min(e,t){return new Mt({...this._def,minLength:{value:e,message:re.toString(t)}})}max(e,t){return new Mt({...this._def,maxLength:{value:e,message:re.toString(t)}})}length(e,t){return new Mt({...this._def,exactLength:{value:e,message:re.toString(t)}})}nonempty(e){return this.min(1,e)}}function is(r){if(r instanceof Je){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=Rt.create(is(s))}return new Je({...r._def,shape:()=>e})}return r instanceof Mt?new Mt({...r._def,type:is(r.element)}):r instanceof Rt?Rt.create(is(r.unwrap())):r instanceof Wt?Wt.create(is(r.unwrap())):r instanceof zt?zt.create(r.items.map(e=>is(e))):r}Mt.create=(r,e)=>new Mt({type:r,minLength:null,maxLength:null,exactLength:null,typeName:_e.ZodArray,...Te(e)});class Je extends Ie{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=Pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==K.object){const c=this._getOrReturnCtx(e);return D(c,{code:b.invalid_type,expected:K.object,received:c.parsedType}),be}const{status:t,ctx:s}=this._processInputParams(e),{shape:a,keys:l}=this._getCached(),i=[];if(!(this._def.catchall instanceof Dt&&this._def.unknownKeys==="strip"))for(const c in s.data)l.includes(c)||i.push(c);const o=[];for(const c of l){const u=a[c],T=s.data[c];o.push({key:{status:"valid",value:c},value:u._parse(new It(s,T,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof Dt){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)o.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(c==="strict")i.length>0&&(D(s,{code:b.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const T=s.data[u];o.push({key:{status:"valid",value:u},value:c._parse(new It(s,T,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of o){const T=await u.key,ne=await u.value;c.push({key:T,value:ne,alwaysSet:u.alwaysSet})}return c}).then(c=>pt.mergeObjectSync(t,c)):pt.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return re.errToObj,new Je({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var a,l,i,o;const c=(i=(l=(a=this._def).errorMap)===null||l===void 0?void 0:l.call(a,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(o=re.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new Je({...this._def,unknownKeys:"strip"})}passthrough(){return new Je({...this._def,unknownKeys:"passthrough"})}extend(e){return new Je({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Je({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:_e.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Je({...this._def,catchall:e})}pick(e){const t={};return Pe.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new Je({...this._def,shape:()=>t})}omit(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new Je({...this._def,shape:()=>t})}deepPartial(){return is(this)}partial(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}),new Je({...this._def,shape:()=>t})}required(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof Rt;)a=a._def.innerType;t[s]=a}}),new Je({...this._def,shape:()=>t})}keyof(){return va(Pe.objectKeys(this.shape))}}Je.create=(r,e)=>new Je({shape:()=>r,unknownKeys:"strip",catchall:Dt.create(),typeName:_e.ZodObject,...Te(e)}),Je.strictCreate=(r,e)=>new Je({shape:()=>r,unknownKeys:"strict",catchall:Dt.create(),typeName:_e.ZodObject,...Te(e)}),Je.lazycreate=(r,e)=>new Je({shape:r,unknownKeys:"strip",catchall:Dt.create(),typeName:_e.ZodObject,...Te(e)});class Rs extends Ie{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async a=>{const l={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:l}),ctx:l}})).then(function(a){for(const i of a)if(i.result.status==="valid")return i.result;for(const i of a)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const l=a.map(i=>new yt(i.ctx.common.issues));return D(t,{code:b.invalid_union,unionErrors:l}),be});{let a;const l=[];for(const o of s){const c={...t,common:{...t.common,issues:[]},parent:null},u=o._parseSync({data:t.data,path:t.path,parent:c});if(u.status==="valid")return u;u.status!=="dirty"||a||(a={result:u,ctx:c}),c.common.issues.length&&l.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const i=l.map(o=>new yt(o));return D(t,{code:b.invalid_union,unionErrors:i}),be}}get options(){return this._def.options}}Rs.create=(r,e)=>new Rs({options:r,typeName:_e.ZodUnion,...Te(e)});const Vt=r=>r instanceof Is?Vt(r.schema):r instanceof xt?Vt(r.innerType()):r instanceof zs?[r.value]:r instanceof Jt?r.options:r instanceof Ls?Pe.objectValues(r.enum):r instanceof Os?Vt(r._def.innerType):r instanceof Ts?[void 0]:r instanceof Ns?[null]:r instanceof Rt?[void 0,...Vt(r.unwrap())]:r instanceof Wt?[null,...Vt(r.unwrap())]:r instanceof Tr||r instanceof Ps?Vt(r.unwrap()):r instanceof Zs?Vt(r._def.innerType):[];class ir extends Ie{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==K.object)return D(t,{code:b.invalid_type,expected:K.object,received:t.parsedType}),be;const s=this.discriminator,a=t.data[s],l=this.optionsMap.get(a);return l?t.common.async?l._parseAsync({data:t.data,path:t.path,parent:t}):l._parseSync({data:t.data,path:t.path,parent:t}):(D(t,{code:b.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),be)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const a=new Map;for(const l of t){const i=Vt(l.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(a.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);a.set(o,l)}}return new ir({typeName:_e.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...Te(s)})}}function Sr(r,e){const t=Pt(r),s=Pt(e);if(r===e)return{valid:!0,data:r};if(t===K.object&&s===K.object){const a=Pe.objectKeys(e),l=Pe.objectKeys(r).filter(o=>a.indexOf(o)!==-1),i={...r,...e};for(const o of l){const c=Sr(r[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===K.array&&s===K.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let l=0;l<r.length;l++){const i=Sr(r[l],e[l]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return t===K.date&&s===K.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Es extends Ie{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(l,i)=>{if(br(l)||br(i))return be;const o=Sr(l.value,i.value);return o.valid?(($r(l)||$r(i))&&t.dirty(),{status:t.value,value:o.data}):(D(s,{code:b.invalid_intersection_types}),be)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([l,i])=>a(l,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Es.create=(r,e,t)=>new Es({left:r,right:e,typeName:_e.ZodIntersection,...Te(t)});class zt extends Ie{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==K.array)return D(s,{code:b.invalid_type,expected:K.array,received:s.parsedType}),be;if(s.data.length<this._def.items.length)return D(s,{code:b.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),be;!this._def.rest&&s.data.length>this._def.items.length&&(D(s,{code:b.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((l,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new It(s,l,s.path,i)):null}).filter(l=>!!l);return s.common.async?Promise.all(a).then(l=>pt.mergeArray(t,l)):pt.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new zt({...this._def,rest:e})}}zt.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new zt({items:r,typeName:_e.ZodTuple,rest:null,...Te(e)})};class or extends Ie{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==K.object)return D(s,{code:b.invalid_type,expected:K.object,received:s.parsedType}),be;const a=[],l=this._def.keyType,i=this._def.valueType;for(const o in s.data)a.push({key:l._parse(new It(s,o,s.path,o)),value:i._parse(new It(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?pt.mergeObjectAsync(t,a):pt.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return new or(t instanceof Ie?{keyType:e,valueType:t,typeName:_e.ZodRecord,...Te(s)}:{keyType:kt.create(),valueType:e,typeName:_e.ZodRecord,...Te(t)})}}class sr extends Ie{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==K.map)return D(s,{code:b.invalid_type,expected:K.map,received:s.parsedType}),be;const a=this._def.keyType,l=this._def.valueType,i=[...s.data.entries()].map(([o,c],u)=>({key:a._parse(new It(s,o,s.path,[u,"key"])),value:l._parse(new It(s,c,s.path,[u,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const u=await c.key,T=await c.value;if(u.status==="aborted"||T.status==="aborted")return be;u.status!=="dirty"&&T.status!=="dirty"||t.dirty(),o.set(u.value,T.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const u=c.key,T=c.value;if(u.status==="aborted"||T.status==="aborted")return be;u.status!=="dirty"&&T.status!=="dirty"||t.dirty(),o.set(u.value,T.value)}return{status:t.value,value:o}}}}sr.create=(r,e,t)=>new sr({valueType:e,keyType:r,typeName:_e.ZodMap,...Te(t)});class as extends Ie{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==K.set)return D(s,{code:b.invalid_type,expected:K.set,received:s.parsedType}),be;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(D(s,{code:b.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(D(s,{code:b.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const l=this._def.valueType;function i(c){const u=new Set;for(const T of c){if(T.status==="aborted")return be;T.status==="dirty"&&t.dirty(),u.add(T.value)}return{status:t.value,value:u}}const o=[...s.data.values()].map((c,u)=>l._parse(new It(s,c,s.path,u)));return s.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new as({...this._def,minSize:{value:e,message:re.toString(t)}})}max(e,t){return new as({...this._def,maxSize:{value:e,message:re.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}as.create=(r,e)=>new as({valueType:r,minSize:null,maxSize:null,typeName:_e.ZodSet,...Te(e)});class cs extends Ie{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==K.function)return D(t,{code:b.invalid_type,expected:K.function,received:t.parsedType}),be;function s(o,c){return Ys({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ks(),vs].filter(u=>!!u),issueData:{code:b.invalid_arguments,argumentsError:c}})}function a(o,c){return Ys({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ks(),vs].filter(u=>!!u),issueData:{code:b.invalid_return_type,returnTypeError:c}})}const l={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof gs){const o=this;return gt(async function(...c){const u=new yt([]),T=await o._def.args.parseAsync(c,l).catch(N=>{throw u.addIssue(s(c,N)),u}),ne=await Reflect.apply(i,this,T);return await o._def.returns._def.type.parseAsync(ne,l).catch(N=>{throw u.addIssue(a(ne,N)),u})})}{const o=this;return gt(function(...c){const u=o._def.args.safeParse(c,l);if(!u.success)throw new yt([s(c,u.error)]);const T=Reflect.apply(i,this,u.data),ne=o._def.returns.safeParse(T,l);if(!ne.success)throw new yt([a(T,ne.error)]);return ne.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new cs({...this._def,args:zt.create(e).rest(es.create())})}returns(e){return new cs({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new cs({args:e||zt.create([]).rest(es.create()),returns:t||es.create(),typeName:_e.ZodFunction,...Te(s)})}}class Is extends Ie{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Is.create=(r,e)=>new Is({getter:r,typeName:_e.ZodLazy,...Te(e)});class zs extends Ie{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return D(t,{received:t.data,code:b.invalid_literal,expected:this._def.value}),be}return{status:"valid",value:e.data}}get value(){return this._def.value}}function va(r,e){return new Jt({values:r,typeName:_e.ZodEnum,...Te(e)})}zs.create=(r,e)=>new zs({value:r,typeName:_e.ZodLiteral,...Te(e)});class Jt extends Ie{constructor(){super(...arguments),bs.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return D(t,{expected:Pe.joinValues(s),received:t.parsedType,code:b.invalid_type}),be}if(Xs(this,bs)||ca(this,bs,new Set(this._def.values)),!Xs(this,bs).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return D(t,{received:t.data,code:b.invalid_enum_value,options:s}),be}return gt(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Jt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Jt.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}bs=new WeakMap,Jt.create=va;class Ls extends Ie{constructor(){super(...arguments),$s.set(this,void 0)}_parse(e){const t=Pe.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==K.string&&s.parsedType!==K.number){const a=Pe.objectValues(t);return D(s,{expected:Pe.joinValues(a),received:s.parsedType,code:b.invalid_type}),be}if(Xs(this,$s)||ca(this,$s,new Set(Pe.getValidEnumValues(this._def.values))),!Xs(this,$s).has(e.data)){const a=Pe.objectValues(t);return D(s,{received:s.data,code:b.invalid_enum_value,options:a}),be}return gt(e.data)}get enum(){return this._def.values}}$s=new WeakMap,Ls.create=(r,e)=>new Ls({values:r,typeName:_e.ZodNativeEnum,...Te(e)});class gs extends Ie{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==K.promise&&t.common.async===!1)return D(t,{code:b.invalid_type,expected:K.promise,received:t.parsedType}),be;const s=t.parsedType===K.promise?t.data:Promise.resolve(t.data);return gt(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}gs.create=(r,e)=>new gs({type:r,typeName:_e.ZodPromise,...Te(e)});class xt extends Ie{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===_e.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,l={addIssue:i=>{D(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(l.addIssue=l.addIssue.bind(l),a.type==="preprocess"){const i=a.transform(s.data,l);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return be;const c=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return c.status==="aborted"?be:c.status==="dirty"||t.value==="dirty"?Qs(c.value):c});{if(t.value==="aborted")return be;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?be:o.status==="dirty"||t.value==="dirty"?Qs(o.value):o}}if(a.type==="refinement"){const i=o=>{const c=a.refinement(o,l);if(s.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?be:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?be:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(a.type==="transform"){if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!ss(i))return i;const o=a.transform(i.value,l);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>ss(i)?Promise.resolve(a.transform(i.value,l)).then(o=>({status:t.value,value:o})):i)}Pe.assertNever(a)}}xt.create=(r,e,t)=>new xt({schema:r,typeName:_e.ZodEffects,effect:e,...Te(t)}),xt.createWithPreprocess=(r,e,t)=>new xt({schema:e,effect:{type:"preprocess",transform:r},typeName:_e.ZodEffects,...Te(t)});class Rt extends Ie{_parse(e){return this._getType(e)===K.undefined?gt(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Rt.create=(r,e)=>new Rt({innerType:r,typeName:_e.ZodOptional,...Te(e)});class Wt extends Ie{_parse(e){return this._getType(e)===K.null?gt(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Wt.create=(r,e)=>new Wt({innerType:r,typeName:_e.ZodNullable,...Te(e)});class Os extends Ie{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===K.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Os.create=(r,e)=>new Os({innerType:r,typeName:_e.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Te(e)});class Zs extends Ie{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return Ms(a)?a.then(l=>({status:"valid",value:l.status==="valid"?l.value:this._def.catchValue({get error(){return new yt(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new yt(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Zs.create=(r,e)=>new Zs({innerType:r,typeName:_e.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Te(e)});class rr extends Ie{_parse(e){if(this._getType(e)!==K.nan){const t=this._getOrReturnCtx(e);return D(t,{code:b.invalid_type,expected:K.nan,received:t.parsedType}),be}return{status:"valid",value:e.data}}}rr.create=r=>new rr({typeName:_e.ZodNaN,...Te(r)});const jn=Symbol("zod_brand");class Tr extends Ie{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class Ds extends Ie{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?be:a.status==="dirty"?(t.dirty(),Qs(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?be:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new Ds({in:e,out:t,typeName:_e.ZodPipeline})}}class Ps extends Ie{_parse(e){const t=this._def.innerType._parse(e),s=a=>(ss(a)&&(a.value=Object.freeze(a.value)),a);return Ms(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}function Dr(r,e={},t){return r?ms.create().superRefine((s,a)=>{var l,i;if(!r(s)){const o=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,c=(i=(l=o.fatal)!==null&&l!==void 0?l:t)===null||i===void 0||i,u=typeof o=="string"?{message:o}:o;a.addIssue({code:"custom",...u,fatal:c})}}):ms.create()}Ps.create=(r,e)=>new Ps({innerType:r,typeName:_e.ZodReadonly,...Te(e)});const Dn={object:Je.lazycreate};var _e;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(_e||(_e={}));const Ur=kt.create,Vr=Bt.create,Un=rr.create,Vn=Gt.create,qr=As.create,qn=rs.create,Hn=er.create,Bn=Ts.create,Gn=Ns.create,Jn=ms.create,Wn=es.create,Kn=Dt.create,Yn=tr.create,Qn=Mt.create,Xn=Je.create,ei=Je.strictCreate,ti=Rs.create,si=ir.create,ri=Es.create,ai=zt.create,ni=or.create,ii=sr.create,oi=as.create,li=cs.create,di=Is.create,ci=zs.create,ui=Jt.create,hi=Ls.create,pi=gs.create,Hr=xt.create,vi=Rt.create,mi=Wt.create,gi=xt.createWithPreprocess,fi=Ds.create,yi={string:r=>kt.create({...r,coerce:!0}),number:r=>Bt.create({...r,coerce:!0}),boolean:r=>As.create({...r,coerce:!0}),bigint:r=>Gt.create({...r,coerce:!0}),date:r=>rs.create({...r,coerce:!0})},_i=be;var Fe=Object.freeze({__proto__:null,defaultErrorMap:vs,setErrorMap:function(r){da=r},getErrorMap:Ks,makeIssue:Ys,EMPTY_PATH:[],addIssueToContext:D,ParseStatus:pt,INVALID:be,DIRTY:Qs,OK:gt,isAborted:br,isDirty:$r,isValid:ss,isAsync:Ms,get util(){return Pe},get objectUtil(){return Cr},ZodParsedType:K,getParsedType:Pt,ZodType:Ie,datetimeRegex:pa,ZodString:kt,ZodNumber:Bt,ZodBigInt:Gt,ZodBoolean:As,ZodDate:rs,ZodSymbol:er,ZodUndefined:Ts,ZodNull:Ns,ZodAny:ms,ZodUnknown:es,ZodNever:Dt,ZodVoid:tr,ZodArray:Mt,ZodObject:Je,ZodUnion:Rs,ZodDiscriminatedUnion:ir,ZodIntersection:Es,ZodTuple:zt,ZodRecord:or,ZodMap:sr,ZodSet:as,ZodFunction:cs,ZodLazy:Is,ZodLiteral:zs,ZodEnum:Jt,ZodNativeEnum:Ls,ZodPromise:gs,ZodEffects:xt,ZodTransformer:xt,ZodOptional:Rt,ZodNullable:Wt,ZodDefault:Os,ZodCatch:Zs,ZodNaN:rr,BRAND:jn,ZodBranded:Tr,ZodPipeline:Ds,ZodReadonly:Ps,custom:Dr,Schema:Ie,ZodSchema:Ie,late:Dn,get ZodFirstPartyTypeKind(){return _e},coerce:yi,any:Jn,array:Qn,bigint:Vn,boolean:qr,date:qn,discriminatedUnion:si,effect:Hr,enum:ui,function:li,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>Dr(t=>t instanceof r,e),intersection:ri,lazy:di,literal:ci,map:ii,nan:Un,nativeEnum:hi,never:Kn,null:Gn,nullable:mi,number:Vr,object:Xn,oboolean:()=>qr().optional(),onumber:()=>Vr().optional(),optional:vi,ostring:()=>Ur().optional(),pipeline:fi,preprocess:gi,promise:pi,record:ni,set:oi,strictObject:ei,string:Ur,symbol:Hn,transformer:Hr,tuple:ai,undefined:Bn,union:ti,unknown:Wn,void:Yn,NEVER:_i,ZodIssueCode:b,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:yt});class ht extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,ht.prototype)}}const jt=Fe.object({name:Fe.string().optional(),title:Fe.string().optional(),type:Fe.enum(["stdio","http","sse"]).optional(),command:Fe.string().optional(),args:Fe.array(Fe.union([Fe.string(),Fe.number(),Fe.boolean()])).optional(),env:Fe.record(Fe.union([Fe.string(),Fe.number(),Fe.boolean(),Fe.null(),Fe.undefined()])).optional(),url:Fe.string().optional()}).passthrough();function St(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function os(r){return(r==null?void 0:r.type)==="stdio"}function Bs(r){return St(r)?r.url:os(r)?r.command:""}const wi=Fe.array(jt),Ci=Fe.object({servers:Fe.array(jt)}),bi=Fe.object({mcpServers:Fe.array(jt)}),$i=Fe.object({servers:Fe.record(Fe.unknown())}),Si=Fe.object({mcpServers:Fe.record(Fe.unknown())}),ki=Fe.record(Fe.unknown()),xi=jt.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const s=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(a=>s.has(a))},{message:"Single server object must have valid server properties"});function Yt(r){try{const e=jt.transform(t=>{let s;if(t.type)s=t.type;else if(t.url)s="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");s="stdio"}if(s==="http"||s==="sse"){if(!t.url)throw new Error(`${s.toUpperCase()} server must have a 'url' property`);return{type:s,name:t.name||t.title||t.url,url:t.url}}{const a=t.command||"",l=t.args?t.args.map(u=>String(u)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const i=l.length>0?`${a} ${l.join(" ")}`:a,o=t.name||t.title||(a?a.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([u,T])=>T!=null).map(([u,T])=>[u,String(T)])):void 0;return{type:"stdio",name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new ht(e.error.message);return e.data}catch(e){throw e instanceof Error?new ht(`Invalid server configuration: ${e.message}`):new ht("Invalid server configuration")}}class Us{constructor(e){Oe(this,"servers",ct([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===Ve.getStoredMCPServersResponse){const s=t.data;return Array.isArray(s)&&this.servers.set(s),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:Ve.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:Ve.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new ht("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const s=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(s),s})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const s=[...t,...e.map(a=>({...a,id:crypto.randomUUID()}))];return this.saveServers(s),s})}checkExistingServerName(e,t){const s=mr(this.servers).find(a=>a.name===e);if(s&&(s==null?void 0:s.id)!==t)throw new ht(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const s=t.map(a=>a.id===e.id?e:a);return this.saveServers(s),s})}deleteServer(e){this.servers.update(t=>{const s=t.filter(a=>a.id!==e);return this.saveServers(s),s})}toggleDisabledServer(e){this.servers.update(t=>{const s=t.map(a=>a.id===e?{...a,disabled:!a.disabled}:a);return this.saveServers(s),s})}static convertServerToJSON(e){if(St(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,s=new Map;e.forEach(l=>{var o,c;const i=(o=l.tools)==null?void 0:o.filter(u=>!u.enabled).map(u=>u.definition.mcp_tool_name);l.disabled?t.set(l.id,"MCP server has been manually disabled"):l.tools&&l.tools.length===0?t.set(l.id,"No tools are available for this MCP server"):i&&i.length===((c=l.tools)==null?void 0:c.length)?t.set(l.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&s.set(l.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const a=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...a]),warnings:s}}static parseDuplicateServerIds(e){const t=new Map;for(const a of e)t.has(a.name)||t.set(a.name,[]),t.get(a.name).push(a.id);const s=new Map;for(const[,a]of t)if(a.length>1)for(let l=1;l<a.length;l++)s.set(a[l],"MCP server is disabled due to duplicate server names");return s}static convertParsedServerToWebview(e){const{tools:t,...s}=e;return{...s,tools:void 0}}static parseServerConfigFromJSON(e){return function(s){try{const a=JSON.parse(s),l=Fe.union([wi.transform(i=>i.map(o=>Yt(o))),Ci.transform(i=>i.servers.map(o=>Yt(o))),bi.transform(i=>i.mcpServers.map(o=>Yt(o))),$i.transform(i=>Object.entries(i.servers).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})),Si.transform(i=>Object.entries(i.mcpServers).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})),xi.transform(i=>[Yt(i)]),ki.transform(i=>{if(!Object.values(i).some(o=>{const c=jt.safeParse(o);return c.success&&(c.data.command!==void 0||c.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(i).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})})]).safeParse(a);if(l.success)return l.data;throw new ht("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(a){throw a instanceof ht?a:new ht("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(s=>this.convertParsedServerToWebview(s))}importFromJSON(e){try{const t=Us.parseServerConfigFromJSON(e),s=mr(this.servers),a=new Set(s.map(l=>l.name));for(const l of t){if(!l.name)throw new ht("All servers must have a name.");if(a.has(l.name))throw new ht(`A server with the name '${l.name}' already exists.`);a.add(l.name)}return this.servers.update(l=>{const i=[...l,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof ht?t:new ht("Failed to import MCP servers from JSON. Please check the format.")}}}class Mi{constructor(e){Oe(this,"_terminalSettings",ct({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===Ve.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:Ve.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:Ve.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:Ve.updateTerminalSettings,data:{startupScript:e}})}}const Ss=class Ss{constructor(e){Oe(this,"_swarmModeSettings",ct(Hs));Oe(this,"_isLoaded",!1);Oe(this,"_pollInterval",null);Oe(this,"_lastKnownSettingsHash","");Oe(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:hr.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(Hs),this._lastKnownSettingsHash=JSON.stringify(Hs),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:hr.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(Hs)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Ss.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:hr.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};Oe(Ss,"key","swarmModeModel"),Oe(Ss,"POLLING_INTERVAL_MS",5e3);let Fs=Ss;var Tt=(r=>(r.file="file",r.folder="folder",r))(Tt||{});class Ht{constructor(e,t){Oe(this,"subscribe");Oe(this,"set");Oe(this,"update");Oe(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case Ve.wsContextSourceFoldersChanged:case Ve.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case Ve.sourceFoldersSyncStatus:this.update(s=>({...s,syncStatus:t.data.status}))}});Oe(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:Ve.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Oe(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:Ve.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,s)=>t.type===s.type?t.name.localeCompare(s.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:s,set:a,update:l}=ct({sourceFolders:[],sourceTree:[],syncStatus:fr.done});this.subscribe=s,this.set=a,this.update=l,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:Ht.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==_t.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:Ve.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:Ve.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:Ve.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=mr(this);const s=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(a=>({...a,sourceFolders:e,sourceTree:s}))}async getRefreshedSourceTree(e,t){const s=Ht.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,s)}async getRefreshedSourceTreeRecurse(e,t){const s=new Map(e.map(a=>[JSON.stringify([a.fileId.folderRoot,a.fileId.relPath]),a]));for(let a of t){const l=Ht.fileIdToString(a.fileId);if(a.type==="folder"){const i=s.get(l);i&&(a.expanded=i.type==="folder"&&i.expanded,a.expanded&&(a.children=await this.getChildren(a.fileId),a.children=await this.getRefreshedSourceTreeRecurse(i.children,a.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,s)=>t.name.localeCompare(s.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}var Ai=m('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Ti=m('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');const Ni="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Ri="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Ei="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Ii=m('<div class="children-container"></div>'),zi=m('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function ma(r,e){Ye(e,!1);let t=w(e,"data",8),s=w(e,"wsContextModel",8),a=w(e,"indentLevel",8);const l=()=>{s().toggleNode(t())},i={[_t.included]:Ni,[_t.excluded]:Ri,[_t.partial]:Ei},o={[_t.included]:"included",[_t.excluded]:"excluded",[_t.partial]:"partially included"};let c=G(),u=G(),T=G();$e(()=>y(t()),()=>{var x;v(u,(x=t()).type===Tt.folder&&x.inclusionState!==_t.excluded?x.expanded?"chevron-down":"chevron-right":x.type===Tt.folder?"folder":"file")}),$e(()=>(y(t()),_t),()=>{v(c,t().type===Tt.folder&&t().inclusionState!==_t.excluded)}),$e(()=>(y(t()),Tt),()=>{v(T,t().type===Tt.folder&&t().expanded&&t().children&&t().children.length>0?t():null)}),lt(),Xe();var ne=zi(),N=h(ne),pe=ls(()=>Gs("Enter",l));let ve;var $=h(N);aa($,{get icon(){return n(u)}});var S=p($,2),f=h(S),B=p(S,2),P=x=>{ue(x,{size:1,class:"file-count",children:(k,R)=>{var F=L();Ce(E=>ze(F,E),[()=>(y(t()),g(()=>t().trackedFileCount.toLocaleString()))],xe),d(k,F)},$$slots:{default:!0}})};Y(B,x=>{y(t()),y(Tt),y(_t),g(()=>t().type===Tt.folder&&t().inclusionState!==_t.excluded&&typeof t().trackedFileCount=="number")&&x(P)});var C=p(B,2),O=p(N,2),Z=x=>{var k=Ii();ot(k,5,()=>(n(T),g(()=>n(T).children)),R=>Ht.fileIdToString(R.fileId),(R,F)=>{var E=nt(),_=Se(E);const M=xe(()=>a()+1);ma(_,{get data(){return n(F)},get wsContextModel(){return s()},get indentLevel(){return n(M)}}),d(R,E)}),d(x,k)};Y(O,x=>{n(T)&&x(Z)}),Ce(x=>{ve=Ct(N,1,"tree-item svelte-sympus",null,ve,x),Qt(N,"title",(y(t()),g(()=>t().reason))),Qt(N,"aria-expanded",(y(t()),y(Tt),g(()=>t().type===Tt.folder&&t().expanded))),Qt(N,"aria-level",a()),ba(N,`padding-left: ${10*a()+20}px;`),ze(f,(y(t()),g(()=>t().name))),Qt(C,"src",(y(t()),g(()=>i[t().inclusionState]))),Qt(C,"alt",(y(t()),g(()=>o[t().inclusionState])))},[()=>({"included-folder":n(c)})],xe),mt("click",N,l),mt("keyup",N,function(...x){var k;(k=n(pe))==null||k.apply(this,x)}),d(r,ne),Qe()}var Li=m('<div class="files-container svelte-8hfqhl"></div>'),Oi=bt('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Zi(r){var e=Oi();d(r,e)}var Pi=m('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Fi=m("<!> <!>",1),ji=m('<div class="settings-card-body"><!></div>'),Di=m('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function Et(r,e){const t=$a(e),s=us(e,["children","$$slots","$$events","$$legacy"]),a=us(s,["class","icon","title","isClickable"]);Ye(e,!1);const l=G(),i=G(),o=G();let c=w(e,"class",8,""),u=w(e,"icon",24,()=>{}),T=w(e,"title",24,()=>{}),ne=w(e,"isClickable",8,!1);$e(()=>(n(l),n(i),y(a)),()=>{v(l,a.class),v(i,Sa(a,["class"]))}),$e(()=>(y(c()),n(l)),()=>{v(o,`settings-card ${c()} ${n(l)||""}`)}),lt();var N=Di();js(N,Z=>({role:"button",class:n(o),...n(i),[ka]:Z}),[()=>({clickable:ne()})],"svelte-13uht7n");var pe=h(N),ve=h(pe),$=h(ve),S=Z=>{var x=Fi(),k=Se(x),R=_=>{var M=Pi(),te=h(M);wr(te,u,(I,A)=>{A(I,{})}),d(_,M)};Y(k,_=>{u()&&_(R)});var F=p(k,2),E=_=>{ue(_,{color:"neutral",size:1,weight:"light",class:"card-title",children:(M,te)=>{var I=L();Ce(()=>ze(I,T())),d(M,I)},$$slots:{default:!0}})};Y(F,_=>{T()&&_(E)}),d(Z,x)},f=Z=>{var x=nt(),k=Se(x);wt(k,e,"header-left",{},null),d(Z,x)};Y($,Z=>{u()||T()?Z(S):Z(f,!1)});var B=p(ve,2),P=h(B);wt(P,e,"header-right",{},null);var C=p(pe,2),O=Z=>{var x=ji(),k=h(x);wt(k,e,"default",{},null),d(Z,x)};Y(C,Z=>{g(()=>t.default)&&Z(O)}),mt("click",N,function(Z){Ta.call(this,e,Z)}),d(r,N),Qe()}var Ui=m('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),Vi=m('<div slot="header-right"><!></div>');function qi(r,e){Ye(e,!1);const[t,s]=Nt(),a=()=>st(i,"$wsContextModel",t),l=G();let i=new Ht(at,new sn(at.postMessage)),o=G(),c=G();$e(()=>a(),()=>{v(o,a().sourceFolders.sort((u,T)=>u.isWorkspaceFolder!==T.isWorkspaceFolder?u.isWorkspaceFolder?-1:1:u.fileId.folderRoot.localeCompare(T.fileId.folderRoot)))}),$e(()=>a(),()=>{v(c,a().syncStatus)}),$e(()=>n(o),()=>{v(l,n(o).reduce((u,T)=>u+(T.trackedFileCount??0),0))}),lt(),Xe(),mt("message",ar,function(...u){var T;(T=i.handleMessageFromExtension)==null||T.apply(this,u)}),Et(r,{get icon(){return Zi},title:"Context",$$events:{contextmenu:u=>u.preventDefault()},children:(u,T)=>{var ne=Ui(),N=h(ne),pe=h(N);ue(pe,{size:1,weight:"medium",class:"context-section-header",children:(B,P)=>{var C=L("SOURCE FOLDERS");d(B,C)},$$slots:{default:!0}}),function(B,P){Ye(P,!1);let C=w(P,"folders",24,()=>[]),O=w(P,"onAddMore",8),Z=w(P,"onRemove",8);Xe();var x=Ti(),k=h(x);ot(k,1,C,_=>Ht.fileIdToString(_.fileId),(_,M)=>{var te=Ai();let I;var A=h(te),ee=he=>{var je=ls(()=>Gs("Enter",()=>Z()(n(M).fileId.folderRoot)));ts(he,{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$events:{click:()=>Z()(n(M).fileId.folderRoot),keyup(...ge){var Le;(Le=n(je))==null||Le.apply(this,ge)}},children:(ge,Le)=>{Xa(ge)},$$slots:{default:!0}})};Y(A,he=>{n(M),g(()=>!n(M).isWorkspaceFolder)&&he(ee)});var X=p(A,2);const de=xe(()=>(n(M),g(()=>(he=>he.isWorkspaceFolder?"root-folder":"folder")(n(M)))));aa(X,{class:"source-folder-v-adjust",get icon(){return n(de)}});var fe=p(X,2),j=h(fe),ce=p(j),me=h(ce),q=p(fe,2),we=he=>{ue(he,{size:1,class:"file-count",children:(je,ge)=>{var Le=L();Ce(Me=>ze(Le,Me),[()=>(n(M),g(()=>n(M).trackedFileCount.toLocaleString()))],xe),d(je,Le)},$$slots:{default:!0}})};Y(q,he=>{n(M),g(()=>n(M).trackedFileCount)&&he(we)}),Ce(he=>{I=Ct(te,1,"item svelte-1skknri",null,I,he),ze(j,`${n(M),g(()=>n(M).name)??""} `),ze(me,(n(M),g(()=>n(M).isPending?"(pending)":n(M).fileId.folderRoot)))},[()=>({"workspace-folder":n(M).isWorkspaceFolder})],xe),d(_,te)});var R=p(k,2),F=ls(()=>Gs("Enter",O())),E=h(R);hs(E,{}),mt("keyup",R,function(..._){var M;(M=n(F))==null||M.apply(this,_)}),mt("click",R,function(..._){var M;(M=O())==null||M.apply(this,_)}),d(B,x),Qe()}(p(pe,2),{get folders(){return n(o)},onRemove:B=>i.removeSourceFolder(B),onAddMore:()=>i.addMoreSourceFolders()});var ve=p(N,2),$=h(ve),S=h($);ue(S,{size:1,weight:"medium",class:"context-section-header",children:(B,P)=>{var C=L("FILES");d(B,C)},$$slots:{default:!0}});var f=p(S,2);ue(f,{size:1,class:"file-count",children:(B,P)=>{var C=L();Ce(O=>ze(C,O),[()=>(n(l),g(()=>n(l).toLocaleString()))],xe),d(B,C)},$$slots:{default:!0}}),function(B,P){Ye(P,!1);const[C,O]=Nt(),Z=()=>st(x(),"$wsContextModel",C);let x=w(P,"wsContextModel",8),k=G();$e(()=>Z(),()=>{v(k,Z().sourceTree)}),lt(),Xe();var R=Li();ot(R,5,()=>n(k),F=>Ht.fileIdToString(F.fileId),(F,E)=>{ma(F,{get wsContextModel(){return x()},get data(){return n(E)},indentLevel:0})}),d(B,R),Qe(),O()}(p($,2),{get wsContextModel(){return i}}),d(u,ne)},$$slots:{default:!0,"header-right":(u,T)=>{var ne=Vi(),N=h(ne),pe=ve=>{var $=ls(()=>Gs("Enter",()=>i.requestRefresh()));ts(ve,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>i.requestRefresh(),keyup(...S){var f;(f=n($))==null||f.apply(this,S)}},children:(S,f)=>{Fa(S)},$$slots:{default:!0}})};Y(N,ve=>{n(c),y(fr),g(()=>n(c)===fr.done)&&ve(pe)}),d(u,ne)}}}),Qe(),s()}function kr(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Br(r){return kr(r)&&"component"in r}var Hi=bt('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Gr(r){var e=Hi();d(r,e)}var Bi=m('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),Gi=m('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),Ji=m('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),Wi=m('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Jr(r,e){Ye(e,!1);let t=w(e,"item",8);Xe();var s=Wi(),a=h(s);wt(a,e,"header",{},null);var l=p(a,2),i=h(l),o=c=>{var u=Ji(),T=Se(u);ue(T,{size:4,weight:"medium",color:"neutral",children:($,S)=>{var f=Bi(),B=h(f);Ce(()=>ze(B,(y(t()),g(()=>{var P;return(P=t())==null?void 0:P.name})))),d($,f)},$$slots:{default:!0}});var ne=p(T,2),N=$=>{ue($,{color:"secondary",size:1,weight:"light",children:(S,f)=>{var B=Gi(),P=h(B);Ce(()=>ze(P,(y(t()),g(()=>{var C;return(C=t())==null?void 0:C.description})))),d(S,B)},$$slots:{default:!0}})};Y(ne,$=>{y(t()),g(()=>{var S;return(S=t())==null?void 0:S.description})&&$(N)});var pe=p(ne,2),ve=h(pe);wt(ve,e,"content",{get item(){return t()}},null),d(c,u)};Y(i,c=>{t()!=null&&c(o)}),Ce(()=>Qt(s,"id",(y(t()),g(()=>{var c;return(c=t())==null?void 0:c.id})))),d(r,s),Qe()}function Cs(r,e,t,s,a,l){return a!==void 0?{name:r,description:e,icon:t,id:s,component:a,props:l}:{name:r,description:e,icon:t,id:s}}var Ki=m('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),Yi=m('<span class="c-navigation__head-icon"><!></span> ',1),Qi=m("<button><!></button>"),Xi=m('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),eo=m('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),to=m('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),so=m("<span><!></span>"),ro=m('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),ao=m('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),no=m("<div><!></div>");function io(r,e){Ye(e,!1);let t=w(e,"group",8,"Workspace Settings"),s=w(e,"items",24,()=>[]),a=w(e,"item",28,()=>{}),l=w(e,"mode",8,"tree"),i=w(e,"selectedId",28,()=>{}),o=w(e,"onNavigationChangeItem",8,$=>{}),c=w(e,"showButton",8,!0),u=w(e,"class",8,""),T=G(new Map);$e(()=>(y(i()),y(a()),y(s())),()=>{var $;i()?a(s().find(S=>(S==null?void 0:S.id)===i())):i(($=a())==null?void 0:$.id)}),$e(()=>(y(s()),y(t())),()=>{v(T,s().reduce(($,S)=>{if(!S)return $;const f=S.group??t(),B=$.get(f)??[];return B.push(S),$.set(f,B),$},new Map))}),$e(()=>(y(a()),y(s())),()=>{a()||a(s()[0])}),$e(()=>(y(o()),y(i())),()=>{o()(i())}),lt(),Xe();var ne=no(),N=h(ne),pe=$=>{nn($,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return c()},minimized:!1,$$slots:{left:(S,f)=>{var B=eo(),P=h(B);rn(P,i,C=>{var O=nt(),Z=Se(O);ot(Z,1,()=>n(T),qt,(x,k)=>{var R=ls(()=>Er(n(k),2));let F=()=>n(R)[0];var E=Xi(),_=h(E);wt(_,e,"group",{get label(){return F()},get mode(){return l()}},te=>{var I=Ki(),A=h(I);Gr(A);var ee=p(A,2);ue(ee,{size:2,color:"primary",children:(X,de)=>{var fe=L();Ce(()=>ze(fe,F())),d(X,fe)},$$slots:{default:!0}}),d(te,I)});var M=p(_,2);ot(M,5,()=>n(R)[1],qt,(te,I)=>{var A=Qi();let ee;var X=h(A);ue(X,{size:2,weight:"regular",color:"primary",children:(de,fe)=>{var j=Yi(),ce=Se(j),me=h(ce);wr(me,()=>n(I).icon,(we,he)=>{he(we,{})});var q=p(ce);Ce(()=>ze(q,` ${n(I),g(()=>n(I).name)??""}`)),d(de,j)},$$slots:{default:!0}}),Ce(de=>ee=Ct(A,1,"c-navigation__item svelte-n5ccbo",null,ee,de),[()=>({"is-active":n(I).id===i()})],xe),mt("click",A,()=>{return de=n(I),a(de),void i(de==null?void 0:de.id);var de}),d(te,A)}),d(x,E)}),d(C,O)}),d(S,B)},right:(S,f)=>{Jr(S,{get item(){return a()},slot:"right",$$slots:{header:(B,P)=>{var C=nt(),O=Se(C);wt(O,e,"header",{get item(){return a()},get selectedId(){return i()}},null),d(B,C)},content:(B,P)=>{var C=nt(),O=Se(C);wt(O,e,"content",{get item(){return a()},get isSelected(){return y(a()),y(i()),g(()=>{var Z;return((Z=a())==null?void 0:Z.id)===i()})}},Z=>{var x=nt(),k=Se(x),R=F=>{var E=nt(),_=Se(E);wr(_,()=>a().component,(M,te)=>{te(M,xa(()=>a().props))}),d(F,E)};Y(k,F=>{y(Br),y(a()),y(l()),y(i()),g(()=>{return Br(a())&&(E=a(),_=l(),M=i(),_!=="tree"||(E==null?void 0:E.id)===M);var E,_,M})&&F(R)}),d(Z,x)}),d(B,C)}}})}}})},ve=$=>{var S=ao(),f=h(S);wt(f,e,"header",{get item(){return a()}},null);var B=p(f,2);ot(B,1,()=>n(T),qt,(P,C)=>{var O=ls(()=>Er(n(C),2));let Z=()=>n(O)[0];var x=ro(),k=Se(x),R=h(k);wt(R,e,"group",{get label(){return Z()},get mode(){return l()}},E=>{ue(E,{color:"secondary",size:2,weight:"medium",children:(_,M)=>{var te=to(),I=Se(te);Gr(h(I));var A=p(I,2),ee=h(A);Ce(()=>ze(ee,Z())),d(_,te)},$$slots:{default:!0}})});var F=p(k,2);ot(F,1,()=>n(O)[1],qt,(E,_)=>{var M=so();Jr(h(M),{get item(){return n(_)},$$slots:{content:(te,I)=>{var A=nt(),ee=Se(A);wt(ee,e,"content",{get item(){return n(_)}},null),d(te,A)}}}),Na(M,(te,I)=>function(A,ee){let X;function de({scrollTo:fe,delay:j,options:ce}){clearTimeout(X),fe&&(X=setTimeout(()=>{A.scrollIntoView(ce)},j))}return de(ee),{update:de,destroy(){clearTimeout(X)}}}(te,I),()=>({scrollTo:l()==="flat"&&n(_).id===i(),delay:300,options:{behavior:"smooth"}})),d(E,M)}),d(P,x)}),d($,S)};Y(N,$=>{l()==="tree"?$(pe):$(ve,!1)}),Ce(()=>Ct(ne,1,`c-navigation c-navigation--mode__${l()??""} ${u()??""}`,"svelte-n5ccbo")),d(r,ne),Qe()}var oo=bt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function lo(r){var e=oo();d(r,e)}var co=bt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function uo(r){var e=co();d(r,e)}var ho=bt("<svg><!></svg>");function ga(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=ho();js(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),d(r,s)}var po=m('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),vo=m("<span>Connect</span>"),mo=m('<div class="connect-button-content svelte-js5lik"><!></div>'),go=m('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),fo=m('<div slot="header-right"><!></div>'),yo=m("<div> </div>"),_o=m('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),wo=m('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Co=m('<div class="category-content"><!></div>'),bo=m('<div class="category"><div class="category-heading"><!></div> <!></div>');const fa="extensionClient",ya="mcpServerModel";function Nr(){const r=ds(ya);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}var $o=m('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),So=m("<span>Connect</span>"),ko=m('<div class="connect-button-content svelte-e3a21z"><!></div>'),xo=m('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),Mo=m('<div slot="header-right"><!></div>'),Ao=m("<div> </div>"),To=m('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>');function No(r,e){Ye(e,!1);let t=w(e,"config",12),s=w(e,"mcpTool",8);const a=Nr(),l=function(){const $=ds(fa);if(!$)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return $}();async function i(){if(n(c))return T&&(clearTimeout(T),T=null),void v(c,!1);l.startRemoteMCPAuth(t().name),v(c,!0);const $=new Promise(S=>{T=setTimeout(()=>{S(),T=null},6e4)});await Promise.race([$]),v(c,!1)}async function o(){var $;await l.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${t().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(s()&&a.deleteServer(($=s())==null?void 0:$.id),v(c,!1))}let c=G(!1),u=G(!1),T=null;$e(()=>(y(t()),Zr),()=>{t(Zr(t()))}),$e(()=>y(s()),()=>{t(t().isConfigured=!!s(),!0)}),lt(),Xe();var ne=To(),N=h(ne);Et(N,{get icon(){return y(t()),g(()=>t().icon)},get title(){return y(t()),g(()=>t().displayName)},$$slots:{"header-right":($,S)=>{var f=Mo(),B=h(f),P=O=>{const Z=xe(()=>n(c)?"neutral":"accent");Ke(O,{variant:"ghost-block",get color(){return n(Z)},size:1,$$events:{click:i},children:(x,k)=>{var R=ko(),F=h(R),E=M=>{var te=$o(),I=Se(te),A=h(I);gr(A,{size:1,useCurrentColor:!0}),d(M,te)},_=M=>{var te=So();d(M,te)};Y(F,M=>{n(c)?M(E):M(_,!1)}),d(x,R)},$$slots:{default:!0}})},C=(O,Z)=>{var x=k=>{var R=xo(),F=h(R);let E;var _=h(F);const M=xe(()=>(y(ps),g(()=>[ps.Hover])));Ft(_,{get triggerOn(){return n(M)},content:"Revoke Access",children:(I,A)=>{ts(I,{color:"neutral",variant:"ghost",size:1,$$events:{click:o},children:(ee,X)=>{ga(ee,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var te=p(F,2);Mr.Root(te,{color:"success",size:1,variant:"soft",children:(I,A)=>{var ee=L("Connected");d(I,ee)},$$slots:{default:!0}}),Ce(I=>E=Ct(F,1,"disconnect-button svelte-e3a21z",null,E,I),[()=>({active:n(u)})],xe),d(k,R)};Y(O,k=>{y(t()),g(()=>t().isConfigured)&&k(x)},Z)};Y(B,O=>{y(t()),g(()=>!t().isConfigured)?O(P):O(C,!1)}),d($,f)}}});var pe=p(N,2),ve=$=>{var S=Ao(),f=h(S);Ce(()=>{Ct(S,1,`status-message ${y(t()),g(()=>t().statusType)??""}`,"svelte-e3a21z"),ze(f,(y(t()),g(()=>t().statusMessage)))}),d($,S)};Y(pe,$=>{y(t()),g(()=>t().showStatus)&&$(ve)}),mt("mouseenter",ne,()=>v(u,!0)),mt("mouseleave",ne,()=>v(u,!1)),d(r,ne),Qe()}var Ro=m('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Eo=m("<div><!></div>");function Io(r,e){Ye(e,!1);const[t,s]=Nt(),a=()=>st($,"$allServers",t),l=()=>st(ve,"$pretendNativeToolDefs",t),i=G();let o=w(e,"title",8),c=w(e,"tools",24,()=>[]),u=w(e,"onAuthenticate",8),T=w(e,"onRevokeAccess",8),ne=w(e,"onToolApprovalConfigChange",8,()=>{});const N=ds(xs.key),pe=Nr(),ve=N.getPretendNativeToolDefs(),$=pe.getServers();$e(()=>a(),()=>{v(i,N.getEnableNativeRemoteMcp()?Xr(a()):[])}),lt(),Xe();var S=Eo(),f=h(S);const B=xe(()=>(y(c()),g(()=>c().length===0)));(function(P,C){let O=w(C,"title",8),Z=w(C,"loading",8,!1);var x=bo(),k=h(x),R=h(k);ue(R,{size:1,color:"secondary",weight:"regular",children:(M,te)=>{var I=L();Ce(()=>ze(I,O())),d(M,I)},$$slots:{default:!0}});var F=p(k,2),E=M=>{var te=wo(),I=h(te);gr(I,{size:1});var A=p(I,2);ue(A,{size:1,color:"secondary",children:(ee,X)=>{var de=L("Loading...");d(ee,de)},$$slots:{default:!0}}),d(M,te)},_=M=>{var te=Co(),I=h(te);wt(I,C,"default",{},null),d(M,te)};Y(F,M=>{Z()?M(E):M(_,!1)}),d(P,x)})(f,{get title(){return o()},get loading(){return n(B)},children:(P,C)=>{var O=Ro(),Z=h(O);ot(Z,1,c,k=>k.name,(k,R)=>{(function(F,E){Ye(E,!1);let _=w(E,"config",8),M=w(E,"onAuthenticate",8),te=w(E,"onRevokeAccess",8);const I=()=>{};let A=G(!1),ee=G(null),X=G(!1);function de(){if(n(A))v(A,!1),n(ee)&&(clearTimeout(n(ee)),v(ee,null));else{v(A,!0);const q=_().authUrl||"";M()(q),v(ee,setTimeout(()=>{v(A,!1),v(ee,null)},6e4))}}$e(()=>(y(_()),n(A),n(ee)),()=>{_().isConfigured&&n(A)&&(v(A,!1),n(ee)&&(clearTimeout(n(ee)),v(ee,null)))}),lt(),Xe();var fe=_o(),j=h(fe);Et(j,{get icon(){return y(_()),g(()=>_().icon)},get title(){return y(_()),g(()=>_().displayName)},$$slots:{"header-right":(q,we)=>{var he=fo(),je=h(he),ge=Me=>{const Ae=xe(()=>n(A)?"neutral":"accent");Ke(Me,{variant:"ghost-block",get color(){return n(Ae)},size:1,$$events:{click:de},children:(De,ke)=>{var ae=mo(),H=h(ae),J=U=>{var Q=po(),le=Se(Q),ye=h(le);gr(ye,{size:1,useCurrentColor:!0}),d(U,Q)},z=U=>{var Q=vo();d(U,Q)};Y(H,U=>{n(A)?U(J):U(z,!1)}),d(De,ae)},$$slots:{default:!0}})},Le=(Me,Ae)=>{var De=ke=>{var ae=go(),H=h(ae),J=h(H),z=h(J);let U;var Q=h(z);const le=xe(()=>(y(ps),g(()=>[ps.Hover])));Ft(Q,{get triggerOn(){return n(le)},content:"Revoke Access",children:(W,V)=>{ts(W,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>te()(_())},children:(se,oe)=>{ga(se,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var ye=p(J,2);Mr.Root(ye,{color:"success",size:1,variant:"soft",children:(W,V)=>{var se=L("Connected");d(W,se)},$$slots:{default:!0}}),Ce(W=>U=Ct(z,1,"icon-button-wrapper svelte-js5lik",null,U,W),[()=>({active:n(X)})],xe),d(ke,ae)};Y(Me,ke=>{y(_()),g(()=>_().isConfigured)&&ke(De)},Ae)};Y(je,Me=>{y(_()),g(()=>!_().isConfigured&&_().authUrl)?Me(ge):Me(Le,!1)}),d(q,he)}}});var ce=p(j,2),me=q=>{var we=yo(),he=h(we);Ce(()=>{Ct(we,1,`status-message ${y(_()),g(()=>_().statusType)??""}`,"svelte-js5lik"),ze(he,(y(_()),g(()=>_().statusMessage)))}),d(q,we)};Y(ce,q=>{y(_()),g(()=>_().showStatus)&&q(me)}),mt("mouseenter",fe,()=>v(X,!0)),mt("mouseleave",fe,()=>v(X,!1)),d(F,fe),ia(E,"onToolApprovalConfigChange",I),Qe({onToolApprovalConfigChange:I})})(k,{get config(){return n(R)},get onAuthenticate(){return u()},get onRevokeAccess(){return T()},onToolApprovalConfigChange:ne()})});var x=p(Z,2);ot(x,1,l,k=>k.name,(k,R)=>{const F=xe(()=>(n(i),n(R),g(()=>n(i).find(E=>E.name===n(R).name))));No(k,{get mcpTool(){return n(F)},get config(){return n(R)}})}),d(P,O)},$$slots:{default:!0}}),d(r,S),Qe(),s()}var zo=m('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Lo=m('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Oo(r,e){Ye(e,!1);let t=w(e,"handleEnterEditMode",8),s=w(e,"envVarEntries",28,()=>[]);Xe();var a=Lo(),l=Se(a);ue(l,{size:1,weight:"medium",children:(N,pe)=>{var ve=L("Environment Variables");d(N,ve)},$$slots:{default:!0}});var i=p(l,2),o=h(i),c=h(o),u=N=>{var pe=nt(),ve=Se(pe);ot(ve,1,s,$=>$.id,($,S,f)=>{var B=zo(),P=h(B),C=h(P);Xt(C,{size:1,placeholder:"Name",class:"full-width",get value(){return n(S).key},set value(R){n(S).key=R,Ir(()=>s())},$$events:{focus(...R){var F;(F=t())==null||F.apply(this,R)},change:()=>function(R,F){const E=s().findIndex(_=>_.id===R);E!==-1&&(s(s()[E].key=F,!0),s(s()))}(n(S).id,n(S).key)},$$legacy:!0});var O=p(P),Z=h(O);Xt(Z,{size:1,placeholder:"Value",class:"full-width",get value(){return n(S).value},set value(R){n(S).value=R,Ir(()=>s())},$$events:{focus(...R){var F;(F=t())==null||F.apply(this,R)},change:()=>function(R,F){const E=s().findIndex(_=>_.id===R);E!==-1&&(s(s()[E].value=F,!0),s(s()))}(n(S).id,n(S).value)},$$legacy:!0});var x=p(O),k=h(x);Ft(k,{content:"Remove",children:(R,F)=>{Ke(R,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...E){var _;(_=t())==null||_.apply(this,E)},click:()=>{return E=n(S).id,t()(),void s(s().filter(_=>_.id!==E));var E}},$$slots:{iconLeft:(E,_)=>{ea(E,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),d($,B)}),d(N,pe)};Y(c,N=>{y(s()),g(()=>s().length>0)&&N(u)});var T=p(i,2),ne=h(T);Ke(ne,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){t()(),s([...s(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(N,pe)=>{var ve=L("Variable");d(N,ve)},$$slots:{default:!0,iconLeft:(N,pe)=>{hs(N,{slot:"iconLeft"})}}}),d(r,a),Qe()}var Zo=m("<div></div>"),Po=m(" <!>",1),Fo=m('<div class="server-name svelte-igdbzh"><!></div>'),jo=m('<div slot="header-left" class="l-header svelte-igdbzh"><!> <!> <!> <div class="command-text svelte-igdbzh"><!></div></div>'),Do=m('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Uo=m('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Vo=m('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),qo=m("<!> <!> <!>",1),Ho=m("<!> <!>",1),Bo=m('<div class="server-actions svelte-igdbzh" slot="header-right"><div class="status-controls svelte-igdbzh"><!> <!></div></div>'),Go=m('<div class="c-tool-item svelte-igdbzh"><div class="c-tool-info svelte-igdbzh"><div class="tool-status svelte-igdbzh"><div></div> <!></div> <div class="c-tool-description svelte-igdbzh"><!></div></div></div>'),Jo=m('<div slot="footer"></div>'),Wo=m('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>',1),Ko=m('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!> <div class="connection-type-buttons svelte-igdbzh"><!> <!></div></div></div>'),Yo=m('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Qo=m('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Xo=m('<!> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <!>',1),el=m('<form><div class="server-edit-form svelte-igdbzh"><div class="server-header svelte-igdbzh"><div class="server-title svelte-igdbzh"><div class="server-icon svelte-igdbzh"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-igdbzh"><div><!></div> <div class="form-actions svelte-igdbzh"><!> <!></div></div></div></form>');function Wr(r,e){var ce;Ye(e,!1);const t=G(),s=G(),a=G(),l=G(),i=G();let o=w(e,"server",8,null),c=w(e,"onDelete",8),u=w(e,"onAdd",8),T=w(e,"onSave",8),ne=w(e,"onEdit",8),N=w(e,"onToggleDisableServer",8),pe=w(e,"onJSONImport",8),ve=w(e,"onCancel",8),$=w(e,"disabledText",24,()=>{}),S=w(e,"warningText",24,()=>{}),f=w(e,"mode",12,"view"),B=w(e,"mcpServerError",12,""),P=G(((ce=o())==null?void 0:ce.name)??""),C=G(St(o())?"":os(o())?o().command:""),O=G(St(o())?o().url:""),Z=os(o())?o().env??{}:{},x=G(""),k=G(St(o())?o().type:"http"),R=G([]);E();let F=G(!0);function E(){v(R,Object.entries(Z).map(([me,q])=>({id:crypto.randomUUID(),key:me,value:q})))}let _=G(()=>{});function M(){o()&&f()==="view"&&(f("edit"),ne()(o()),n(_)())}let te=w(e,"busy",12,!1);function I({key:me,value:q}){return me.trim()&&q.trim()}async function A(){B(""),te(!0);const me=n(R).filter(I);Z=Object.fromEntries(me.map(({key:q,value:we})=>[q.trim(),we.trim()])),E();try{if(f()==="add"){const q={type:"stdio",name:n(P).trim(),command:n(C).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(Z).length>0?Z:void 0};await u()(q)}else if(f()==="addRemote"){const q={type:n(k),name:n(P).trim(),url:n(O).trim()};await u()(q)}else if(f()==="addJson"){try{JSON.parse(n(x))}catch(q){const we=q instanceof Error?q.message:String(q);throw new ht(`Invalid JSON format: ${we}`)}await pe()(n(x))}else if(f()==="edit"&&o()){if(St(o())){const q={...o(),type:n(k),name:n(P).trim(),url:n(O).trim()};await T()(q)}else if(os(o())){const q={...o(),name:n(P).trim(),command:n(C).trim(),arguments:"",env:Object.keys(Z).length>0?Z:void 0};await T()(q)}}}catch(q){B(q instanceof ht?q.message:"Failed to save server"),console.warn(q)}finally{te(!1)}}function ee(){var me,q;te(!1),B(""),(me=ve())==null||me(),v(x,""),v(P,((q=o())==null?void 0:q.name)??""),v(C,St(o())?"":os(o())?o().command:""),v(O,St(o())?o().url:""),Z=os(o())&&o().env?{...o().env}:{},v(k,St(o())?o().type:"http"),E()}$e(()=>y(o()),()=>{var me;v(t,((me=o())==null?void 0:me.tools)??[])}),$e(()=>(n(P),n(C)),()=>{n(P)&&n(C)&&B("")}),$e(()=>(y(f()),n(P),n(C),n(O)),()=>{v(s,!((f()!=="add"||n(P).trim()&&n(C).trim())&&(f()!=="addRemote"||n(P).trim()&&n(O).trim())))}),$e(()=>(y(f()),n(x)),()=>{v(a,f()==="addJson"&&!n(x).trim())}),$e(()=>(n(s),y(f()),n(a)),()=>{v(l,n(s)||f()==="view"||n(a))}),$e(()=>y(f()),()=>{v(i,(()=>{switch(f()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),lt(),Xe();var X=nt(),de=Se(X),fe=me=>{na(me,{get collapsed(){return n(F)},set collapsed(q){v(F,q)},$$slots:{header:(q,we)=>{Et(q,{slot:"header",$$slots:{"header-left":(he,je)=>{var ge=jo(),Le=h(ge),Me=z=>{ts(z,{size:1,variant:"ghost",$$events:{click:()=>v(F,!n(F))},children:(U,Q)=>{var le=nt(),ye=Se(le),W=se=>{Ja(se,{})},V=se=>{Ar(se,{})};Y(ye,se=>{n(F)?se(W):se(V,!1)}),d(U,le)},$$slots:{default:!0}})};Y(Le,z=>{n(t),g(()=>n(t).length>0)&&z(Me)});var Ae=p(Le,2);const De=xe(()=>$()||S());Ft(Ae,{get content(){return n(De)},children:(z,U)=>{var Q=Zo();let le;Ce(ye=>le=Ct(Q,1,"c-dot svelte-igdbzh",null,le,ye),[()=>({"c-green":!$(),"c-warning":!$()&&!!S(),"c-red":!!$(),"c-disabled":o().disabled})],xe),d(z,Q)},$$slots:{default:!0}});var ke=p(Ae,2);Ft(ke,{get content(){return y(o()),g(()=>o().name)},side:"top",align:"start",children:(z,U)=>{var Q=Fo(),le=h(Q);ue(le,{size:1,weight:"medium",children:(ye,W)=>{var V=Po(),se=Se(V),oe=p(se),ie=Ne=>{var Ee=L();Ce(()=>ze(Ee,`(${n(t),g(()=>n(t).length)??""}) tools`)),d(Ne,Ee)};Y(oe,Ne=>{n(t),g(()=>n(t).length>0)&&Ne(ie)}),Ce(()=>ze(se,`${y(o()),g(()=>o().name)??""} `)),d(ye,V)},$$slots:{default:!0}}),d(z,Q)},$$slots:{default:!0}});var ae=p(ke,2),H=h(ae);const J=xe(()=>(y(Bs),y(o()),g(()=>Bs(o()))));Ft(H,{get content(){return n(J)},side:"top",align:"start",children:(z,U)=>{ue(z,{color:"secondary",size:1,weight:"regular",children:(Q,le)=>{var ye=L();Ce(W=>ze(ye,W),[()=>(y(Bs),y(o()),g(()=>Bs(o())))],xe),d(Q,ye)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(he,ge)},"header-right":(he,je)=>{var ge=Bo(),Le=h(ge),Me=h(Le),Ae=ke=>{const ae=xe(()=>(y(o()),g(()=>!o().disabled)));yr(ke,{size:1,get checked(){return n(ae)},$$events:{change:()=>{o()&&N()(o().id),n(_)()}}})};Y(Me,ke=>{y(Lr),g(Lr)&&ke(Ae)});var De=p(Me,2);We.Root(De,{get requestClose(){return n(_)},set requestClose(ke){v(_,ke)},children:(ke,ae)=>{var H=Ho(),J=Se(H);We.Trigger(J,{children:(U,Q)=>{ts(U,{size:1,variant:"ghost-block",color:"neutral",children:(le,ye)=>{ln(le,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var z=p(J,2);We.Content(z,{side:"bottom",align:"end",children:(U,Q)=>{var le=qo(),ye=Se(le);We.Item(ye,{onSelect:M,children:(se,oe)=>{var ie=Do(),Ne=h(ie);dn(Ne,{});var Ee=p(Ne,2);ue(Ee,{size:1,weight:"medium",children:(Re,qe)=>{var He=L("Edit");d(Re,He)},$$slots:{default:!0}}),d(se,ie)},$$slots:{default:!0}});var W=p(ye,2);We.Item(W,{onSelect:()=>{(function(){if(o()){const se=Us.convertServerToJSON(o());navigator.clipboard.writeText(se)}})(),n(_)()},children:(se,oe)=>{var ie=Uo(),Ne=h(ie);en(Ne,{});var Ee=p(Ne,2);ue(Ee,{size:1,weight:"medium",children:(Re,qe)=>{var He=L("Copy JSON");d(Re,He)},$$slots:{default:!0}}),d(se,ie)},$$slots:{default:!0}});var V=p(W,2);We.Item(V,{color:"error",onSelect:()=>{c()(o().id),n(_)()},children:(se,oe)=>{var ie=Vo(),Ne=h(ie);ea(Ne,{});var Ee=p(Ne,2);ue(Ee,{size:1,weight:"medium",children:(Re,qe)=>{var He=L("Delete");d(Re,He)},$$slots:{default:!0}}),d(se,ie)},$$slots:{default:!0}}),d(U,le)},$$slots:{default:!0}}),d(ke,H)},$$slots:{default:!0},$$legacy:!0}),d(he,ge)}}})},footer:(q,we)=>{var he=Jo();ot(he,5,()=>n(t),qt,(je,ge)=>{var Le=Go(),Me=h(Le),Ae=h(Me),De=h(Ae);let ke;var ae=p(De,2);ue(ae,{size:1,weight:"medium",children:(z,U)=>{var Q=L();Ce(()=>ze(Q,(n(ge),g(()=>n(ge).definition.mcp_tool_name||n(ge).definition.name)))),d(z,Q)},$$slots:{default:!0}});var H=p(Ae,2),J=h(H);Ft(J,{get content(){return n(ge),g(()=>n(ge).definition.description)},align:"start",children:(z,U)=>{var Q=nt(),le=Se(Q),ye=W=>{ue(W,{size:1,color:"secondary",children:(V,se)=>{var oe=L();Ce(()=>ze(oe,(n(ge),g(()=>n(ge).definition.description)))),d(V,oe)},$$slots:{default:!0}})};Y(le,W=>{n(ge),g(()=>n(ge).definition.description)&&W(ye)}),d(z,Q)},$$slots:{default:!0}}),Ce(z=>ke=Ct(De,1,"tool-status-dot svelte-igdbzh",null,ke,z),[()=>({enabled:n(ge).enabled,disabled:!n(ge).enabled})],xe),d(je,Le)}),d(q,he)}},$$legacy:!0})},j=me=>{var q=el(),we=h(q),he=h(we),je=h(he),ge=h(je),Le=h(ge);ja(Le);var Me=p(ge,2);ue(Me,{color:"secondary",size:1,weight:"medium",children:(V,se)=>{var oe=L();Ce(()=>ze(oe,n(i))),d(V,oe)},$$slots:{default:!0}});var Ae=p(he,2),De=V=>{var se=Wo(),oe=Se(se),ie=h(oe),Ne=h(ie);ue(Ne,{size:1,weight:"medium",children:(He,vt)=>{var Be=L("Code Snippet");d(He,Be)},$$slots:{default:!0}});var Ee=p(oe,2),Re=h(Ee),qe=h(Re);oa(qe,{size:1,placeholder:"Paste JSON here...",get value(){return n(x)},set value(He){v(x,He)},$$legacy:!0}),d(V,se)},ke=(V,se)=>{var oe=ie=>{var Ne=Xo(),Ee=Se(Ne),Re=et=>{var Ge=Ko(),rt=h(Ge),$t=h(rt);ue($t,{size:1,weight:"medium",children:(Ot,Vs)=>{var ys=L("Connection Type");d(Ot,ys)},$$slots:{default:!0}});var it=p($t,2),Lt=h(it);const Kt=xe(()=>n(k)==="http"?"solid":"ghost"),fs=xe(()=>n(k)==="http"?"accent":"neutral");Ke(Lt,{size:1,get variant(){return n(Kt)},get color(){return n(fs)},type:"button",$$events:{click:()=>v(k,"http")},children:(Ot,Vs)=>{var ys=L("HTTP");d(Ot,ys)},$$slots:{default:!0}});var Ze=p(Lt,2);const tt=xe(()=>n(k)==="sse"?"solid":"ghost"),ft=xe(()=>n(k)==="sse"?"accent":"neutral");Ke(Ze,{size:1,get variant(){return n(tt)},get color(){return n(ft)},type:"button",$$events:{click:()=>v(k,"sse")},children:(Ot,Vs)=>{var ys=L("SSE");d(Ot,ys)},$$slots:{default:!0}}),d(et,Ge)};Y(Ee,et=>{y(f()),y(o()),g(()=>{var Ge,rt;return f()==="addRemote"||f()==="edit"&&(((Ge=o())==null?void 0:Ge.type)==="http"||((rt=o())==null?void 0:rt.type)==="sse")})&&et(Re)});var qe=p(Ee,2),He=h(qe),vt=h(He);Xt(vt,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return n(P)},set value(et){v(P,et)},$$events:{focus:M},$$slots:{label:(et,Ge)=>{ue(et,{slot:"label",size:1,weight:"medium",children:(rt,$t)=>{var it=L("Name");d(rt,it)},$$slots:{default:!0}})}},$$legacy:!0});var Be=p(qe,2),dt=et=>{var Ge=Yo(),rt=h(Ge),$t=h(rt);Xt($t,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return n(O)},set value(it){v(O,it)},$$events:{focus:M},$$slots:{label:(it,Lt)=>{ue(it,{slot:"label",size:1,weight:"medium",children:(Kt,fs)=>{var Ze=L("URL");d(Kt,Ze)},$$slots:{default:!0}})}},$$legacy:!0}),d(et,Ge)},At=et=>{var Ge=Qo(),rt=h(Ge),$t=h(rt);Xt($t,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return n(C)},set value(it){v(C,it)},$$events:{focus:M},$$slots:{label:(it,Lt)=>{ue(it,{slot:"label",size:1,weight:"medium",children:(Kt,fs)=>{var Ze=L("Command");d(Kt,Ze)},$$slots:{default:!0}})}},$$legacy:!0}),d(et,Ge)};Y(Be,et=>{y(f()),y(o()),g(()=>{var Ge,rt;return f()==="addRemote"||((Ge=o())==null?void 0:Ge.type)==="http"||((rt=o())==null?void 0:rt.type)==="sse"})?et(dt):et(At,!1)}),d(ie,Ne)};Y(V,ie=>{f()!=="add"&&f()!=="addRemote"&&f()!=="edit"||ie(oe)},se)};Y(Ae,V=>{f()==="addJson"?V(De):V(ke,!1)});var ae=p(Ae,2),H=V=>{Oo(V,{handleEnterEditMode:M,get envVarEntries(){return n(R)},set envVarEntries(se){v(R,se)},$$legacy:!0})};Y(ae,V=>{y(f()),y(St),y(o()),g(()=>(f()==="add"||f()==="edit")&&!St(o()))&&V(H)});var J=p(ae,2),z=h(J);let U;var Q=h(z);ks(Q,{variant:"soft",color:"error",size:1,children:(V,se)=>{var oe=L();Ce(()=>ze(oe,B())),d(V,oe)},$$slots:{default:!0,icon:(V,se)=>{Da(V,{slot:"icon"})}}});var le=p(z,2),ye=h(le);Ke(ye,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:ee},children:(V,se)=>{var oe=L("Cancel");d(V,oe)},$$slots:{default:!0}});var W=p(ye,2);Ke(W,{size:1,variant:"solid",color:"accent",get loading(){return te()},type:"submit",get disabled(){return n(l)},children:(V,se)=>{var oe=nt(),ie=Se(oe),Ne=Re=>{var qe=L("Import");d(Re,qe)},Ee=(Re,qe)=>{var He=Be=>{var dt=L("Add");d(Be,dt)},vt=(Be,dt)=>{var At=Ge=>{var rt=L("Add");d(Ge,rt)},et=(Ge,rt)=>{var $t=it=>{var Lt=L("Save");d(it,Lt)};Y(Ge,it=>{f()==="edit"&&it($t)},rt)};Y(Be,Ge=>{f()==="addRemote"?Ge(At):Ge(et,!1)},dt)};Y(Re,Be=>{f()==="add"?Be(He):Be(vt,!1)},qe)};Y(ie,Re=>{f()==="addJson"?Re(Ne):Re(Ee,!1)}),d(V,oe)},$$slots:{default:!0}}),Ce(V=>{Ct(q,1,"c-mcp-server-card "+(f()==="add"||f()==="addJson"||f()==="addRemote"?"add-server-section":"server-item"),"svelte-igdbzh"),U=Ct(z,1,"error-container svelte-igdbzh",null,U,V)},[()=>({"is-error":!!B()})],xe),mt("submit",q,on(A)),d(me,q)};return Y(de,me=>{f()==="view"&&o()?me(fe):me(j,!1)}),d(r,X),ia(e,"setLocalEnvVarFormState",E),Qe({setLocalEnvVarFormState:E})}var tl=m('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),sl=m('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),rl=m('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),al=m('<div class="installed-indicator svelte-8tbe79"><!></div>'),nl=m('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),il=m('<div class="mcp-service-item"><!></div>'),ol=m('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),ll=m('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),dl=m('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),cl=m('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function ul(r,e){Ye(e,!1);let t=w(e,"onMCPServerAdd",24,()=>{}),s=w(e,"servers",24,()=>[]);const a=[{label:cr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:cr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:cr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],l="easyMCPInstall.collapsed";let i=G(!1),o=G(!1),c=G(null),u=G({}),T=G({});function ne($){var P;if(!$.userInput)return;for(let C=0;C<$.userInput.length;C++){const O=$.userInput[C];let Z;if(Z=O.type==="environmentVariable"&&O.envVarName?O.envVarName:O.correspondingArg?O.correspondingArg:`input_${C}`,!((P=n(u)[Z])==null?void 0:P.trim())){const k=n(T)[Z];return void(k&&k.focus())}}let S=[$.command],f={};$.args&&S.push(...$.args);for(let C=0;C<$.userInput.length;C++){const O=$.userInput[C];let Z;Z=O.type==="environmentVariable"&&O.envVarName?O.envVarName:O.correspondingArg?O.correspondingArg:`input_${C}`;const x=n(u)[Z].trim(),k=`"${x}"`;if(O.type==="environmentVariable"&&O.envVarName)f[O.envVarName]=x;else if(O.correspondingArg){const R=S.indexOf(O.correspondingArg);R!==-1?S.splice(R+1,0,k):S.push(O.correspondingArg,k)}else S.push(k)}const B={type:"stdio",name:$.label,command:S.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(f).length>0?f:void 0};t()&&t()(B),v(c,null),v(u,{})}function N(){v(c,null),v(u,{})}$e(()=>{},()=>{const $=localStorage.getItem(l);if($!==null)try{v(i,JSON.parse($))}catch{localStorage.removeItem(l)}v(o,!0)}),$e(()=>(n(o),n(i)),()=>{typeof window<"u"&&n(o)&&localStorage.setItem(l,JSON.stringify(n(i)))}),lt(),Xe();var pe=cl(),ve=h(pe);na(ve,{get collapsed(){return n(i)},set collapsed($){v(i,$)},children:($,S)=>{var f=ol(),B=h(f);ot(B,5,()=>a,qt,(P,C)=>{var O=il();Et(h(O),{$$slots:{"header-left":(Z,x)=>{var k=rl(),R=h(k),F=h(R);ue(F,{size:1,weight:"medium",children:(I,A)=>{var ee=L();Ce(()=>ze(ee,(n(C),g(()=>n(C).label)))),d(I,ee)},$$slots:{default:!0}});var E=p(R,2),_=I=>{ue(I,{size:1,color:"secondary",children:(A,ee)=>{var X=L();Ce(()=>ze(X,(n(C),g(()=>n(C).description)))),d(A,X)},$$slots:{default:!0}})};Y(E,I=>{n(C),g(()=>n(C).description)&&I(_)});var M=p(E,2),te=I=>{var A=sl(),ee=h(A);ot(ee,1,()=>(n(C),g(()=>n(C).userInput)),qt,(j,ce,me)=>{var q=tl();const we=xe(()=>(n(ce),g(()=>n(ce).type==="environmentVariable"&&n(ce).envVarName?n(ce).envVarName:n(ce).correspondingArg||`input_${me}`)));var he=h(q);ue(he,{size:1,weight:"medium",color:"neutral",children:(Ae,De)=>{var ke=L();Ce(()=>ze(ke,(n(ce),g(()=>n(ce).label)))),d(Ae,ke)},$$slots:{default:!0}});var je=p(he,2),ge=Ae=>{ue(Ae,{size:1,color:"secondary",children:(De,ke)=>{var ae=L();Ce(()=>ze(ae,(n(ce),g(()=>n(ce).description)))),d(De,ae)},$$slots:{default:!0}})};Y(je,Ae=>{n(ce),g(()=>n(ce).description)&&Ae(ge)});var Le=p(je,2);const Me=xe(()=>(n(ce),g(()=>n(ce).placeholder||"")));Xt(Le,{get placeholder(){return n(Me)},size:1,variant:"surface",get value(){return n(u)[n(we)]},set value(Ae){lr(u,n(u)[n(we)]=Ae)},get textInput(){return n(T)[n(we)]},set textInput(Ae){lr(T,n(T)[n(we)]=Ae)},$$events:{keydown:Ae=>{Ae.key==="Enter"?ne(n(C)):Ae.key==="Escape"&&N()}},$$legacy:!0}),d(j,q)});var X=p(ee,2),de=h(X);Ke(de,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>ne(n(C))},children:(j,ce)=>{var me=L("Install");d(j,me)},$$slots:{default:!0}});var fe=p(de,2);Ke(fe,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:N},children:(j,ce)=>{var me=L("Cancel");d(j,me)},$$slots:{default:!0}}),d(I,A)};Y(M,I=>{n(c),n(C),g(()=>n(c)===n(C).label&&n(C).userInput)&&I(te)}),d(Z,k)},"header-right":(Z,x)=>{var k=nl(),R=h(k),F=_=>{var M=al(),te=h(M);Mr.Root(te,{color:"success",size:1,variant:"soft",children:(I,A)=>{var ee=L("Installed");d(I,ee)},$$slots:{default:!0}}),d(_,M)},E=(_,M)=>{var te=I=>{ts(I,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(A){if(s().some(X=>X.name===A.label))return;if(A.userInput&&A.userInput.length>0)return v(u,{}),A.userInput.forEach((X,de)=>{let fe;fe=X.type==="environmentVariable"&&X.envVarName?X.envVarName:X.correspondingArg?X.correspondingArg:`input_${de}`,lr(u,n(u)[fe]=X.defaultValue||"")}),void v(c,A.label);const ee={type:"stdio",name:A.label,command:A.command,arguments:"",useShellInterpolation:!0};t()&&t()(ee)}(n(C))},children:(A,ee)=>{hs(A,{})},$$slots:{default:!0}})};Y(_,I=>{n(c),n(C),g(()=>n(c)!==n(C).label)&&I(te)},M)};Y(R,_=>{y(s()),n(C),g(()=>s().some(M=>M.name===n(C).label))?_(F):_(E,!1)}),d(Z,k)}}}),d(P,O)}),d($,f)},$$slots:{default:!0,header:($,S)=>{var f=dl();Et(h(f),{$$slots:{"header-left":(B,P)=>{var C=ll(),O=h(C);an(O,{});var Z=p(O,2);ue(Z,{color:"neutral",size:1,weight:"light",class:"card-title",children:(x,k)=>{var R=L("Easy MCP Installation");d(x,R)},$$slots:{default:!0}}),d(B,C)}}}),d($,f)}},$$legacy:!0}),d(r,pe),Qe()}const hl={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},pl={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},vl=Ra(),ml=new class{constructor(r){Oe(this,"strings");let e={[dr.vscode]:{},[dr.jetbrains]:pl,[dr.web]:{}};this.strings={...hl,...e[r]}}get(r){return this.strings[r]}}(vl.clientType);var gl=m('<div class="section-heading-text">MCP</div>'),fl=m(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),yl=m('<div class="section-heading-text">Terminal</div>'),_l=m("<!> <!>",1),wl=m('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function Cl(r,e){Ye(e,!1);const t=G();let s=w(e,"supportedShells",24,()=>[]),a=w(e,"selectedShell",24,()=>{}),l=w(e,"startupScript",28,()=>{}),i=w(e,"onShellSelect",8),o=w(e,"onStartupScriptChange",8),c=G();$e(()=>y(a()),()=>{var f;v(t,a()?(f=a(),s().find(B=>B.friendlyName===f)):void 0)}),lt(),Xe();var u=wl(),T=h(u);ue(T,{size:1,weight:"regular",color:"secondary",children:(f,B)=>{var P=yl();d(f,P)},$$slots:{default:!0}});var ne=p(T,2),N=h(ne);ue(N,{size:1,children:(f,B)=>{var P=L("Shell:");d(f,P)},$$slots:{default:!0}});var pe=p(N,2);We.Root(pe,{get requestClose(){return n(c)},set requestClose(f){v(c,f)},children:(f,B)=>{var P=_l(),C=Se(P);We.Trigger(C,{children:(Z,x)=>{const k=xe(()=>(y(s()),g(()=>s().length===0)));Ke(Z,{size:1,variant:"outline",color:"neutral",get disabled(){return n(k)},children:(R,F)=>{var E=nt(),_=Se(E),M=I=>{var A=L();Ce(()=>ze(A,`${n(t),g(()=>n(t).friendlyName)??""}
            (${n(t),g(()=>n(t).supportString)??""})`)),d(I,A)},te=(I,A)=>{var ee=de=>{var fe=L("No shells available");d(de,fe)},X=de=>{var fe=L("Select a shell");d(de,fe)};Y(I,de=>{y(s()),g(()=>s().length===0)?de(ee):de(X,!1)},A)};Y(_,I=>{n(t),y(s()),g(()=>n(t)&&s().length>0)?I(M):I(te,!1)}),d(R,E)},$$slots:{default:!0,iconRight:(R,F)=>{Va(R)}}})},$$slots:{default:!0}});var O=p(C,2);We.Content(O,{side:"bottom",align:"start",children:(Z,x)=>{var k=nt(),R=Se(k),F=_=>{var M=nt(),te=Se(M);ot(te,1,s,I=>I.friendlyName,(I,A)=>{const ee=xe(()=>(y(a()),n(A),g(()=>a()===n(A).friendlyName)));We.Item(I,{onSelect:()=>{i()(n(A).friendlyName),n(c)()},get highlight(){return n(ee)},children:(X,de)=>{var fe=L();Ce(()=>ze(fe,`${n(A),g(()=>n(A).friendlyName)??""}
              (${n(A),g(()=>n(A).supportString)??""})`)),d(X,fe)},$$slots:{default:!0}})}),d(_,M)},E=_=>{We.Label(_,{children:(M,te)=>{var I=L("No shells available");d(M,I)},$$slots:{default:!0}})};Y(R,_=>{y(s()),g(()=>s().length>0)?_(F):_(E,!1)}),d(Z,k)},$$slots:{default:!0}}),d(f,P)},$$slots:{default:!0},$$legacy:!0});var ve=p(ne,2),$=h(ve);ue($,{size:1,children:(f,B)=>{var P=L("Start-up script: Code to run wherever a new terminal is opened");d(f,P)},$$slots:{default:!0}});var S=p($,2);oa(S,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return l()},set value(f){l(f)},$$events:{change:function(f){const B=f.target;o()(B.value)}},$$legacy:!0}),d(r,u),Qe()}var bl=m('<div class="section-heading-text">Sound Settings</div>'),$l=m('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Sl=m('<div slot="header-right"><!></div>'),kl=m('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),xl=m('<div slot="header-right"><!></div>'),Ml=m('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Al=m('<div class="section-heading-text">Agent Settings</div>'),Tl=m('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Nl=m('<div slot="header-right"><!></div>'),Rl=m('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),El=m('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function Il(r,e){let t=w(e,"tools",24,()=>[]),s=w(e,"isMCPEnabled",8,!0),a=w(e,"isMCPImportEnabled",8,!0),l=w(e,"isTerminalEnabled",8,!0),i=w(e,"isSoundCategoryEnabled",8,!1),o=w(e,"isAgentCategoryEnabled",8,!1),c=w(e,"isSwarmModeFeatureFlagEnabled",8,!1),u=w(e,"hasEverUsedRemoteAgent",8,!1),T=w(e,"onAuthenticate",8),ne=w(e,"onRevokeAccess",8),N=w(e,"onToolApprovalConfigChange",8,()=>{}),pe=w(e,"onMCPServerAdd",8),ve=w(e,"onMCPServerSave",8),$=w(e,"onMCPServerDelete",8),S=w(e,"onMCPServerToggleDisable",8),f=w(e,"onMCPServerJSONImport",8),B=w(e,"onCancel",24,()=>{}),P=w(e,"supportedShells",24,()=>[]),C=w(e,"selectedShell",24,()=>{}),O=w(e,"startupScript",24,()=>{}),Z=w(e,"onShellSelect",8,()=>{}),x=w(e,"onStartupScriptChange",8,()=>{});var k=El(),R=h(k);Io(R,{title:"Services",get tools(){return t()},get onAuthenticate(){return T()},get onRevokeAccess(){return ne()},onToolApprovalConfigChange:N()});var F=p(R,2),E=X=>{(function(de,fe){Ye(fe,!1);const[j,ce]=Nt(),me=()=>st(le,"$allServers",j),q=G(),we=G();let he=w(fe,"onMCPServerAdd",8),je=w(fe,"onMCPServerSave",8),ge=w(fe,"onMCPServerDelete",8),Le=w(fe,"onMCPServerToggleDisable",8),Me=w(fe,"onCancel",24,()=>{}),Ae=w(fe,"onMCPServerJSONImport",8),De=w(fe,"isMCPImportEnabled",8,!0),ke=G(null),ae=G(null);function H(){var Ze;v(ke,null),v(ae,null),(Ze=Me())==null||Ze()}let J=G([]);const z=ds(xs.key),U=Nr(),Q=z.getEnableNativeRemoteMcp(),le=U.getServers();function ye(Ze){v(ke,Ze.id)}function W(Ze){return async function(...tt){const ft=await Ze(...tt);return v(ae,null),v(ke,null),ft}}const V=W(he()),se=W(je()),oe=W(Ae()),ie=W(ge()),Ne=W(Le()),Ee=ml.get("mcpDocsURL");$e(()=>(n(ae),n(ke)),()=>{v(q,n(ae)==="add"||n(ae)==="addJson"||n(ae)==="addRemote"||n(ke)!==null)}),$e(()=>me(),()=>{v(J,Q?Ua(me()):me())}),$e(()=>n(J),()=>{v(we,Us.parseServerValidationMessages(n(J)))}),lt(),Xe();var Re=fl(),qe=Se(Re),He=h(qe),vt=h(He);ue(vt,{size:1,weight:"regular",color:"secondary",children:(Ze,tt)=>{var ft=gl();d(Ze,ft)},$$slots:{default:!0}});var Be=p(He,2),dt=p(h(Be)),At=p(Be,2);ul(At,{get onMCPServerAdd(){return V},get servers(){return n(J)}});var et=p(At,2);ot(et,1,()=>n(J),Ze=>Ze.id,(Ze,tt)=>{const ft=xe(()=>(n(ke),n(tt),g(()=>n(ke)===n(tt).id?"edit":"view"))),Ot=xe(()=>(n(we),n(tt),g(()=>n(we).errors.get(n(tt).id)))),Vs=xe(()=>(n(we),n(tt),g(()=>n(we).warnings.get(n(tt).id))));Wr(Ze,{get mode(){return n(ft)},get server(){return n(tt)},get onAdd(){return V},get onSave(){return se},get onDelete(){return ie},get onToggleDisableServer(){return Ne},onEdit:ye,onCancel:H,get onJSONImport(){return oe},get disabledText(){return n(Ot)},get warningText(){return n(Vs)}})});var Ge=p(qe,2),rt=Ze=>{Wr(Ze,{get mode(){return n(ae)},get onAdd(){return V},get onSave(){return se},get onDelete(){return ie},get onToggleDisableServer(){return Ne},onEdit:ye,onCancel:H,get onJSONImport(){return oe}})};Y(Ge,Ze=>{n(ae)!=="add"&&n(ae)!=="addJson"&&n(ae)!=="addRemote"||Ze(rt)});var $t=p(Ge,2),it=h($t);Ke(it,{get disabled(){return n(q)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{v(ae,"add")}},children:(Ze,tt)=>{var ft=L("Add MCP");d(Ze,ft)},$$slots:{default:!0,iconLeft:(Ze,tt)=>{hs(Ze,{slot:"iconLeft"})}}});var Lt=p(it,2);Ke(Lt,{get disabled(){return n(q)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{v(ae,"addRemote")}},children:(Ze,tt)=>{var ft=L("Add remote MCP");d(Ze,ft)},$$slots:{default:!0,iconLeft:(Ze,tt)=>{hs(Ze,{slot:"iconLeft"})}}});var Kt=p(Lt,2),fs=Ze=>{Ke(Ze,{get disabled(){return n(q)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{v(ae,"addJson")}},children:(tt,ft)=>{var Ot=L("Import from JSON");d(tt,Ot)},$$slots:{default:!0,iconLeft:(tt,ft)=>{ra(tt,{slot:"iconLeft"})}}})};Y(Kt,Ze=>{De()&&Ze(fs)}),Ce(()=>Qt(dt,"href",Ee)),d(de,Re),Qe(),ce()})(X,{get onMCPServerAdd(){return pe()},get onMCPServerSave(){return ve()},get onMCPServerDelete(){return $()},get onMCPServerToggleDisable(){return S()},get onMCPServerJSONImport(){return f()},get onCancel(){return B()},get isMCPImportEnabled(){return a()}})};Y(F,X=>{s()&&X(E)});var _=p(F,2),M=X=>{Cl(X,{get supportedShells(){return P()},get selectedShell(){return C()},get startupScript(){return O()},onShellSelect:Z(),onStartupScriptChange:x()})};Y(_,X=>{l()&&X(M)});var te=p(_,2),I=X=>{(function(de,fe){Ye(fe,!1);const[j,ce]=Nt(),me=()=>st(n(q),"$currentSettings",j),q=G(),we=G(),he=ds(_r.key);async function je(){return await he.playAgentComplete(),"success"}$e(()=>{},()=>{Ws(v(q,he.getCurrentSettings),"$currentSettings",j)}),$e(()=>me(),()=>{v(we,me().enabled)}),lt(),Xe();var ge=Ml(),Le=Se(ge);ue(Le,{size:1,weight:"regular",color:"secondary",children:(ae,H)=>{var J=bl();d(ae,J)},$$slots:{default:!0}});var Me=p(Le,2),Ae=h(Me);Et(Ae,{$$slots:{"header-left":(ae,H)=>{var J=$l(),z=h(J),U=h(z);ue(U,{size:2,weight:"medium",children:(ye,W)=>{var V=L("Enable Sound Effects");d(ye,V)},$$slots:{default:!0}});var Q=p(z,2),le=h(Q);ue(le,{size:1,weight:"medium",children:(ye,W)=>{var V=L("Play a sound when an agent completes a task");d(ye,V)},$$slots:{default:!0}}),d(ae,J)},"header-right":(ae,H)=>{var J=Sl(),z=h(J);yr(z,{size:1,get checked(){return n(we)},$$events:{change:()=>he.updateEnabled(!n(we))}}),d(ae,J)}}});var De=p(Ae,2),ke=ae=>{Et(ae,{$$slots:{"header-left":(H,J)=>{var z=kl(),U=h(z),Q=h(U);ue(Q,{size:2,weight:"medium",children:(W,V)=>{var se=L("Test Sound");d(W,se)},$$slots:{default:!0}});var le=p(U,2),ye=h(le);ue(ye,{size:1,weight:"medium",children:(W,V)=>{var se=L("Play a sample of the agent completion sound");d(W,se)},$$slots:{default:!0}}),d(H,z)},"header-right":(H,J)=>{var z=xl(),U=h(z);const Q=xe(()=>n(we)?"":"Enable sound effects to test"),le=xe(()=>(y(ps),g(()=>[ps.Hover])));Ft(U,{get content(){return n(Q)},get triggerOn(){return n(le)},children:(ye,W)=>{const V=xe(()=>!n(we));cn(ye,{size:1,defaultColor:"neutral",get enabled(){return n(we)},stickyColor:!1,get disabled(){return n(V)},onClick:je,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(se,oe)=>{var ie=L("Play");d(se,ie)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(H,z)}}})};Y(De,ae=>{n(we)&&ae(ke)}),d(de,ge),Qe(),ce()})(X,{})};Y(te,X=>{i()&&X(I)});var A=p(te,2),ee=X=>{(function(de,fe){Ye(fe,!1);const[j,ce]=Nt(),me=()=>st(n(q),"$currentSettings",j),q=G(),we=G();let he=w(fe,"isSwarmModeEnabled",8,!1),je=w(fe,"hasEverUsedRemoteAgent",8,!1);const ge=ds(Fs.key);$e(()=>{},()=>{Ws(v(q,ge.getCurrentSettings),"$currentSettings",j)}),$e(()=>me(),()=>{v(we,me().enabled)}),lt(),Xe();var Le=nt(),Me=Se(Le),Ae=De=>{var ke=Rl(),ae=Se(ke);ue(ae,{size:1,weight:"regular",color:"secondary",children:(J,z)=>{var U=Al();d(J,U)},$$slots:{default:!0}});var H=p(ae,2);Et(h(H),{$$slots:{"header-left":(J,z)=>{var U=Tl(),Q=h(U),le=h(Q);ue(le,{size:2,weight:"medium",children:(oe,ie)=>{var Ne=L("Enable Swarm Mode");d(oe,Ne)},$$slots:{default:!0}});var ye=p(Q,2),W=h(ye);ue(W,{size:1,weight:"medium",children:(oe,ie)=>{var Ne=L("Allow agents to coordinate and work together on complex tasks");d(oe,Ne)},$$slots:{default:!0}});var V=p(ye,2),se=h(V);ue(se,{size:1,weight:"regular",color:"secondary",children:(oe,ie)=>{var Ne=L(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);d(oe,Ne)},$$slots:{default:!0}}),d(J,U)},"header-right":(J,z)=>{var U=Nl(),Q=h(U);yr(Q,{size:1,get checked(){return n(we)},$$events:{change:()=>ge.updateEnabled(!n(we))}}),d(J,U)}}}),d(De,ke)};Y(Me,De=>{he()&&je()&&De(Ae)}),d(de,Le),Qe(),ce()})(X,{get isSwarmModeEnabled(){return c()},get hasEverUsedRemoteAgent(){return u()}})};Y(A,X=>{o()&&X(ee)}),d(r,k)}var zl=bt("<svg><!></svg>");function Ll(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=zl();js(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),d(r,s)}var Ol=bt("<svg><!></svg>");function Zl(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=Ol();js(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),d(r,s)}var Pl=m('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function _a(r,e){Ye(e,!1);const[t,s]=Nt(),a=xr();let l=w(e,"userGuidelines",12,""),i=w(e,"userGuidelinesLengthLimit",24,()=>{}),o=w(e,"updateUserGuideline",8,()=>!1);const c=ct(void 0);function u(){const N=l().trim();if(st(c,"$originalValue",t)!==N){if(!o()(N))throw i()&&N.length>i()?`The user guideline must be less than ${i()} character long`:"An error occurred updating the user";zr(c,N)}}Kr(()=>{zr(c,l().trim())}),Yr(()=>{u()}),Xe();var T=Pl(),ne=h(T);hn(ne,{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:u,get value(){return l()},set value(N){l(N)},$$events:{focus:N=>{a("focus",N)}},$$legacy:!0}),d(r,T),Qe(),s()}class Fl{constructor(e,t,s){Oe(this,"_showCreateRuleDialog",ct(!1));Oe(this,"_createRuleError",ct(""));Oe(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=s;const a=new ta;this._extensionClient=new sa(e,t,a)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:qs.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const s=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(s)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===Fr?this._extensionClient.openFile({repoRoot:t,pathName:Fr}):this._extensionClient.openFile({repoRoot:t,pathName:`${Wa}/${Ka}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:Ve.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?qs.selectedDirectory:(e.directoryOrFile,qs.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:qs.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var jl=m("<!> <!> <!>",1),Dl=m('<div slot="footer"><!> <!></div>'),Ul=m('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),Vl=m('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),ql=m("<!> <!>",1),Hl=m("<!> <!>",1),Bl=m("<!> <!>",1),Gl=m("<!> <!> <!> <!>",1),Jl=m('<div slot="body" class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),Wl=m('<div slot="footer"><!> <!></div>');function Kl(r,e){Ye(e,!1);const[t,s]=Nt(),a=()=>st(n(pe),"$focusedIndex",t),l=G(),i=xr();let o=w(e,"show",8,!1),c=w(e,"options",24,()=>[]),u=w(e,"isLoading",8,!1),T=w(e,"errorMessage",8,""),ne=w(e,"successMessage",8,""),N=G(n(l)),pe=G(void 0),ve=G(()=>{});function $(){n(N)&&!u()&&i("select",n(N))}function S(){u()||(i("cancel"),v(N,n(l)))}$e(()=>y(c()),()=>{v(l,c().length>0?c()[0]:null)}),$e(()=>(y(o()),n(l)),()=>{o()&&v(N,n(l))}),lt(),Xe(),mt("keydown",ar,function(f){o()&&!u()&&(f.key==="Escape"?(f.preventDefault(),S()):f.key==="Enter"&&n(N)&&(f.preventDefault(),$()))}),la(r,{get show(){return o()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return u()},get preventEscapeClose(){return u()},$$events:{cancel:S},$$slots:{body:(f,B)=>{var P=Jl(),C=h(P),O=x=>{var k=Ul();d(x,k)},Z=x=>{var k=Gl(),R=Se(k);ue(R,{size:2,color:"secondary",children:(A,ee)=>{var X=L("Select existing rules to auto import to .augment/rules");d(A,X)},$$slots:{default:!0}});var F=p(R,2);const E=xe(()=>(y(c()),g(()=>c().length===0?[]:void 0)));We.Root(F,{get triggerOn(){return n(E)},get requestClose(){return n(ve)},set requestClose(A){v(ve,A)},get focusedIndex(){return n(pe)},set focusedIndex(A){Ws(v(pe,A),"$focusedIndex",t)},children:(A,ee)=>{var X=Bl(),de=Se(X);We.Trigger(de,{children:(j,ce)=>{var me=Vl(),q=h(me),we=p(q,2);Ar(we,{class:"c-dropdown-chevron"}),Ce(()=>Ma(q,(n(N),g(()=>n(N)?n(N).label:"Existing rules")))),d(j,me)},$$slots:{default:!0}});var fe=p(de,2);We.Content(fe,{align:"start",side:"bottom",children:(j,ce)=>{var me=Hl(),q=Se(me);ot(q,1,c,qt,(je,ge)=>{const Le=xe(()=>(n(N),n(ge),g(()=>{var Me;return((Me=n(N))==null?void 0:Me.label)===n(ge).label})));We.Item(je,{onSelect:()=>function(Me){v(N,Me),n(ve)()}(n(ge)),get highlight(){return n(Le)},children:(Me,Ae)=>{var De=L();Ce(()=>ze(De,(n(ge),g(()=>n(ge).label)))),d(Me,De)},$$slots:{default:!0}})});var we=p(q,2),he=je=>{var ge=ql(),Le=Se(ge);We.Separator(Le,{});var Me=p(Le,2);We.Label(Me,{children:(Ae,De)=>{var ke=L();Ce(()=>ze(ke,(a(),y(c()),n(N),g(()=>{var ae;return a()!==void 0?c()[a()].description:(ae=n(N))==null?void 0:ae.description})))),d(Ae,ke)},$$slots:{default:!0}}),d(je,ge)};Y(we,je=>{(a()!==void 0||n(N))&&je(he)}),d(j,me)},$$slots:{default:!0}}),d(A,X)},$$slots:{default:!0},$$legacy:!0});var _=p(F,2),M=A=>{ks(A,{variant:"soft",color:"error",size:1,children:(ee,X)=>{var de=L();Ce(()=>ze(de,T())),d(ee,de)},$$slots:{default:!0,icon:(ee,X)=>{Js(ee,{slot:"icon"})}}})};Y(_,A=>{T()&&A(M)});var te=p(_,2),I=A=>{ks(A,{variant:"soft",color:"success",size:1,children:(ee,X)=>{var de=L();Ce(()=>ze(de,ne())),d(ee,de)},$$slots:{default:!0,icon:(ee,X)=>{qa(ee,{slot:"icon"})}}})};Y(te,A=>{ne()&&A(I)}),d(x,k)};Y(C,x=>{y(c()),g(()=>c().length===0)?x(O):x(Z,!1)}),d(f,P)},footer:(f,B)=>{var P=Wl(),C=h(P);Ke(C,{variant:"solid",color:"neutral",get disabled(){return u()},$$events:{click:S},children:(x,k)=>{var R=L("Cancel");d(x,R)},$$slots:{default:!0}});var O=p(C,2),Z=x=>{const k=xe(()=>!n(N)||u());Ke(x,{color:"accent",variant:"solid",get disabled(){return n(k)},get loading(){return u()},$$events:{click:$},children:(R,F)=>{var E=L();Ce(()=>ze(E,u()?"Importing...":"Import ")),d(R,E)},$$slots:{default:!0}})};Y(O,x=>{y(c()),g(()=>c().length>0)&&x(Z)}),d(f,P)}}}),Qe(),s()}var Yl=m('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),Ql=m('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),Xl=m('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),ed=m('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),td=m('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),sd=m("<!> <!>",1),rd=m("<!> <!>",1),ad=m("<!> <!>",1),nd=m(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function id(r,e){Ye(e,!1);const[t,s]=Nt(),a=()=>st(f,"$rulesFiles",t),l=()=>st(n(F),"$importFocusedIndex",t),i=G(),o=G(),c=G();let u=w(e,"userGuidelines",8,""),T=w(e,"userGuidelinesLengthLimit",24,()=>{}),ne=w(e,"workspaceGuidelinesLengthLimit",24,()=>{}),N=w(e,"workspaceGuidelinesContent",8,""),pe=w(e,"updateUserGuideline",8,()=>!1);const ve=new Qr(at),$=new Ya(ve),S=new Fl(at,ve,$);ve.registerConsumer($);const f=$.getCachedRules(),B=S.getShowCreateRuleDialog(),P=S.getCreateRuleError();let C=G(!1),O=G([]),Z=G(!1),x=G(""),k=G("");const R=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let F=G(void 0),E=G(()=>{});async function _(H){try{H.id==="select_file_or_directory"?await S.selectFileToImport():H.id==="auto_import"&&await async function(){try{v(x,""),v(k,"");const J=await S.getAutoImportOptions();v(O,J.data.options),v(C,!0)}catch(J){console.error("Failed to get auto-import options:",J),v(x,"Failed to detect existing rules in workspace.")}}()}catch(J){console.error("Failed to handle import select:",J)}n(E)&&n(E)()}Kr(()=>{$.requestRules()}),$e(()=>y(ne()),()=>{v(i,ne())}),$e(()=>(n(o),n(c),a(),y(N()),n(i)),()=>{var H;H=tn({rules:a(),workspaceGuidelinesContent:N(),rulesAndGuidelinesLimit:n(i)}),v(o,H.isOverLimit),v(c,H.warningMessage)}),lt(),Xe();var M=nd();mt("message",ar,function(...H){var J;(J=ve.onMessageFromExtension)==null||J.apply(this,H)});var te=Se(M),I=h(te),A=h(I);ue(A,{class:"c-section-header",size:3,color:"primary",children:(H,J)=>{var z=L("Rules");d(H,z)},$$slots:{default:!0}});var ee=p(A,2),X=p(h(ee)),de=h(X);ue(de,{size:1,weight:"regular",children:(H,J)=>{var z=L("Learn more");d(H,z)},$$slots:{default:!0}});var fe=p(ee,2),j=H=>{ks(H,{variant:"soft",color:"warning",size:1,children:(J,z)=>{var U=L();Ce(()=>ze(U,n(c))),d(J,U)},$$slots:{default:!0,icon:(J,z)=>{Js(J,{slot:"icon"})}}})};Y(fe,H=>{n(o)&&H(j)});var ce=p(fe,2),me=h(ce),q=H=>{var J=Yl(),z=h(J);ue(z,{size:1,color:"neutral",children:(U,Q)=>{var le=L("No rules files found");d(U,le)},$$slots:{default:!0}}),d(H,J)},we=H=>{var J=nt(),z=Se(J);ot(z,1,a,U=>U.path,(U,Q)=>{Et(U,{isClickable:!0,$$events:{click:()=>S.openRule(n(Q).path)},$$slots:{"header-left":(le,ye)=>{var W=Ql(),V=h(W),se=h(V),oe=Re=>{Ft(Re,{content:"No description found",children:(qe,He)=>{Js(qe,{})},$$slots:{default:!0}})},ie=Re=>{Ga(Re,{})};Y(se,Re=>{n(Q),y(Or),g(()=>n(Q).type===Or.AGENT_REQUESTED&&!n(Q).description)?Re(oe):Re(ie,!1)});var Ne=p(V,2),Ee=h(Ne);ue(Ee,{size:1,color:"neutral",children:(Re,qe)=>{var He=L();Ce(()=>ze(He,(n(Q),g(()=>n(Q).path)))),d(Re,He)},$$slots:{default:!0}}),d(le,W)},"header-right":(le,ye)=>{var W=Xl(),V=h(W),se=h(V),oe=h(se);pn(oe,{get rule(){return n(Q)},onSave:async(Ee,Re)=>{await $.updateRuleContent({type:Ee,path:n(Q).path,content:n(Q).content,description:Re})}});var ie=p(se,2);Ke(ie,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ee=>{Ee.stopPropagation(),S.openRule(n(Q).path)}},$$slots:{iconRight:(Ee,Re)=>{Ha(Ee,{slot:"iconRight"})}}});var Ne=p(ie,2);Ke(Ne,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ee=>{Ee.stopPropagation(),S.deleteRule(n(Q).path)}},$$slots:{iconRight:(Ee,Re)=>{Ba(Ee,{slot:"iconRight"})}}}),d(le,W)}}})}),d(H,J)};Y(me,H=>{a(),g(()=>a().length===0)?H(q):H(we,!1)});var he=p(ce,2),je=h(he);Ke(je,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>S.createRule()},children:(H,J)=>{var z=ed(),U=h(z);hs(U,{slot:"iconLeft"}),d(H,z)},$$slots:{default:!0}});var ge=p(je,2);We.Root(ge,{get requestClose(){return n(E)},set requestClose(H){v(E,H)},get focusedIndex(){return n(F)},set focusedIndex(H){Ws(v(F,H),"$importFocusedIndex",t)},children:(H,J)=>{var z=ad(),U=Se(z);We.Trigger(U,{children:(le,ye)=>{Ke(le,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(W,V)=>{var se=td(),oe=h(se);ra(oe,{slot:"iconLeft"});var ie=p(oe,2);Ar(ie,{slot:"iconRight"}),d(W,se)},$$slots:{default:!0}})},$$slots:{default:!0}});var Q=p(U,2);We.Content(Q,{align:"start",side:"bottom",children:(le,ye)=>{var W=rd(),V=Se(W);ot(V,1,()=>R,ie=>ie.id,(ie,Ne)=>{We.Item(ie,{onSelect:()=>_(n(Ne)),children:(Ee,Re)=>{var qe=L();Ce(()=>ze(qe,(n(Ne),g(()=>n(Ne).label)))),d(Ee,qe)},$$slots:{default:!0}})});var se=p(V,2),oe=ie=>{var Ne=sd(),Ee=Se(Ne);We.Separator(Ee,{});var Re=p(Ee,2);We.Label(Re,{children:(qe,He)=>{var vt=L();Ce(()=>ze(vt,(l(),g(()=>l()!==void 0?R[l()].description:R[0])))),d(qe,vt)},$$slots:{default:!0}}),d(ie,Ne)};Y(se,ie=>{l()!==void 0&&ie(oe)}),d(le,W)},$$slots:{default:!0}}),d(H,z)},$$slots:{default:!0},$$legacy:!0});var Le=p(I,2),Me=h(Le);ue(Me,{class:"c-section-header",size:3,color:"primary",children:(H,J)=>{var z=L("User Guidelines");d(H,z)},$$slots:{default:!0}});var Ae=p(Me,2),De=p(h(Ae)),ke=h(De);ue(ke,{size:1,weight:"regular",children:(H,J)=>{var z=L("Learn more");d(H,z)},$$slots:{default:!0}}),_a(p(Ae,2),{get userGuidelines(){return u()},get userGuidelinesLengthLimit(){return T()},updateUserGuideline:pe()});var ae=p(te,2);(function(H,J){Ye(J,!1);const z=xr();let U=w(J,"show",8,!1),Q=w(J,"errorMessage",8,""),le=G(""),ye=G(void 0),W=G(!1);function V(){n(le).trim()&&!n(W)&&(v(W,!0),z("create",n(le).trim()))}function se(){n(W)||(z("cancel"),v(le,""))}function oe(ie){n(W)||(ie.key==="Enter"?(ie.preventDefault(),V()):ie.key==="Escape"&&(ie.preventDefault(),se()))}$e(()=>(y(U()),n(ye)),()=>{U()&&n(ye)&&setTimeout(()=>{var ie;return(ie=n(ye))==null?void 0:ie.focus()},100)}),$e(()=>(y(U()),y(Q())),()=>{U()&&!Q()||v(W,!1)}),$e(()=>(y(U()),y(Q())),()=>{U()||Q()||v(le,"")}),lt(),Xe(),la(H,{get show(){return U()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return n(W)},get preventEscapeClose(){return n(W)},$$events:{cancel:se,keydown:function(ie){n(W)||ie.detail.key==="Enter"&&(ie.detail.preventDefault(),V())}},$$slots:{body:(ie,Ne)=>{var Ee=jl(),Re=Se(Ee);ue(Re,{size:2,color:"secondary",children:(Be,dt)=>{var At=L("Enter a name for the new rule file (e.g., architecture.md):");d(Be,At)},$$slots:{default:!0}});var qe=p(Re,2);Xt(qe,{placeholder:"rule-name.md",get disabled(){return n(W)},get value(){return n(le)},set value(Be){v(le,Be)},get textInput(){return n(ye)},set textInput(Be){v(ye,Be)},$$events:{keydown:oe},$$legacy:!0});var He=p(qe,2),vt=Be=>{ks(Be,{variant:"soft",color:"error",size:1,children:(dt,At)=>{var et=L();Ce(()=>ze(et,Q())),d(dt,et)},$$slots:{default:!0,icon:(dt,At)=>{Js(dt,{slot:"icon"})}}})};Y(He,Be=>{Q()&&Be(vt)}),d(ie,Ee)},footer:(ie,Ne)=>{var Ee=Dl(),Re=h(Ee);Ke(Re,{variant:"solid",color:"neutral",get disabled(){return n(W)},$$events:{click:se},children:(vt,Be)=>{var dt=L("Cancel");d(vt,dt)},$$slots:{default:!0}});var qe=p(Re,2);const He=xe(()=>(n(le),n(W),g(()=>!n(le).trim()||n(W))));Ke(qe,{variant:"solid",color:"accent",get disabled(){return n(He)},get loading(){return n(W)},$$events:{click:V},children:(vt,Be)=>{var dt=L();Ce(()=>ze(dt,n(W)?"Creating...":"Create")),d(vt,dt)},$$slots:{default:!0}}),d(ie,Ee)}}}),Qe()})(ae,{get show(){return st(B,"$showCreateRuleDialog",t)},get errorMessage(){return st(P,"$createRuleError",t)},$$events:{create:function(H){S.handleCreateRuleWithName(H.detail)},cancel:function(){S.hideCreateRuleDialog()}}}),Kl(p(ae,2),{get show(){return n(C)},get options(){return n(O)},get isLoading(){return n(Z)},get errorMessage(){return n(x)},get successMessage(){return n(k)},$$events:{select:async function(H){const J=H.detail;try{v(Z,!0),v(x,"");const z=await S.processAutoImportSelection(J);let U=`Successfully imported ${z.importedRulesCount} rule${z.importedRulesCount!==1?"s":""} from ${J.label}`;z.duplicatesCount>0&&(U+=`, ${z.duplicatesCount} duplicate${z.duplicatesCount!==1?"s":""} skipped`),z.totalAttempted>z.importedRulesCount+z.duplicatesCount&&(U+=`, ${z.totalAttempted-z.importedRulesCount-z.duplicatesCount} failed`),v(k,U),setTimeout(()=>{v(C,!1),v(k,"")},500)}catch(z){console.error("Failed to process auto-import selection:",z),v(x,"Failed to import rules. Please try again.")}finally{v(Z,!1)}},cancel:function(){v(C,!1),v(x,""),v(k,"")}}}),d(r,M),Qe(),s()}var od=bt("<svg><!></svg>");function ld(r,e){Ye(e,!1);let t=w(e,"onSignOut",8),s=G(!1);Xe(),Ke(r,{get loading(){return n(s)},variant:"soft","data-testid":"sign-out-button",$$events:{click:function(){t()(),v(s,!0)}},children:(a,l)=>{var i=L("Sign Out");d(a,i)},$$slots:{default:!0,iconLeft:(a,l)=>{(function(i,o){const c=us(o,["children","$$slots","$$events","$$legacy"]);var u=od();js(u,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...c}));var T=h(u);nr(T,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),d(i,u)})(a,{slot:"iconLeft"})}}}),Qe()}var dd=m('<span slot="content"><!></span>');function cd(r,e){Ye(e,!1);const[t,s]=Nt(),a=()=>st(M,"$guidelines",t),l=()=>st(O,"$settingsComponentSupported",t),i=()=>st(Z,"$enableAgentMode",t),o=()=>st(R,"$terminalSettingsStore",t),c=G(),u=G(),T=G(),ne=G(),N=G(),pe=new xs(at),ve=new Us(at),$=new Mi(at),S=new Qr(at),f=new ta,B=new sa(at,S,f),P=new _r(S),C=new Fs(S);_s(_r.key,P),_s(Fs.key,C),_s(xs.key,pe),function(j){_s(fa,j)}(B),function(j){_s(ya,j)}(ve);const O=pe.getSettingsComponentSupported(),Z=pe.getEnableAgentMode(),x=pe.getEnableAgentSwarmMode(),k=pe.getHasEverUsedRemoteAgent();S.registerConsumer(pe),S.registerConsumer(ve),S.registerConsumer($);const R=$.getTerminalSettings();let F=G();const E={handleMessageFromExtension:j=>!(!j.data||j.data.type!==Ve.navigateToSettingsSection)&&(j.data.data&&typeof j.data.data=="string"&&v(F,j.data.data),!0)};S.registerConsumer(E);const _=pe.getDisplayableTools(),M=pe.getGuidelines();function te(j){const ce=j.trim();return!(n(u)&&ce.length>n(u))&&(pe.updateLocalUserGuidelines(ce),at.postMessage({type:Ve.updateUserGuidelines,data:j}),!0)}function I(j){at.postMessage({type:Ve.toolConfigStartOAuth,data:{authUrl:j}}),pe.startPolling()}async function A(j){await B.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${j.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&at.postMessage({type:Ve.toolConfigRevokeAccess,data:{toolId:j.identifier}})}function ee(j){$.updateSelectedShell(j)}function X(j){$.updateStartupScript(j)}function de(j,ce){at.postMessage({type:Ve.toolApprovalConfigSetRequest,data:{toolId:j,approvalConfig:ce}})}function fe(){at.postMessage({type:Ve.signOut})}Yr(()=>{pe.dispose(),P.dispose(),C.dispose()}),pe.notifyLoaded(),at.postMessage({type:Ve.getOrientationStatus}),at.postMessage({type:Ve.settingsPanelLoaded}),$e(()=>a(),()=>{var j;v(c,(j=a().userGuidelines)==null?void 0:j.contents)}),$e(()=>a(),()=>{var j;v(u,(j=a().userGuidelines)==null?void 0:j.lengthLimit)}),$e(()=>a(),()=>{var j,ce;v(T,(ce=(j=a().workspaceGuidelines)==null?void 0:j[0])==null?void 0:ce.lengthLimit)}),$e(()=>a(),()=>{var j,ce;v(ne,((ce=(j=a().workspaceGuidelines)==null?void 0:j[0])==null?void 0:ce.contents)||"")}),$e(()=>(l(),Pr),()=>{v(N,[l().remoteTools?Cs("Tools","",lo,"section-tools"):void 0,l().userGuidelines&&!l().rules?Cs("User Guidelines","Guidelines for Augment Chat to follow.",Ll,"guidelines"):void 0,l().rules?Cs("Rules and User Guidelines","",Zl,"guidelines"):void 0,l().workspaceContext?Cs("Context","",uo,"context"):void 0,Cs("Account","Manage your Augment account settings.",Pr,"account")].filter(Boolean))}),$e(()=>(n(N),n(F)),()=>{var j;n(N).length>1&&!n(F)&&v(F,(j=n(N)[0])==null?void 0:j.id)}),lt(),Xe(),mt("message",ar,function(...j){var ce;(ce=S.onMessageFromExtension)==null||ce.apply(this,j)}),un.Root(r,{children:(j,ce)=>{io(j,{get items(){return n(N)},mode:"tree",class:"c-settings-navigation",get selectedId(){return n(F)},$$slots:{content:(me,q)=>{var we=dd();const he=xe(()=>q.item);var je=h(we),ge=Me=>{},Le=(Me,Ae)=>{var De=ae=>{qi(ae,{})},ke=(ae,H)=>{var J=U=>{var Q=nt(),le=Se(Q),ye=V=>{id(V,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},get workspaceGuidelinesLengthLimit(){return n(T)},get workspaceGuidelinesContent(){return n(ne)},updateUserGuideline:te})},W=V=>{_a(V,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:te})};Y(le,V=>{l(),g(()=>l().rules)?V(ye):V(W,!1)}),d(U,Q)},z=(U,Q)=>{var le=W=>{ld(W,{onSignOut:fe})},ye=W=>{const V=xe(()=>(i(),l(),g(()=>i()&&l().mcpServerList))),se=xe(()=>(i(),l(),g(()=>i()&&l().mcpServerImport)));Il(W,{get tools(){return st(_,"$displayableTools",t)},onAuthenticate:I,onRevokeAccess:A,onToolApprovalConfigChange:de,onMCPServerAdd:oe=>ve.addServer(oe),onMCPServerSave:oe=>ve.updateServer(oe),onMCPServerDelete:oe=>ve.deleteServer(oe),onMCPServerToggleDisable:oe=>ve.toggleDisabledServer(oe),onMCPServerJSONImport:oe=>ve.importServersFromJSON(oe),get isMCPEnabled(){return n(V)},get isMCPImportEnabled(){return n(se)},get supportedShells(){return o(),g(()=>o().supportedShells)},get selectedShell(){return o(),g(()=>o().selectedShell)},get startupScript(){return o(),g(()=>o().startupScript)},onShellSelect:ee,onStartupScriptChange:X,get isTerminalEnabled(){return l(),g(()=>l().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return i()},get isSwarmModeFeatureFlagEnabled(){return st(x,"$enableAgentSwarmMode",t)},get hasEverUsedRemoteAgent(){return st(k,"$hasEverUsedRemoteAgent",t)}})};Y(U,W=>{y(n(he)),g(()=>{var V;return((V=n(he))==null?void 0:V.id)==="account"})?W(le):W(ye,!1)},Q)};Y(ae,U=>{y(n(he)),g(()=>{var Q;return((Q=n(he))==null?void 0:Q.id)==="guidelines"})?U(J):U(z,!1)},H)};Y(Me,ae=>{y(n(he)),g(()=>{var H;return((H=n(he))==null?void 0:H.id)==="context"})?ae(De):ae(ke,!1)},Ae)};Y(je,Me=>{y(kr),y(n(he)),g(()=>!kr(n(he)))?Me(ge):Me(Le,!1)}),d(me,we)}}})},$$slots:{default:!0}}),Qe(),s()}(async function(){at&&at.initialize&&await at.initialize(),Aa(cd,{target:document.getElementById("app")})})();
