var cn=Object.defineProperty;var un=(t,e,n)=>e in t?cn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var ze=(t,e,n)=>un(t,typeof e!="symbol"?e+"":e,n);import{A as At,C as Y,D as Rt,J as C,M as S,t as h,Q as ht,W as gt,b as N,H as X,_ as r,a3 as U,L as x,K as P,I as Et,a6 as Je,m as D,Y as _,X as J,$ as me,am as Ae,F as Ze,G as Dt,N as ln,T as dn,V as xe,ab as ke,aE as ft,P as Pt,al as Ke,aF as Re,az as mn}from"./SpinnerAugment-AffdR7--.js";import"./design-system-init-BpU1o6o4.js";import{e as Ot,i as re,c as Ft,W as vt,B as hn,I as Ee}from"./IconButtonAugment-DVt24OaC.js";import{C as gn}from"./CopyButton-Cx19Z4lO.js";import{B as Ne}from"./ButtonAugment-K-zrKZyw.js";import{t as yt,d as fn,m as Ve,a as Oe,b as Fe}from"./differenceInCalendarDays-CwIWhZCr.js";import{c as vn,S as he,M as pn}from"./index-ZbZe59K6.js";import{e as bn,R as Kt}from"./toggleHighContrast-Cb9MCs64.js";import{C as ve}from"./next-edit-types-904A5ehg.js";import{T as wn}from"./TextAreaAugment-BXNDUf24.js";import{C as yn}from"./CardAugment-CB88N7dm.js";import{o as je}from"./keypress-DD1aQVr0.js";import{M as Ie}from"./MaterialIcon-DgNf9Yde.js";import"./index-C5DcjNTh.js";import"./preload-helper-Dv6uf1Os.js";import"./BaseTextInput-IcL3sG2L.js";function jt(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}let xn={};function fe(){return xn}function ae(t,e){var c,o,d,u;const n=fe(),i=(e==null?void 0:e.weekStartsOn)??((o=(c=e==null?void 0:e.locale)==null?void 0:c.options)==null?void 0:o.weekStartsOn)??n.weekStartsOn??((u=(d=n.locale)==null?void 0:d.options)==null?void 0:u.weekStartsOn)??0,a=yt(t),s=a.getDay(),k=(s<i?7:0)+s-i;return a.setDate(a.getDate()-k),a.setHours(0,0,0,0),a}function ge(t){return ae(t,{weekStartsOn:1})}function tn(t){const e=yt(t),n=e.getFullYear(),i=jt(t,0);i.setFullYear(n+1,0,4),i.setHours(0,0,0,0);const a=ge(i),s=jt(t,0);s.setFullYear(n,0,4),s.setHours(0,0,0,0);const k=ge(s);return e.getTime()>=a.getTime()?n+1:e.getTime()>=k.getTime()?n:n-1}function kn(t){if(e=t,!(e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"||typeof t=="number"))return!1;var e;const n=yt(t);return!isNaN(Number(n))}const Nn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function pe(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const _n={date:pe({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:pe({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:pe({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Mn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Vt(t){return(e,n)=>{let i;if((n!=null&&n.context?String(n.context):"standalone")==="formatting"&&t.formattingValues){const a=t.defaultFormattingWidth||t.defaultWidth,s=n!=null&&n.width?String(n.width):a;i=t.formattingValues[s]||t.formattingValues[a]}else{const a=t.defaultWidth,s=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[s]||t.values[a]}return i[t.argumentCallback?t.argumentCallback(e):e]}}const Tn={ordinalNumber:(t,e)=>{const n=Number(t),i=n%100;if(i>20||i<10)switch(i%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:Vt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Vt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:Vt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Vt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Vt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function te(t){return(e,n={})=>{const i=n.width,a=i&&t.matchPatterns[i]||t.matchPatterns[t.defaultMatchWidth],s=e.match(a);if(!s)return null;const k=s[0],c=i&&t.parsePatterns[i]||t.parsePatterns[t.defaultParseWidth],o=Array.isArray(c)?function(u,p){for(let y=0;y<u.length;y++)if(p(u[y]))return y}(c,u=>u.test(k)):function(u,p){for(const y in u)if(Object.prototype.hasOwnProperty.call(u,y)&&p(u[y]))return y}(c,u=>u.test(k));let d;return d=t.valueCallback?t.valueCallback(o):o,d=n.valueCallback?n.valueCallback(d):d,{value:d,rest:e.slice(k.length)}}}const Pn={ordinalNumber:(ee={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)},(t,e={})=>{const n=t.match(ee.matchPattern);if(!n)return null;const i=n[0],a=t.match(ee.parsePattern);if(!a)return null;let s=ee.valueCallback?ee.valueCallback(a[0]):a[0];return s=e.valueCallback?e.valueCallback(s):s,{value:s,rest:t.slice(i.length)}}),era:te({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:te({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:te({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:te({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:te({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var ee;const Dn={code:"en-US",formatDistance:(t,e,n)=>{let i;const a=Nn[t];return i=typeof a=="string"?a:e===1?a.one:a.other.replace("{{count}}",e.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+i:i+" ago":i},formatLong:_n,formatRelative:(t,e,n,i)=>Mn[t],localize:Tn,match:Pn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Cn(t){const e=yt(t);return fn(e,function(i){const a=yt(i),s=jt(i,0);return s.setFullYear(a.getFullYear(),0,1),s.setHours(0,0,0,0),s}(e))+1}function Sn(t){const e=yt(t),n=+ge(e)-+function(i){const a=tn(i),s=jt(i,0);return s.setFullYear(a,0,4),s.setHours(0,0,0,0),ge(s)}(e);return Math.round(n/Ve)+1}function en(t,e){var u,p,y,w;const n=yt(t),i=n.getFullYear(),a=fe(),s=(e==null?void 0:e.firstWeekContainsDate)??((p=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??a.firstWeekContainsDate??((w=(y=a.locale)==null?void 0:y.options)==null?void 0:w.firstWeekContainsDate)??1,k=jt(t,0);k.setFullYear(i+1,0,s),k.setHours(0,0,0,0);const c=ae(k,e),o=jt(t,0);o.setFullYear(i,0,s),o.setHours(0,0,0,0);const d=ae(o,e);return n.getTime()>=c.getTime()?i+1:n.getTime()>=d.getTime()?i:i-1}function $n(t,e){const n=yt(t),i=+ae(n,e)-+function(a,s){var u,p,y,w;const k=fe(),c=(s==null?void 0:s.firstWeekContainsDate)??((p=(u=s==null?void 0:s.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??k.firstWeekContainsDate??((w=(y=k.locale)==null?void 0:y.options)==null?void 0:w.firstWeekContainsDate)??1,o=en(a,s),d=jt(a,0);return d.setFullYear(o,0,c),d.setHours(0,0,0,0),ae(d,s)}(n,e);return Math.round(i/Ve)+1}function $(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const Tt={y(t,e){const n=t.getFullYear(),i=n>0?n:1-n;return $(e==="yy"?i%100:i,e.length)},M(t,e){const n=t.getMonth();return e==="M"?String(n+1):$(n+1,2)},d:(t,e)=>$(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return n==="am"?"a.m.":"p.m."}},h:(t,e)=>$(t.getHours()%12||12,e.length),H:(t,e)=>$(t.getHours(),e.length),m:(t,e)=>$(t.getMinutes(),e.length),s:(t,e)=>$(t.getSeconds(),e.length),S(t,e){const n=e.length,i=t.getMilliseconds();return $(Math.trunc(i*Math.pow(10,n-3)),e.length)}},qn="midnight",Wn="noon",zn="morning",An="afternoon",Rn="evening",En="night",Ye={G:function(t,e,n){const i=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(i,{width:"abbreviated"});case"GGGGG":return n.era(i,{width:"narrow"});default:return n.era(i,{width:"wide"})}},y:function(t,e,n){if(e==="yo"){const i=t.getFullYear(),a=i>0?i:1-i;return n.ordinalNumber(a,{unit:"year"})}return Tt.y(t,e)},Y:function(t,e,n,i){const a=en(t,i),s=a>0?a:1-a;return e==="YY"?$(s%100,2):e==="Yo"?n.ordinalNumber(s,{unit:"year"}):$(s,e.length)},R:function(t,e){return $(tn(t),e.length)},u:function(t,e){return $(t.getFullYear(),e.length)},Q:function(t,e,n){const i=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(i);case"QQ":return $(i,2);case"Qo":return n.ordinalNumber(i,{unit:"quarter"});case"QQQ":return n.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(i,{width:"narrow",context:"formatting"});default:return n.quarter(i,{width:"wide",context:"formatting"})}},q:function(t,e,n){const i=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(i);case"qq":return $(i,2);case"qo":return n.ordinalNumber(i,{unit:"quarter"});case"qqq":return n.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(i,{width:"narrow",context:"standalone"});default:return n.quarter(i,{width:"wide",context:"standalone"})}},M:function(t,e,n){const i=t.getMonth();switch(e){case"M":case"MM":return Tt.M(t,e);case"Mo":return n.ordinalNumber(i+1,{unit:"month"});case"MMM":return n.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(i,{width:"narrow",context:"formatting"});default:return n.month(i,{width:"wide",context:"formatting"})}},L:function(t,e,n){const i=t.getMonth();switch(e){case"L":return String(i+1);case"LL":return $(i+1,2);case"Lo":return n.ordinalNumber(i+1,{unit:"month"});case"LLL":return n.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(i,{width:"narrow",context:"standalone"});default:return n.month(i,{width:"wide",context:"standalone"})}},w:function(t,e,n,i){const a=$n(t,i);return e==="wo"?n.ordinalNumber(a,{unit:"week"}):$(a,e.length)},I:function(t,e,n){const i=Sn(t);return e==="Io"?n.ordinalNumber(i,{unit:"week"}):$(i,e.length)},d:function(t,e,n){return e==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):Tt.d(t,e)},D:function(t,e,n){const i=Cn(t);return e==="Do"?n.ordinalNumber(i,{unit:"dayOfYear"}):$(i,e.length)},E:function(t,e,n){const i=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},e:function(t,e,n,i){const a=t.getDay(),s=(a-i.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return $(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,i){const a=t.getDay(),s=(a-i.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return $(s,e.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){const i=t.getDay(),a=i===0?7:i;switch(e){case"i":return String(a);case"ii":return $(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},a:function(t,e,n){const i=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(t,e,n){const i=t.getHours();let a;switch(a=i===12?Wn:i===0?qn:i/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,n){const i=t.getHours();let a;switch(a=i>=17?Rn:i>=12?An:i>=4?zn:En,e){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,n){if(e==="ho"){let i=t.getHours()%12;return i===0&&(i=12),n.ordinalNumber(i,{unit:"hour"})}return Tt.h(t,e)},H:function(t,e,n){return e==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):Tt.H(t,e)},K:function(t,e,n){const i=t.getHours()%12;return e==="Ko"?n.ordinalNumber(i,{unit:"hour"}):$(i,e.length)},k:function(t,e,n){let i=t.getHours();return i===0&&(i=24),e==="ko"?n.ordinalNumber(i,{unit:"hour"}):$(i,e.length)},m:function(t,e,n){return e==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):Tt.m(t,e)},s:function(t,e,n){return e==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):Tt.s(t,e)},S:function(t,e){return Tt.S(t,e)},X:function(t,e,n){const i=t.getTimezoneOffset();if(i===0)return"Z";switch(e){case"X":return Le(i);case"XXXX":case"XX":return zt(i);default:return zt(i,":")}},x:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"x":return Le(i);case"xxxx":case"xx":return zt(i);default:return zt(i,":")}},O:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+He(i,":");default:return"GMT"+zt(i,":")}},z:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+He(i,":");default:return"GMT"+zt(i,":")}},t:function(t,e,n){return $(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return $(t.getTime(),e.length)}};function He(t,e=""){const n=t>0?"-":"+",i=Math.abs(t),a=Math.trunc(i/60),s=i%60;return s===0?n+String(a):n+String(a)+e+$(s,2)}function Le(t,e){return t%60==0?(t>0?"-":"+")+$(Math.abs(t)/60,2):zt(t,e)}function zt(t,e=""){const n=t>0?"-":"+",i=Math.abs(t);return n+$(Math.trunc(i/60),2)+e+$(i%60,2)}const Be=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},Ge=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},On={p:Ge,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],i=n[1],a=n[2];if(!a)return Be(t,e);let s;switch(i){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;default:s=e.dateTime({width:"full"})}return s.replace("{{date}}",Be(i,e)).replace("{{time}}",Ge(a,e))}},Fn=/^D+$/,jn=/^Y+$/,In=["D","DD","YY","YYYY"],Yn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Hn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ln=/^'([^]*?)'?$/,Bn=/''/g,Gn=/[a-zA-Z]/;function Qe(t,e,n){var u,p,y,w,v,R,T,l;const i=fe(),a=(n==null?void 0:n.locale)??i.locale??Dn,s=(n==null?void 0:n.firstWeekContainsDate)??((p=(u=n==null?void 0:n.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??i.firstWeekContainsDate??((w=(y=i.locale)==null?void 0:y.options)==null?void 0:w.firstWeekContainsDate)??1,k=(n==null?void 0:n.weekStartsOn)??((R=(v=n==null?void 0:n.locale)==null?void 0:v.options)==null?void 0:R.weekStartsOn)??i.weekStartsOn??((l=(T=i.locale)==null?void 0:T.options)==null?void 0:l.weekStartsOn)??0,c=yt(t);if(!kn(c))throw new RangeError("Invalid time value");let o=e.match(Hn).map(g=>{const b=g[0];return b==="p"||b==="P"?(0,On[b])(g,a.formatLong):g}).join("").match(Yn).map(g=>{if(g==="''")return{isToken:!1,value:"'"};const b=g[0];if(b==="'")return{isToken:!1,value:Qn(g)};if(Ye[b])return{isToken:!0,value:g};if(b.match(Gn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+b+"`");return{isToken:!1,value:g}});a.localize.preprocessor&&(o=a.localize.preprocessor(c,o));const d={firstWeekContainsDate:s,weekStartsOn:k,locale:a};return o.map(g=>{if(!g.isToken)return g.value;const b=g.value;return(!(n!=null&&n.useAdditionalWeekYearTokens)&&function(f){return jn.test(f)}(b)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&function(f){return Fn.test(f)}(b))&&function(f,E,z){const O=function(L,V,lt){const rt=L[0]==="Y"?"years":"days of the month";return`Use \`${L.toLowerCase()}\` instead of \`${L}\` (in \`${V}\`) for formatting ${rt} to the input \`${lt}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(f,E,z);if(console.warn(O),In.includes(f))throw new RangeError(O)}(b,e,String(t)),(0,Ye[b[0]])(c,b,a.localize,d)}).join("")}function Qn(t){const e=t.match(Ln);return e?e[1].replace(Bn,"'"):t}function be(t,e){const n=function(c){const o={},d=c.split(le.dateTimeDelimiter);let u;if(d.length>2)return o;if(/:/.test(d[0])?u=d[0]:(o.date=d[0],u=d[1],le.timeZoneDelimiter.test(o.date)&&(o.date=c.split(le.timeZoneDelimiter)[0],u=c.substr(o.date.length,c.length))),u){const p=le.timezone.exec(u);p?(o.time=u.replace(p[1],""),o.timezone=p[1]):o.time=u}return o}(t);let i;if(n.date){const c=function(o,d){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+d)+"})|(\\d{2}|[+-]\\d{"+(2+d)+"})$)"),p=o.match(u);if(!p)return{year:NaN,restDateString:""};const y=p[1]?parseInt(p[1]):null,w=p[2]?parseInt(p[2]):null;return{year:w===null?y:100*w,restDateString:o.slice((p[1]||p[2]).length)}}(n.date,2);i=function(o,d){if(d===null)return new Date(NaN);const u=o.match(Xn);if(!u)return new Date(NaN);const p=!!u[4],y=ne(u[1]),w=ne(u[2])-1,v=ne(u[3]),R=ne(u[4]),T=ne(u[5])-1;if(p)return function(l,g,b){return g>=1&&g<=53&&b>=0&&b<=6}(0,R,T)?function(l,g,b){const f=new Date(0);f.setUTCFullYear(l,0,4);const E=f.getUTCDay()||7,z=7*(g-1)+b+1-E;return f.setUTCDate(f.getUTCDate()+z),f}(d,R,T):new Date(NaN);{const l=new Date(0);return function(g,b,f){return b>=0&&b<=11&&f>=1&&f<=(Zn[b]||(Xe(g)?29:28))}(d,w,v)&&function(g,b){return b>=1&&b<=(Xe(g)?366:365)}(d,y)?(l.setUTCFullYear(d,w,Math.max(y,v)),l):new Date(NaN)}}(c.restDateString,c.year)}if(!i||isNaN(i.getTime()))return new Date(NaN);const a=i.getTime();let s,k=0;if(n.time&&(k=function(c){const o=c.match(Un);if(!o)return NaN;const d=we(o[1]),u=we(o[2]),p=we(o[3]);return function(y,w,v){return y===24?w===0&&v===0:v>=0&&v<60&&w>=0&&w<60&&y>=0&&y<25}(d,u,p)?d*Oe+u*Fe+1e3*p:NaN}(n.time),isNaN(k)))return new Date(NaN);if(!n.timezone){const c=new Date(a+k),o=new Date(0);return o.setFullYear(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()),o.setHours(c.getUTCHours(),c.getUTCMinutes(),c.getUTCSeconds(),c.getUTCMilliseconds()),o}return s=function(c){if(c==="Z")return 0;const o=c.match(Jn);if(!o)return 0;const d=o[1]==="+"?-1:1,u=parseInt(o[2]),p=o[3]&&parseInt(o[3])||0;return function(y,w){return w>=0&&w<=59}(0,p)?d*(u*Oe+p*Fe):NaN}(n.timezone),isNaN(s)?new Date(NaN):new Date(a+k+s)}const le={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Xn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Un=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Jn=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ne(t){return t?parseInt(t):1}function we(t){return t&&parseFloat(t.replace(",","."))||0}const Zn=[31,null,31,30,31,30,31,31,30,31,30,31];function Xe(t){return t%400==0||t%4==0&&t%100!=0}var it=(t=>(t[t.unset=0]="unset",t[t.positive=1]="positive",t[t.negative=2]="negative",t))(it||{}),Kn=C('<span slot="text" class="c-history-header--ellipsis svelte-8btr94"> </span>'),Vn=C('<span class="c-history-header--ellipsis-left svelte-8btr94"> </span>'),ti=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">File:</span> <!></div>'),ei=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Instruction:</span> <span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ni=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ii=C('<div class="c-history-header svelte-8btr94"><div class="c-history-header__timestamp svelte-8btr94"> </div> <div class="c-history-header__metadata svelte-8btr94"><div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Request ID:</span> <!></div> <!> <!> <!></div></div>');function nn(t,e){At(e,!1);let n=Y(e,"occuredAt",8),i=Y(e,"requestID",8),a=Y(e,"pathName",8,""),s=Y(e,"repoRoot",8),k=Y(e,"prompt",8,""),c=Y(e,"others",24,()=>[]);function o(){Ft.postMessage({type:vt.openFile,data:{repoRoot:s(),pathName:a()}})}Rt();var d=ii(),u=h(d),p=h(u),y=S(u,2),w=h(y),v=S(h(w),2);gn(v,{get text(){return i()},variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:(f,E)=>{var z=Kn(),O=h(z);ht(()=>gt(O,i())),N(f,z)}}});var R=S(w,2),T=f=>{var E=ti(),z=S(h(E),2);Ne(z,{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$events:{click:o},children:(O,L)=>{var V=Vn(),lt=h(V);ht(()=>gt(lt,`‎${a()??""}`)),N(O,V)},$$slots:{default:!0}}),N(f,E)};X(R,f=>{a()&&f(T)});var l=S(R,2),g=f=>{var E=ei(),z=S(h(E),2),O=h(z);ht(()=>gt(O,k())),N(f,E)};X(l,f=>{k()&&f(g)});var b=S(l,2);Ot(b,1,c,re,(f,E)=>{var z=ni(),O=h(z),L=h(O);ht(()=>gt(L,r(E))),N(f,z)}),ht(f=>gt(p,f),[()=>(P(Qe),P(n()),x(()=>Qe(n(),"p 'on' P")))],U),N(t,d),Et()}const _e={lineNumbers:"off",padding:{top:18,bottom:18}};var ri=C('<div class="c-completion-code-block svelte-krgqjl"><div class="c-completion-code-block__content svelte-krgqjl"><!></div></div>');const Ue=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],ie=new class{constructor(){ze(this,"_state");this._state=Ft.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(t){return this._state.feedback[t]?this._state.feedback[t]:{selectedRating:it.unset,feedbackNote:""}}setFeedback(t,e){this._state.feedback[t]=e,Ft.setState(this._state)}cleanupFeedback(t){for(const e of Object.keys(this._state.feedback))t[e]||delete this._state.feedback[e];Ft.setState(this._state)}};function de(t){return typeof t=="string"?t:t==null?void 0:t.value}var ai=C('<div><div class="background-slider svelte-axvozx"></div> <!></div>');function rn(t,e){At(e,!1);let n=Y(e,"options",8),i=Y(e,"size",8,2),a=Y(e,"disabled",8,!1),s=Y(e,"onSelectOption",8),k=Y(e,"activeOption",28,()=>de(n()[0])),c=D(),o=D();function d(){var b;const l=(b=r(c))==null?void 0:b.querySelectorAll(".c-toggle-button__button");if(!l)return;const g=l[n().findIndex(f=>de(f)===k())];if(r(c)&&r(o)&&g){const f=g.getBoundingClientRect(),E=r(c).getBoundingClientRect();ft(o,r(o).style.left=f.left-E.left+"px"),ft(o,r(o).style.width=`${f.width}px`),ft(o,r(o).style.height=`${f.height}px`)}}let u=D(),p=D(!1),y=D();Je(()=>{var l;(l=r(u))==null||l.disconnect(),_(u,void 0),clearTimeout(r(y))}),J(()=>P(k()),()=>{k()&&d()}),J(()=>(r(c),r(u),r(y)),()=>{r(c)&&!r(u)&&(_(u,new ResizeObserver(()=>{_(p,!0),d(),clearTimeout(r(y)),_(y,setTimeout(()=>{_(p,!1)},100))})),r(u).observe(r(c)))}),me(),Rt();var w=ai();let v;var R=h(w);Ae(R,l=>_(o,l),()=>r(o));var T=S(R,2);Ot(T,1,n,re,(l,g)=>{const b=U(()=>r(g)===k()?"c-toggle-button__button--active":"");hn(l,{get size(){return i()},get disabled(){return a()},variant:"ghost",color:"neutral",get class(){return`c-toggle-button__button ${r(b)??""}`},$$events:{click:()=>function(f){!a()&&s()(f)&&k(f)}(de(r(g)))},children:(f,E)=>{var z=Ze(),O=Dt(z);const L=U(()=>(r(g),x(()=>de(r(g)))));ln(O,e,"option-button-contents",{get option(){return r(L)},get size(){return i()}},V=>{const lt=U(()=>i()===.5?1:i());dn(V,{get size(){return r(lt)},children:(rt,It)=>{var Yt=xe();ht(()=>gt(Yt,(r(g),x(()=>typeof r(g)=="string"?r(g):r(g).label)))),N(rt,Yt)},$$slots:{default:!0}})}),N(f,z)},$$slots:{default:!0}})}),Ae(w,l=>_(c,l),()=>r(c)),ht(l=>v=ke(w,1,"c-toggle-button svelte-axvozx",null,v,l),[()=>({"c-toggle-button--disabled":a(),"c-toggle-button--size-0_5":i()===.5,"c-toggle-button--size-1":i()===1,"c-toggle-button--size-2":i()===2,"c-toggle-button--size-3":i()===3,"c-toggle-button--size-4":i()===4,"c-toggle-button--resizing":r(p)})],U),N(t,w),Et()}var oi=C('<div class="c-unified-history-item__tabs svelte-179mxe5"><!></div>'),si=C('<div class="c-unified-history-item__code-block svelte-179mxe5"><!></div>'),ci=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext"><code><span> </span></code></pre></div> <div> </div> <section>original: <!> modified: <!></section>',1),ui=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext" class="c-next-edit-addition svelte-179mxe5"><code><span> </span></code></pre></div>'),li=C('<section><!> <div class="c-unified-history-item__no-modifications svelte-179mxe5">Unchanged locations:</div> <!></section>'),di=C('<div class="c-unified-history-item__feedback-area svelte-179mxe5"><div class="c-unified-history-item__feedback-content svelte-179mxe5"><!></div> <div class="c-unified-history-item__feedback-actions svelte-179mxe5"><!> <!></div></div>'),mi=C('<div class="c-unified-history-item__header svelte-179mxe5"><!></div> <!> <div class="c-unified-history-item__content svelte-179mxe5"><!></div> <div class="c-unified-history-item__footer svelte-179mxe5"><div class="c-unified-history-item__ratings svelte-179mxe5"><div class="c-unified-history-item__rating-buttons svelte-179mxe5"><!> <!></div> <div class="c-unified-history-item__thankyou svelte-179mxe5"> </div></div> <!></div>',1);function ye(t,e){At(e,!1);const n=D(),i=D(),a=D(),s=D(),k=D();let c=Y(e,"completion",24,()=>{}),o=Y(e,"nextEdit",24,()=>{}),d=Y(e,"demo",8,!1),u=D(r(n)==="completion"?"Completion":"Next Edit");const p=r(n)==="completion"?["Completion"]:["Next Edit"];let y,w,v=D(ie.getFeedback(r(i))),R=D(!1),T=D(""),l=D(!1),g=null,b=D(""),f=D(),E=D([]),z=D([]);function O(){_(T,Ue[Math.floor(Math.random()*Ue.length)]),y&&clearTimeout(y),y=setTimeout(()=>{_(T,"")},4e3)}function L(M){ft(v,r(v).selectedRating=M),g=M,_(b,""),_(l,!0)}function V(){_(l,!1),g=null,_(b,""),ft(v,r(v).selectedRating=it.unset)}function lt(){g&&r(b).trim().length!==0&&(function(M,q){if(O(),w=r(v).selectedRating,M!==it.unset&&ft(v,r(v).selectedRating=M),d())return;let F=q||r(v).feedbackNote;ie.setFeedback(r(i),r(v)),_(R,!0);const ct=r(n)==="completion"?vt.completionRating:vt.nextEditRating;Ft.postMessage({type:ct,data:{requestId:r(i),rating:M,note:F.trim()}})}(g,r(b).trim()),V())}function rt(M){Ft.postMessage({type:vt.openFile,data:{repoRoot:M.qualifiedPathName.rootPath,pathName:M.result.path,range:M.lineRange,differentTab:!0}}),_(f,M)}function It(M){return _(u,M),!0}J(()=>P(c()),()=>{_(n,c()?"completion":"nextEdit")}),J(()=>(P(c()),P(o())),()=>{var M,q;_(i,((M=c())==null?void 0:M.requestId)||((q=o())==null?void 0:q.requestId)||"")}),J(()=>(P(c()),P(o())),()=>{var M,q;_(a,((M=c())==null?void 0:M.occuredAt)||((q=o())==null?void 0:q.occurredAt)||new Date)}),J(()=>P(c()),()=>{var M;_(s,((M=c())==null?void 0:M.pathName)||"")}),J(()=>(P(c()),P(o())),()=>{var M,q,F;_(k,((M=c())==null?void 0:M.repoRoot)||((F=(q=o())==null?void 0:q.qualifiedPathName)==null?void 0:F.rootPath)||"")}),J(()=>(P(o()),ve),()=>{o()&&(_(E,o().suggestions.filter(M=>M.changeType!==ve.noop)),_(z,o().suggestions.filter(M=>M.changeType===ve.noop)))}),me(),Rt(),Pt("message",Ke,function(M){if(d())return;const q=M.data;switch(q.type){case vt.completionRatingDone:{const{requestId:F}=q.data;if(F!==r(i))return;_(R,!1),q.data.success||(ft(v,r(v).selectedRating=w),ie.setFeedback(r(i),r(v)));break}case vt.nextEditRatingDone:{const{requestId:F}=q.data;if(F!==r(i))return;_(R,!1),q.data.success||(ft(v,r(v).selectedRating=w),ie.setFeedback(r(i),r(v)));break}}});const Yt=U(()=>"c-unified-history-item "+(r(R)?"c-unified-history-item--sending-feedback":""));yn(t,{size:2,variant:"surface",get class(){return r(Yt)},children:(M,q)=>{var F=mi(),ct=Dt(F),at=h(ct);const pt=U(()=>(P(o()),x(()=>o()?[`Request type: ${o().mode}/${o().scope}`]:void 0)));nn(at,{get occuredAt(){return r(a)},get requestID(){return r(i)},get pathName(){return r(s)},get repoRoot(){return r(k)},get others(){return r(pt)}});var xt=S(ct,2),Ct=j=>{var tt=oi();rn(h(tt),{get options(){return p},onSelectOption:It,get activeOption(){return r(u)},size:1}),N(j,tt)};X(xt,j=>{x(()=>p.length>1)&&j(Ct)});var St=S(xt,2),Lt=h(St),Bt=j=>{var tt=Ze(),ut=Dt(tt);Ot(ut,1,()=>(P(c()),x(()=>c().completions)),re,(B,et)=>{var Z=si();(function($t,H){At(H,!1);let m=Y(H,"prefix",8),G=Y(H,"suffix",8),I=Y(H,"completion",8);const K=function(Mt){const mt=Mt.split(`
`).slice(-6);for(let wt=0;wt<mt.length;wt++)if(mt[wt].trim().length>0)return mt.slice(wt).join(`
`);return""}(m()),Nt=(nt=G(),!!(ot=I().skippedSuffix)&&nt.indexOf(ot)===0);var nt,ot;const _t=Nt?function(Mt,mt){return mt?Mt.indexOf(mt)!==0?Mt:Mt.slice(mt.length):Mt}(G(),I().skippedSuffix):G(),qt=function(Mt){const mt=Mt.split(`
`).slice(0,6);for(let wt=mt.length-1;wt>=0;wt--)if(mt[wt].trim().length>0)return mt.slice(0,wt+1).join(`
`);return""}(_t),st=I().text,Wt=Nt?I().skippedSuffix:"",Ut=I().suffixReplacementText,Jt=K+st+Wt+Ut+qt,Q=bn.createModel(Jt,"plaintext"),Ht=Q.getPositionAt(0),Zt=Q.getPositionAt(K.length),Me=Q.getPositionAt(K.length),Te=Q.getPositionAt(K.length+st.length),Pe=Q.getPositionAt(K.length+st.length),De=Q.getPositionAt(K.length+st.length+Wt.length),Ce=Q.getPositionAt(K.length+st.length+Wt.length),Se=Q.getPositionAt(K.length+st.length+Wt.length+Ut.length),$e=Q.getPositionAt(K.length+st.length+Wt.length+Ut.length),qe=Q.getPositionAt(Jt.length),an=[{range:new Kt(Ht.lineNumber,Ht.column,Zt.lineNumber,Zt.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Kt($e.lineNumber,$e.column,qe.lineNumber,qe.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Kt(Me.lineNumber,Me.column,Te.lineNumber,Te.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Kt(Ce.lineNumber,Ce.column,Se.lineNumber,Se.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Kt(Pe.lineNumber,Pe.column,De.lineNumber,De.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];Je(()=>{Q==null||Q.dispose()}),Rt();var We=ri(),on=h(We),sn=h(on);vn(sn,{get options(){return _e},get model(){return Q},get decorations(){return an}}),N($t,We),Et()})(h(Z),{get completion(){return r(et)},get prefix(){return P(c()),x(()=>c().prefix)},get suffix(){return P(c()),x(()=>c().suffix)}}),N(B,Z)}),N(j,tt)},Gt=(j,tt)=>{var ut=B=>{var et=li(),Z=h(et);Ot(Z,1,()=>r(E),re,(H,m)=>{var G=ci(),I=Dt(G),K=Re(()=>je("Enter",()=>rt(r(m)))),Nt=h(I),nt=h(Nt),ot=h(nt);let _t;var qt=h(ot),st=S(I,2),Wt=h(st),Ut=S(st,2),Jt=S(h(Ut));he(Jt,{get text(){return r(m),x(()=>r(m).result.existingCode)},get pathName(){return r(m),x(()=>r(m).qualifiedPathName.relPath)},options:{lineNumbers:"off"}});var Q=S(Jt,2);he(Q,{get text(){return r(m),x(()=>r(m).result.suggestedCode)},get pathName(){return r(m),x(()=>r(m).qualifiedPathName.relPath)},options:{lineNumbers:"off"}}),ht(Ht=>{_t=ke(ot,1,"c-next-edit-addition svelte-179mxe5",null,_t,Ht),gt(qt,`${r(m),x(()=>r(m).qualifiedPathName.relPath)??""}: ${r(m),x(()=>r(m).lineRange.start+(r(m).lineRange.start<r(m).lineRange.stop?1:0))??""}-${r(m),x(()=>r(m).lineRange.stop)??""}`),gt(Wt,(r(m),x(()=>r(m).result.changeDescription)))},[()=>({"c-next-edit-addition-clicked":r(f)===r(m)})],U),Pt("click",I,()=>rt(r(m))),Pt("keydown",I,function(...Ht){var Zt;(Zt=r(K))==null||Zt.apply(this,Ht)}),N(H,G)});var $t=S(Z,4);Ot($t,1,()=>r(z),re,(H,m)=>{var G=ui(),I=Re(()=>je("Enter",()=>rt(r(m)))),K=h(G),Nt=h(K),nt=h(Nt);let ot;var _t=h(nt);ht(qt=>{ot=ke(nt,1,"c-next-edit-addition svelte-179mxe5",null,ot,qt),gt(_t,`${r(m),x(()=>r(m).qualifiedPathName.relPath)??""}: ${r(m),x(()=>r(m).lineRange.start+(r(m).lineRange.start<r(m).lineRange.stop?1:0))??""}-${r(m),x(()=>r(m).lineRange.stop)??""}`)},[()=>({"c-next-edit-addition-clicked":r(f)===r(m)})],U),Pt("click",G,()=>rt(r(m))),Pt("keydown",G,function(...qt){var st;(st=r(I))==null||st.apply(this,qt)}),N(H,G)}),N(B,et)};X(j,B=>{r(n)==="nextEdit"&&o()&&B(ut)},tt)};X(Lt,j=>{r(n)==="completion"&&c()?j(Bt):j(Gt,!1)});var Qt=S(St,2),A=h(Qt),dt=h(A),kt=h(dt);const bt=U(()=>(r(v),P(it),x(()=>r(v).selectedRating===it.negative?"error":"neutral")));Ee(kt,{variant:"ghost",get color(){return r(bt)},size:2,get disabled(){return r(R)},title:"Leave feedback about this completion",$$events:{click:()=>L(it.negative)},children:(j,tt)=>{const ut=U(()=>(r(v),P(it),x(()=>r(v).selectedRating===it.negative)));Ie(j,{iconName:"thumb_down",get fill(){return r(ut)}})},$$slots:{default:!0}});var W=S(kt,2);const Xt=U(()=>(r(v),P(it),x(()=>r(v).selectedRating===it.positive?"success":"neutral")));Ee(W,{variant:"ghost",get color(){return r(Xt)},size:2,get disabled(){return r(R)},title:"Leave feedback about this completion",$$events:{click:()=>L(it.positive)},children:(j,tt)=>{const ut=U(()=>(r(v),P(it),x(()=>r(v).selectedRating===it.positive)));Ie(j,{iconName:"thumb_up",get fill(){return r(ut)}})},$$slots:{default:!0}});var oe=S(dt,2),se=h(oe),ce=S(A,2),ue=j=>{var tt=di(),ut=h(tt),B=h(ut);wn(B,{rows:"4",placeholder:"Enter your feedback...",resize:"none",get value(){return r(b)},set value(m){_(b,m)},$$legacy:!0});var et=S(ut,2),Z=h(et);Ne(Z,{variant:"ghost",size:2,$$events:{click:V},children:(m,G)=>{var I=xe("Cancel");N(m,I)},$$slots:{default:!0}});var $t=S(Z,2);const H=U(()=>(r(b),x(()=>r(b).trim().length===0)));Ne($t,{variant:"solid",size:2,get disabled(){return r(H)},$$events:{click:lt},children:(m,G)=>{var I=xe("Share Feedback");N(m,I)},$$slots:{default:!0}}),N(j,tt)};X(ce,j=>{r(l)&&j(ue)}),ht(()=>gt(se,r(T))),N(M,F)},$$slots:{default:!0}}),Et()}var hi=C(`<div class="l-no-items svelte-10bvc8"><div class="l-no-items__msg svelte-10bvc8"><h2>History.</h2> <p>As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we'll display for each suggestion.</p></div> <div class="l-no-items__divider svelte-10bvc8"></div> <div class="l-no-items__example svelte-10bvc8"><!></div></div>`),gi=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),fi=C("modified: <!>",1),vi=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),pi=C("<section>original: <!> <!></section>"),bi=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn" role="button" tabindex="0">Click to view diff</div>'),wi=C('<div class="c-instruction-item svelte-15p7ohn"><!> <!></div>'),yi=C('<div class="l-items-list__empty svelte-5e6wj2"><!></div>'),xi=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),ki=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Ni=C('<div class="l-items-list__instructions-section svelte-5e6wj2"><h3 class="l-items-list__section-title svelte-5e6wj2">Instructions</h3> <div class="l-items-list__content svelte-5e6wj2"></div></div>'),_i=C('<div class="l-items-list__empty-panel svelte-5e6wj2"><p> </p></div>'),Mi=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Ti=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Pi=C('<div class="l-items-list__content svelte-5e6wj2"></div>'),Di=C('<!> <!> <div class="l-items-list__panel-content"><!></div>',1),Ci=C('<main class="l-items-list svelte-5e6wj2"><!></main>');mn(function(t,e){At(e,!1);const n=D(),i=D(),a=D(),s=D(),k=D();let c=D({}),o=D({}),d=D({});function u(T){for(const l of T)r(c)[l.requestId]||ft(c,r(c)[l.requestId]={...l,occuredAt:be(l.occuredAt)});ie.cleanupFeedback(r(c))}function p(T){for(const l of T)if(!r(o)[l.requestId]){if(typeof l.occuredAt=="string"){const g=l.occuredAt;l.occuredAt=be(g)}ft(o,r(o)[l.requestId]={...l})}}function y(T){for(const l of T)l.suggestions.length!==0&&ft(d,r(d)[l.requestId]={requestId:l.requestId,occuredAt:be(l.occurredAt),result:l})}Ft.postMessage({type:vt.historyLoaded});let w=D([]),v=D("Completions");function R(T){return _(v,T),!0}J(()=>(r(o),r(c),r(d)),()=>{_(w,[...Object.values(r(o)),...Object.values(r(c)),...Object.values(r(d))].sort((T,l)=>l.occuredAt.getTime()-T.occuredAt.getTime()))}),J(()=>r(w),()=>{_(n,r(w).filter(T=>"completions"in T))}),J(()=>r(w),()=>{_(i,r(w).filter(T=>"result"in T))}),J(()=>r(w),()=>{_(a,r(w).filter(T=>"prompt"in T))}),J(()=>(r(n),r(i)),()=>{_(s,[{value:"Completions",label:`Completions ${r(n).length}`},{value:"Next Edits",label:`Next Edits ${r(i).length}`}])}),J(()=>(r(v),r(n),r(i)),()=>{_(k,r(v)==="Completions"?r(n):r(i))}),me(),Rt(),Pt("message",Ke,function(T){const l=T.data;switch(l.type){case vt.historyInitialize:p(l.data.instructions),u(l.data.completionRequests),y(l.data.nextEdits);break;case vt.completions:u(l.data);break;case vt.instructions:p(l.data);break;case vt.nextEditSuggestions:y([l.data])}}),pn.Root(t,{children:(T,l)=>{var g=Ci(),b=h(g),f=z=>{var O=yi();(function(L,V){At(V,!1);const lt={occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`};Rt();var rt=hi(),It=S(h(rt),4);ye(h(It),{get completion(){return lt},demo:!0}),N(L,rt),Et()})(h(O),{}),N(z,O)},E=z=>{var O=Di(),L=Dt(O);rn(L,{get options(){return r(s)},onSelectOption:R,get activeOption(){return r(v)},size:2});var V=S(L,2),lt=q=>{var F=Ni(),ct=S(h(F),2);Ot(ct,7,()=>r(a),at=>at.requestId,(at,pt,xt)=>{var Ct=ki(),St=Dt(Ct),Lt=h(St);const Bt=U(()=>(r(pt),x(()=>function(A){if(!("prompt"in A))throw new Error("wrong type");if("completions"in A)throw new Error("wrong type");return A}(r(pt)))));(function(A,dt){At(dt,!1);const kt=D(),bt=D();let W=Y(dt,"instruction",8),Xt=D(void 0);function oe(){_(Xt,W().requestId)}function se(B){const et=B.split(`
`);for(let Z=et.length-1;Z>=0;Z--)if(et[Z].trim().length>0)return et.slice(0,Z+1).join(`
`);return""}J(()=>P(W()),()=>{_(kt,se(W().selectedText))}),J(()=>P(W()),()=>{_(bt,se(W().modifiedText))}),me(),Rt();var ce=wi(),ue=h(ce);nn(ue,{get occuredAt(){return P(W()),x(()=>W().occuredAt)},get requestID(){return P(W()),x(()=>W().requestId)},get pathName(){return P(W()),x(()=>W().pathName)},get repoRoot(){return P(W()),x(()=>W().repoRoot)},get prompt(){return P(W()),x(()=>W().prompt)}});var j=S(ue,2),tt=B=>{var et=gi();N(B,et)},ut=(B,et)=>{var Z=H=>{var m=pi(),G=S(h(m));he(G,{get options(){return _e},get text(){return r(kt)},get pathName(){return P(W()),x(()=>W().pathName)}});var I=S(G,2),K=nt=>{var ot=fi(),_t=S(Dt(ot));he(_t,{get options(){return _e},get text(){return r(bt)},get pathName(){return P(W()),x(()=>W().pathName)}}),N(nt,ot)},Nt=nt=>{var ot=vi();N(nt,ot)};X(I,nt=>{r(kt)!==r(bt)?nt(K):nt(Nt,!1)}),N(H,m)},$t=H=>{var m=bi();Pt("keyup",m,oe),Pt("click",m,oe),N(H,m)};X(B,H=>{P(W()),r(Xt),x(()=>W().userRequested||r(Xt)===W().requestId)?H(Z):H($t,!1)},et)};X(j,B=>{P(W()),x(()=>W().selectedText===W().modifiedText)?B(tt):B(ut,!1)}),N(A,ce),Et()})(Lt,{get instruction(){return r(Bt)}});var Gt=S(St,2),Qt=A=>{var dt=xi();N(A,dt)};X(Gt,A=>{P(r(xt)),r(a),x(()=>r(xt)<r(a).length-1)&&A(Qt)}),N(at,Ct)}),N(q,F)};X(V,q=>{r(a),x(()=>r(a).length>0)&&q(lt)});var rt=S(V,2),It=h(rt),Yt=q=>{var F=_i(),ct=h(F),at=h(ct);ht(pt=>gt(at,`No ${pt??""} found.`),[()=>(r(v),x(()=>r(v).toLowerCase()))],U),N(q,F)},M=q=>{var F=Pi();Ot(F,7,()=>r(k),ct=>ct.requestId,(ct,at,pt)=>{var xt=Ti(),Ct=Dt(xt),St=h(Ct),Lt=A=>{ye(A,{get completion(){return r(at)}})},Bt=(A,dt)=>{var kt=bt=>{ye(bt,{get nextEdit(){return r(at),x(()=>r(at).result)}})};X(A,bt=>{"result"in r(at)&&bt(kt)},dt)};X(St,A=>{"completions"in r(at)?A(Lt):A(Bt,!1)});var Gt=S(Ct,2),Qt=A=>{var dt=Mi();N(A,dt)};X(Gt,A=>{P(r(pt)),r(k),x(()=>r(pt)<r(k).length-1)&&A(Qt)}),N(ct,xt)}),N(q,F)};X(It,q=>{r(k),x(()=>r(k).length===0)?q(Yt):q(M,!1)}),N(z,O)};X(b,z=>{r(w),x(()=>!r(w).length)?z(f):z(E,!1)}),N(T,g)},$$slots:{default:!0}}),Et()},{target:document.getElementById("app")});
