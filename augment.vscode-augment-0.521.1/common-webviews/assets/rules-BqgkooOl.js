import{A as K,C as ne,m as b,X as L,Y as f,K as D,_ as e,$ as ce,D as P,J as F,t as g,M,T as N,V as S,b as n,F as de,G as pe,H as Q,L as ue,I as U,ak as ve,P as me,al as fe,a2 as ge,w as he,a4 as $e,az as ye}from"./SpinnerAugment-AffdR7--.js";import"./design-system-init-BpU1o6o4.js";import{c as w,W as A}from"./IconButtonAugment-DVt24OaC.js";import{B as be}from"./ButtonAugment-K-zrKZyw.js";import{O as we}from"./OpenFileButton-DLmRCR9z.js";import{C as Re,R as ze,E as Ee,T as Me,a as G}from"./index-CnLsnTY6.js";import{M as W,R as X}from"./message-broker-Bv_1VsFe.js";import{M as Fe}from"./MarkdownEditor-eRXt2w4J.js";import{R as ke}from"./RulesModeSelector-DVF13ARD.js";import{C as Ce}from"./chevron-left-DyB-JMbr.js";import{T as Te}from"./CardAugment-CB88N7dm.js";import{l as xe}from"./chevron-down-BBAM6A1q.js";import"./chat-model-context-LQjgzAXk.js";import"./index-C4gKbsWy.js";import"./index-C5DcjNTh.js";import"./remote-agents-client-DXcDoFHp.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-jvQuAtrB.js";import"./TextAreaAugment-BXNDUf24.js";import"./BaseTextInput-IcL3sG2L.js";import"./async-messaging-DXXiLgz5.js";import"./focusTrapStack-CB_5BS9R.js";import"./isObjectLike-D6uePTe3.js";var Le=F('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),De=F('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),Ne=F("<div>Loading...</div>"),Se=F('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ye(function(Y,_){K(_,!1);const[V,j]=ge(),O=()=>$e(H,"$rule",V),B=new W(w),H=he(null),q={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===A.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=G.getDescriptionFrontmatterKey(a),R=G.getRuleTypeFromContent(a),r=G.extractContent(a);H.set({path:t.data.pathName,content:r,type:R,description:o})}}return!0}};ve(()=>{B.registerConsumer(q),w.postMessage({type:A.rulesLoaded})}),P();var I=Se();me("message",fe,function(...s){var t;(t=B.onMessageFromExtension)==null||t.apply(this,s)});var Z=g(I),ee=s=>{(function(t,h){K(h,!1);const a=b(),o=b(),R=b();let r=ne(h,"rule",12),$=b(r().content),i=b(r().description);const J=new W(w),se=new Re,ae=new Ee(w,J,se),re=new ze(J),k=async(l,p)=>{r({...r(),type:l,description:p||e(i)}),p!==void 0&&f(i,p);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:p||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=xe.debounce(k,500),ie=()=>{w.postMessage({type:A.openSettingsPage,data:"guidelines"})};L(()=>D(r()),()=>{f(a,r().path)}),L(()=>D(r()),()=>{f(o,r().type)}),L(()=>(e(a),e(o),e($),e(i)),()=>{f(R,{path:e(a),type:e(o),content:e($),description:e(i)})}),ce(),P(),Fe(t,{saveFunction:()=>k(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,p)=>{var c=de(),z=pe(c),C=u=>{var y=Le(),T=g(y),E=g(T);N(E,{size:1,class:"c-field-label",children:(v,m)=>{var x=S("Description");n(v,x)},$$slots:{default:!0}});var d=M(E,2);Me(d,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(v){f(i,v)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(u,y)};Q(z,u=>{e(o),D(X),ue(()=>e(o)===X.AGENT_REQUESTED)&&u(C)}),n(l,c)},$$slots:{default:!0,header:(l,p)=>{var c=De(),z=g(c),C=g(z),u=g(C);Te(u,{content:"Navigate back to all Rules & Guidelines",children:(d,v)=>{be(d,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(m,x)=>{Ce(m,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=M(u,2);N(y,{size:1,class:"c-field-label",children:(d,v)=>{var m=S("Trigger:");n(d,m)},$$slots:{default:!0}});var T=M(y,2);ke(T,{onSave:k,get rule(){return e(R)}});var E=M(z,2);we(E,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(d,v)=>{N(d,{slot:"text",size:1,children:(m,x)=>{var le=S("Open file");n(m,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),U()})(s,{get rule(){return O()}})},te=s=>{var t=Ne();n(s,t)};Q(Z,s=>{O()!==null?s(ee):s(te,!1)}),n(Y,I),U(),j()},{target:document.getElementById("app")});
