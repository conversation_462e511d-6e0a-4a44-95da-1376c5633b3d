import{ag as W,an as D,E as G,ao as H,ap as I,O as J,L as K,aq as N,ar as Q,as as R,q as V,at as b,h as O,g as U,au as X,av as Y,aw as Z}from"./SpinnerAugment-AffdR7--.js";const g={tick:a=>requestAnimationFrame(a),now:()=>performance.now(),tasks:new Set};function P(){const a=g.now();g.tasks.forEach(t=>{t.c(a)||(g.tasks.delete(t),t.f())}),g.tasks.size!==0&&g.tick(P)}function k(a,t){Q(()=>{a.dispatchEvent(new CustomEvent(t))})}function j(a){if(a==="float")return"cssFloat";if(a==="offset")return"cssOffset";if(a.startsWith("--"))return a;const t=a.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(n=>n[0].toUpperCase()+n.slice(1)).join("")}function A(a){const t={},n=a.split(";");for(const o of n){const[d,r]=o.split(":");if(!d||r===void 0)break;t[j(d.trim())]=r.trim()}return t}const z=a=>a;function rt(a,t,n,o){var d,r,i,u=!!(a&Y),$=!!(a&Z),l=!!(a&N),s=u&&$?"both":u?"in":"out",h=t.inert,m=t.style.overflow;function y(){var f=X,x=W;O(null),U(null);try{return d??(d=n()(t,(o==null?void 0:o())??{},{direction:s}))}finally{O(f),U(x)}}var v={is_global:l,in(){var f;if(t.inert=h,!u)return i==null||i.abort(),void((f=i==null?void 0:i.reset)==null?void 0:f.call(i));$||(r==null||r.abort()),k(t,"introstart"),r=C(t,y(),i,1,()=>{k(t,"introend"),r==null||r.abort(),r=d=void 0,t.style.overflow=m})},out(f){if(!$)return f==null||f(),void(d=void 0);t.inert=!0,k(t,"outrostart"),i=C(t,y(),r,0,()=>{k(t,"outroend"),f==null||f()})},stop:()=>{r==null||r.abort(),i==null||i.abort()}},c=W;if((c.transitions??(c.transitions=[])).push(v),u&&D){var p=l;if(!p){for(var e=c.parent;e&&e.f&G;)for(;(e=e.parent)&&!(e.f&H););p=!e||!!(e.f&I)}p&&J(()=>{K(()=>v.in())})}}function C(a,t,n,o,d){var r=o===1;if(R(t)){var i,u=!1;return V(()=>{if(!u){var p=t({direction:r?"in":"out"});i=C(a,p,n,o,d)}}),{abort:()=>{u=!0,i==null||i.abort()},deactivate:()=>i.deactivate(),reset:()=>i.reset(),t:()=>i.t()}}if(n==null||n.deactivate(),!(t!=null&&t.duration))return d(),{abort:b,deactivate:b,reset:b,t:()=>o};const{delay:$=0,css:l,tick:s,easing:h=z}=t;var m=[];if(r&&n===void 0&&(s&&s(0,1),l)){var y=A(l(0,1));m.push(y,y)}var v=()=>1-o,c=a.animate(m,{duration:$,fill:"forwards"});return c.onfinish=()=>{c.cancel();var p=(n==null?void 0:n.t())??1-o;n==null||n.abort();var e=o-p,f=t.duration*Math.abs(e),x=[];if(f>0){var S=!1;if(l)for(var E=Math.ceil(f/(1e3/60)),F=0;F<=E;F+=1){var q=p+e*h(F/E),M=A(l(q,1-q));x.push(M),S||(S=M.overflow==="hidden")}S&&(a.style.overflow="hidden"),v=()=>{var w=c.currentTime;return p+e*h(w/f)},s&&function(w){let _;g.tasks.size===0&&g.tick(P),new Promise(B=>{g.tasks.add(_={c:w,f:B})})}(()=>{if(c.playState!=="running")return!1;var w=v();return s(w,1-w),!0})}(c=a.animate(x,{duration:f,fill:"forwards"})).onfinish=()=>{v=()=>o,s==null||s(o,1-o),d()}},{abort:()=>{c&&(c.cancel(),c.effect=null,c.onfinish=b)},deactivate:()=>{d=b},reset:()=>{o===0&&(s==null||s(1,0))},t:()=>v()}}const tt=a=>a;function T(a){const t=a-1;return t*t*t+1}function L(a){const t=typeof a=="string"&&a.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[a,"px"]}function it(a,{delay:t=0,duration:n=400,easing:o=tt}={}){const d=+getComputedStyle(a).opacity;return{delay:t,duration:n,easing:o,css:r=>"opacity: "+r*d}}function nt(a,{delay:t=0,duration:n=400,easing:o=T,x:d=0,y:r=0,opacity:i=0}={}){const u=getComputedStyle(a),$=+u.opacity,l=u.transform==="none"?"":u.transform,s=$*(1-i),[h,m]=L(d),[y,v]=L(r);return{delay:t,duration:n,easing:o,css:(c,p)=>`
			transform: ${l} translate(${(1-c)*h}${m}, ${(1-c)*y}${v});
			opacity: ${$-s*p}`}}function et(a,{delay:t=0,duration:n=400,easing:o=T,axis:d="y"}={}){const r=getComputedStyle(a),i=+r.opacity,u=d==="y"?"height":"width",$=parseFloat(r[u]),l=d==="y"?["top","bottom"]:["left","right"],s=l.map(e=>`${e[0].toUpperCase()}${e.slice(1)}`),h=parseFloat(r[`padding${s[0]}`]),m=parseFloat(r[`padding${s[1]}`]),y=parseFloat(r[`margin${s[0]}`]),v=parseFloat(r[`margin${s[1]}`]),c=parseFloat(r[`border${s[0]}Width`]),p=parseFloat(r[`border${s[1]}Width`]);return{delay:t,duration:n,easing:o,css:e=>`overflow: hidden;opacity: ${Math.min(20*e,1)*i};${u}: ${e*$}px;padding-${l[0]}: ${e*h}px;padding-${l[1]}: ${e*m}px;margin-${l[0]}: ${e*y}px;margin-${l[1]}: ${e*v}px;border-${l[0]}-width: ${e*c}px;border-${l[1]}-width: ${e*p}px;min-${u}: 0`}}export{it as a,nt as f,et as s,rt as t};
