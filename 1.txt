wang<PERSON><PERSON><PERSON>@MacBook-Pro-5df97d49 mogai521 % grep -r 'showQuickPick' .
./extension/out/extension.js:`)}},QAt=Ae(require("vscode")),pK=class extends ar{constructor(e,t){super("Open Augment SSH Config"),this.featureFlagManager=e,this.sshConfigManager=new $E(t)}static commandID="vscode-augment.openSshConfig";sshConfigManager;type="public";remoteAgentsEnabled(){return rr(this.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion??"")}canRun(){return this.remoteAgentsEnabled()}async run(){try{var e=this.sshConfigManager.fileSystem,t=await e.getAugmentSshConfigPath();await this.sshConfigManager.ensureAugmentConfigExists(),await e.openFile(t)}catch(e){QAt.window.showErrorMessage("Error opening SSH config: "+String(e))}}},tbe=require("vscode"),fK=class extends ar{static commandID="vscode-augment.settings";type="public";run(){tbe.commands.executeCommand("workbench.action.openSettings","@ext:augment.vscode-augment")}},hK=class extends ar{static commandID="vscode-augment.keyboard-shortcuts";type="public";run(){tbe.commands.executeCommand("workbench.action.openGlobalKeybindings","@ext:augment.vscode-augment")}},qAt=require("vscode"),UAt=Ae(require("vscode")),gK=class extends ar{static commandID="vscode-augment.showAccountPage";type="public";async run(){await UAt.env.openExternal(qAt.Uri.parse("https://app.augmentcode.com/account"))}},VAt=require("vscode"),HAt=Ae(require("vscode")),mK=class extends ar{static commandID="vscode-augment.showDocs";type="public";async run(){await HAt.env.openExternal(VAt.Uri.parse("https://docs.augmentcode.com"))}},xu=Ae(require("vscode")),_K=class e extends Oa{constructor(t,r,a,s,i,n,o,l,c=xu.window.createWebviewPanel("history","Augment History",s)){super("history.html",c.webview),this._extensionUri=t,this._config=r,this._apiServer=a,this._recentCompletions=i,this._recentInstructions=n,this._recentNextEditResults=o,this._workTimer=l,this._panel=c,this._panel.iconPath={light:xu.Uri.joinPath(this._extensionUri,"media","panel-icon-light.svg"),dark:xu.Uri.joinPath(this._extensionUri,"media","panel-icon-dark.svg")},this._panel.onDidDispose(()=>{this.dispose()}),this.addDisposables(this._panel,new xu.Disposable(()=>{e.currentPanel=void 0})),this.addDisposable(this._recentInstructions.onNewItems(e=>{this._panel&&this._panel.visible&&this._panel.webview.postMessage({type:"instructions",data:[e]})})),this.addDisposable(this._recentNextEditResults.onNewItems(e=>{this._panel&&this._panel.visible&&this._panel.webview.postMessage({type:"next-edit-suggestions",data:e})})),this.addDisposable(this._recentCompletions.onNewItems(async e=>{await this.sendCompletions()})),this.addDisposable(this._config.onDidChange(async()=>{await this.sendConfig()})),this._panel.webview.onDidReceiveMessage(async e=>{await this._workTimer.runTimed(e.type,async()=>{await this._handleMessage(e)})}),this.loadHTML(t)}static currentPanel;async _postMessage(e){this._panel&&this._panel.visible&&await this._panel.webview.postMessage(e)}async sendCompletions(){await this._postMessage({type:"completions",data:this._getCompletions()})}_getCompletions(){return this._recentCompletions.items.map(e=>({occuredAt:e.occuredAt.toISOString(),requestId:e.requestId,repoRoot:e.repoRoot,pathName:e.pathName,prefix:e.prefix,completions:e.completions.map(e=>({text:e.completionText,skippedSuffix:e.skippedSuffix,suffixReplacementText:e.suffixReplacementText})),suffix:e.suffix}))}async sendConfig(){await this._postMessage({type:"history-config",data:this._config.config})}async _handleMessage(e){if(this._panel&&this._panel.visible)switch(e.type){case"history-loaded":await this._postMessage({type:"history-initialize",data:{config:this._config.config,completionRequests:this._getCompletions(),instructions:this._recentInstructions.items.slice(),nextEdits:this._recentNextEditResults.items.slice()}});break;case"copy-request-id-to-clipboard":await xu.env.clipboard.writeText(e.data),await xu.window.showInformationMessage("Copied request ID to clipboard");break;case"open-file":uc(e.data,this._panel.viewColumn);break;case"completion-rating":{let t=!0;try{await this._apiServer.completionFeedback(e.data)}catch(e){t=!1,xu.window.showErrorMessage("Failed to submit feedback: "+e.message)}finally{this._panel.webview.postMessage({type:"completion-rating-done",data:{success:t,requestId:e.data.requestId}})}break}case"next-edit-rating":{let t=!0;try{await this._apiServer.nextEditFeedback(e.data)}catch(e){t=!1,xu.window.showErrorMessage("Failed to submit feedback: "+e.message)}finally{this._panel.webview.postMessage({type:"next-edit-rating-done",data:{success:t,requestId:e.data.requestId}})}break}}}static createOrShow(t,r,a,s,i,n,o){e.currentPanel?e.currentPanel._panel.reveal(xu.ViewColumn.Beside):e.currentPanel=new e(t,r,a,xu.ViewColumn.Beside,s,i,n,o)}},AK=class extends ar{constructor(e,t,r,a,s,i,n){super(),this._extensionUri=e,this._config=t,this._apiServer=r,this._recentCompletions=a,this._recentInstructions=s,this._recentNextEditResults=i,this._workTimer=n}static commandID="vscode-augment.showHistoryPanel";type="public";run(){_K.createOrShow(this._extensionUri,this._config,this._apiServer,this._recentCompletions,this._recentInstructions,this._recentNextEditResults,this._workTimer)}},Rw=Ae(require("vscode")),bK=class extends ar{constructor(e,t,r){super(),this._extension=e,this._context=t,this._commandManager=r}static commandID="vscode-augment.showAugmentCommands";type="public";async run(){var e=this._context.extension.packageJSON?.contributes?.commands;if(e){var t,r=Object.fromEntries(e.map(({command:e,title:t})=>[e,t]));for(t of this._commandManager.availableCommands){var a=t.title||r[t.commandID];a&&(r[t.commandID]=a)}var s={};if(this._extension.keybindingWatcher)for(var i of Object.keys(r)){var n=this._extension.keybindingWatcher.getKeybindingForCommand(i,!0);n&&(s[i]=""+n)}e=this.getActions(r,s),e=await Rw.window.showQuickPick(e,{title:"Augment Commands"});e&&e.commandID&&Rw.commands.executeCommand(e.commandID)}}getActions(t,e){var r,a,s=[];for({name:r,commands:a}of this._commandManager.availableCommandGroups){var i=a.filter(e=>t[e.commandID]);if(0!==i.length){0<s.length&&s.push({label:r,kind:Rw.QuickPickItemKind.Separator});for(var n of i)n.showInActionPanel&&s.push({commandID:n.commandID,label:t[n.commandID],description:e[n.commandID]})}}return Promise.resolve(s)}},vK=class extends ar{constructor(e,t,r,a,s,i,n,o,l,c){super("Show Remote Agents Panel",!0),this._extensionUri=e,this._apiServer=t,this._workTimer=r,this._globalState=a,this._featureFlagManager=s,this._extension=i,this._configListener=n,this._guidelinesWatcher=o,this._workspaceManager=l,this._extensionContext=c}static commandID="vscode-augment.showRemoteAgentsPanel";type="public";remoteAgentsEnabled(){return rr(this._featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion??"")}run(){var e;this.remoteAgentsEnabled()&&(e={extensionUri:this._extensionUri,apiServer:this._apiServer,workTimer:this._workTimer,globalState:this._globalState,toolConfigStore:this._extension.toolConfigStore,configListener:this._configListener,guidelinesWatcher:this._guidelinesWatcher,workspaceManager:this._workspaceManager,extensionContext:this._extensionContext,featureFlagManager:this._featureFlagManager},x9.createOrShow(e))}canRun(){return this.remoteAgentsEnabled()}},J9=class extends ar{constructor(e,t,r,a,s,i){super("Show Settings Panel",!0),this._extensionUri=e,this._extension=t,this._apiServer=r,this._config=a,this._guidelinesWatcher=s,this._authSessionStore=i}static commandID="vscode-augment.showSettingsPanel";type="public";run(e){var t=vw.createOrShow(this._extensionUri,this._extension,this._apiServer,this._config,this._guidelinesWatcher,this._authSessionStore,e);e&&t.navigateToSection(e)}},Lw=class extends ar{constructor(e){super(),this._chatExtensionEvent=e}static commandID="vscode-augment.startNewChat";type="public";async run(e){await Rm("Start chat command"),this._chatExtensionEvent.fire({type:zl.newThread,mode:e})}},GAt=Ae(require("vscode")),Ow=class extends ar{static commandID="_vscode-augment.statusbarClick";type="private";constructor(){super(void 0,!1)}async run(){await GAt.commands.executeCommand("augment-chat.focus")}},hL=require("vscode"),yK=class e extends kd{constructor(e,t){super(t,()=>this._configListener.config.completions.enableAutomaticCompletions?"Turn Automatic Completions Off":"Turn Automatic Completions On"),this._configListener=e}static commandID="vscode-augment.toggleAutomaticCompletionSetting";static autoCompletionsConfigKey="completions.enableAutomaticCompletions";type="public";run(){let t=hL.workspace.getConfiguration("augment"),r=t.inspect(e.autoCompletionsConfigKey),a=hL.ConfigurationTarget.Global;void 0!==r?.workspaceValue&&(a=hL.ConfigurationTarget.Workspace),t.update(e.autoCompletionsConfigKey,!this._configListener.config.completions.enableAutomaticCompletions,a)}canRun(){return super.canRun()}},Swt=Ae(Cwt()),xwt=Ae(require("fs")),kwt=Ae(require("os")),bX=Ae(require("path")),Qg=Ae(require("vscode")),wye=Ie("ZipAugmentUserAssetsCommand"),AX=class extends ar{constructor(e){super("Zip Augment User Assets"),this._extensionContext=e}static commandID="vscode-augment.zipAugmentUserAssets";type="public";async run(){try{var e=br((this._extensionContext.storageUri??this._extensionContext.globalStorageUri).fsPath,"augment-user-assets"),t=Qg.Uri.file(e);try{await Qg.workspace.fs.stat(t)}catch{return void Qg.window.showErrorMessage("Augment user assets directory does not exist: "+e)}var r=`augment-user-assets-${(new Date).toISOString().replace(/[:.]/g,"-").slice(0,19)}.zip`,a=kwt.tmpdir(),s=bX.join(a,r),i=await this.createZipArchive(e,s),n=`Augment user assets zipfile saved to "${i}". Path copied to clipboard.`;Qg.window.showInformationMessage(n),await Qg.env.clipboard.writeText(i),wye.info(`Successfully zipped augment-user-assets to: ${i}, source: `+e)}catch(e){Qg.window.showErrorMessage("Error creating augment user assets archive: "+String(e))}}async createZipArchive(s,i){return new Promise((e,t)=>{let r=xwt.createWriteStream(i),a=(0,Swt.default)("zip",{zlib:{level:9}});r.on("close",()=>{wye.info(`Archive created successfully: ${i} (${a.pointer()} total bytes)`),e(i)}),a.on("warning",e=>{"ENOENT"===e.code?wye.warn("Archive warning: "+e.message):t(e)}),a.on("error",e=>{t(new Error("Failed to create archive: "+e.message))}),a.pipe(r),a.directory(s,bX.basename(s)),a.finalize()})}};function Iwt(e,t,r,a,s,i,n,o,l,c,p,u,d,h){var f=new WV(r);return f.registerGroup("",[new Y9(a,s,Y9.signInCommandID,"$(sign-in) Sign In"),new Za,new sK(u,r,t)]),f.registerGroup("Completions",[new uK(t,u),new yK(r,u)]),f.registerGroup("Code Instruction",[new cw(t,e.extensionUri,i,t.guidelinesWatcher,u)]),f.registerGroup("Chat",[new U_(t,r,p,u),new Cw(t,r,p,u),new Lw(p),new Sw(t,r,p,u),new xw(t,r,p,u),new ej(p,d,h),new Aw(t,i,u),new J9(e.extensionUri,t,i,r,t.guidelinesWatcher,a)]),f.registerGroup("Next Edit Suggestions",[new z0(t,r,u),new M0(t,r,u),new P9(t,r,u),new $9(t,r,u),new kg(t,r,u),new q9(t,r,u),new Q9(t,r,u),new Wd(t,r,u),new Ij(t,r,u),new Wj(t,r,u),new Tj(t,r,u),new H_(t,r,u),new aL(t,r,u),new Yj(t,r,u),new Fj(t,r,u),new Nj(t,r,u,d),new eK(t,r),new Dj(t,r,u),new Rj(t,r,u),new Lj(t,r,u),new Lm(t,r,u),new Oj(t,r,u),new Ig(t,r,u),new zj(t,r,u),new Bj(t,r,u),new Cu(t,r,u),new kw(t,r,u)]),f.registerGroup("Debug",[new aK(t,r),new dK(t,r),new G9(t.featureFlagManager),new nK(t.featureFlagManager),new xj(t,r)]),f.registerGroup("Remote Agents",[new pK(t.featureFlagManager,d),new vK(e.extensionUri,i,t.workTimer,d,t.featureFlagManager,t,r,t.guidelinesWatcher,t.workspaceManager,e)]),f.registerGroup("",[new fK,new hK,new iK(u,r,t),new mK,new gK,new AK(e.extensionUri,r,i,n,o,l,t.workTimer),new tK(i),new Y9(a,s,Y9.signOutCommandID,"$(sign-out) Sign Out"),new Pj(t,r,u),new AX(e)]),f.register([new bK(t,e,f),new Mj(t,r,u),new _w(t.featureFlagManager,c),new lK(t,i),new VG(t,e.extensionUri,i),new lw,new uw,new dw,new pw,new fw,new hw,new Ow]),f}var vX=class e{constructor(t,r,a,s,i=e.chunkSize){this._apiServer=t,this._completionTimeoutMs=r,this._completionParams={prefixSize:a,suffixSize:s,chunkSize:i}}static chunkSize=1024;_completionTimeoutMs;_completionParams;createRequestId(){return this._apiServer.createRequestId()}get completionParams(){return this._completionParams}async complete(t,r,a,s,i,n,o,l,c,p,u,d,h,f){t=await this._apiServer.complete(t,r,a,s,i,n,o,l,c,p,u,d,h,f);return void 0!==t.completionTimeoutMs&&(this._completionTimeoutMs=t.completionTimeoutMs),void 0!==t.suggestedPrefixCharCount&&(this._completionParams.prefixSize=t.suggestedPrefixCharCount),void 0!==t.suggestedSuffixCharCount&&(this._completionParams.suffixSize=t.suggestedSuffixCharCount),void 0!==t.suggestedPrefixCharCount&&(this._completionParams.chunkSize=e.chunkSize),t}},Rwt=Ae(Mc()),G0=Ae(require("vscode")),jm=class{constructor(e,t,r,a){this.completionText=e,this.suffixReplacementText=t,this.skippedSuffix=r,this.range=a}toString(){return`text: ${this.completionText}
wangjunyao@MacBook-Pro-5df97d49 mogai521 %